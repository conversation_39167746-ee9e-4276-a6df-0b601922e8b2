﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q,r,s,t,u],v,_(w,x,y,z,g,A,B,_(),C,[],D,_(E,F,G,H,I,_(J,K,L,M),N,null,O,P,P,Q,R,S,null,T,U,V,W,X,Y,Z,U,ba,bb,_(J,K,L,bc),bd,U,be,bf,_(bg,bh,bi,bj,bk,bj,bl,bj,L,_(bm,bn,bo,bn,bp,bn,bq,br)),i,_(j,k,l,k)),bs,_(),bt,_(),bu,_(bv,[_(bw,bx,by,h,bz,bA,y,bB,bC,bB,bD,bE,D,_(i,_(j,bF,l,bF)),bs,_(),bG,_(),bH,bI),_(bw,bJ,by,h,bz,bK,y,bL,bC,bL,bD,bE,D,_(E,bM,i,_(j,bN,l,bN),bO,_(bP,bQ,bR,bS),N,null),bs,_(),bG,_(),bT,_(bU,bV)),_(bw,bW,by,h,bz,bK,y,bL,bC,bL,bD,bE,D,_(E,bM,i,_(j,bN,l,bN),bO,_(bP,bX,bR,bS),N,null),bs,_(),bG,_(),bT,_(bU,bY)),_(bw,bZ,by,h,bz,ca,y,cb,bC,cb,bD,bE,D,_(cc,_(J,K,L,M,cd,ce),i,_(j,cf,l,cg),E,ch,I,_(J,K,L,ci),cj,ck,bd,cl,bO,_(bP,cm,bR,cn)),bs,_(),bG,_(),co,bh),_(bw,cp,by,cq,bz,cr,y,cs,bC,cs,bD,bE,D,_(i,_(j,ct,l,cu),bO,_(bP,cv,bR,cw)),bs,_(),bG,_(),cx,cy,cz,cA,cB,bh,cC,bh,cD,[_(bw,cE,by,cF,y,cG,bv,[_(bw,cH,by,h,bz,ca,cI,cp,cJ,bn,y,cb,bC,cb,bD,bE,D,_(i,_(j,cK,l,cL),E,cM,bO,_(bP,cN,bR,k),Z,U),bs,_(),bG,_(),co,bh),_(bw,cO,by,h,bz,ca,cI,cp,cJ,bn,y,cb,bC,cb,bD,bE,D,_(cP,cQ,i,_(j,cR,l,cS),E,cT,bO,_(bP,cU,bR,cV)),bs,_(),bG,_(),bt,_(cW,_(cX,cY,cZ,[_(cX,h,da,h,db,bh,dc,dd,de,[_(df,dg,cX,dh,di,dj,dk,_(h,_(h,dl)),dm,_(dn,v,dp,bE),dq,dr)])])),ds,bE,co,bh),_(bw,dt,by,h,bz,ca,cI,cp,cJ,bn,y,cb,bC,cb,bD,bE,D,_(cP,cQ,i,_(j,du,l,cS),E,cT,bO,_(bP,cR,bR,cV)),bs,_(),bG,_(),co,bh),_(bw,dv,by,h,bz,bK,cI,cp,cJ,bn,y,bL,bC,bL,bD,bE,D,_(E,bM,i,_(j,dw,l,cS),bO,_(bP,dx,bR,bj),N,null),bs,_(),bG,_(),bT,_(bU,dy)),_(bw,dz,by,h,bz,ca,cI,cp,cJ,bn,y,cb,bC,cb,bD,bE,D,_(cP,cQ,i,_(j,cR,l,cS),E,cT,bO,_(bP,dA,bR,dB)),bs,_(),bG,_(),bt,_(cW,_(cX,cY,cZ,[_(cX,h,da,h,db,bh,dc,dd,de,[_(df,dg,cX,dh,di,dj,dk,_(h,_(h,dl)),dm,_(dn,v,dp,bE),dq,dr)])])),ds,bE,co,bh),_(bw,dC,by,h,bz,ca,cI,cp,cJ,bn,y,cb,bC,cb,bD,bE,D,_(cP,cQ,i,_(j,du,l,cS),E,cT,bO,_(bP,dD,bR,dB)),bs,_(),bG,_(),co,bh),_(bw,dE,by,h,bz,bK,cI,cp,cJ,bn,y,bL,bC,bL,bD,bE,D,_(E,bM,i,_(j,dF,l,dG),bO,_(bP,dx,bR,dH),N,null),bs,_(),bG,_(),bT,_(bU,dI)),_(bw,dJ,by,h,bz,ca,cI,cp,cJ,bn,y,cb,bC,cb,bD,bE,D,_(cc,_(J,K,L,dK,cd,ce),i,_(j,dL,l,dM),E,cT,bO,_(bP,dN,bR,dO)),bs,_(),bG,_(),co,bh),_(bw,dP,by,h,bz,ca,cI,cp,cJ,bn,y,cb,bC,cb,bD,bE,D,_(cc,_(J,K,L,dK,cd,ce),i,_(j,dQ,l,cS),E,cT,bO,_(bP,dN,bR,dR)),bs,_(),bG,_(),co,bh),_(bw,dS,by,h,bz,ca,cI,cp,cJ,bn,y,cb,bC,cb,bD,bE,D,_(cc,_(J,K,L,dT,cd,ce),i,_(j,dU,l,cS),E,cT,bO,_(bP,dV,bR,dW),I,_(J,K,L,M)),bs,_(),bG,_(),bt,_(cW,_(cX,cY,cZ,[_(cX,h,da,h,db,bh,dc,dd,de,[_(df,dg,cX,dh,di,dj,dk,_(h,_(h,dl)),dm,_(dn,v,dp,bE),dq,dr)])])),ds,bE,co,bh),_(bw,dX,by,h,bz,ca,cI,cp,cJ,bn,y,cb,bC,cb,bD,bE,D,_(cc,_(J,K,L,dT,cd,ce),i,_(j,dY,l,cS),E,cT,bO,_(bP,dZ,bR,dO),cj,ea),bs,_(),bG,_(),co,bh),_(bw,eb,by,h,bz,ca,cI,cp,cJ,bn,y,cb,bC,cb,bD,bE,D,_(cc,_(J,K,L,dT,cd,ce),i,_(j,ec,l,cS),E,cT,bO,_(bP,ed,bR,dO),cj,ea),bs,_(),bG,_(),co,bh),_(bw,ee,by,h,bz,ca,cI,cp,cJ,bn,y,cb,bC,cb,bD,bE,D,_(cc,_(J,K,L,dT,cd,ce),i,_(j,dY,l,cS),E,cT,bO,_(bP,dZ,bR,dR),cj,ea),bs,_(),bG,_(),co,bh),_(bw,ef,by,h,bz,ca,cI,cp,cJ,bn,y,cb,bC,cb,bD,bE,D,_(cc,_(J,K,L,dT,cd,ce),i,_(j,ec,l,cS),E,cT,bO,_(bP,ed,bR,dR),cj,ea),bs,_(),bG,_(),co,bh),_(bw,eg,by,h,bz,ca,cI,cp,cJ,bn,y,cb,bC,cb,bD,bE,D,_(cc,_(J,K,L,dK,cd,ce),i,_(j,eh,l,dM),E,cT,bO,_(bP,dN,bR,ei)),bs,_(),bG,_(),co,bh),_(bw,ej,by,h,bz,ca,cI,cp,cJ,bn,y,cb,bC,cb,bD,bE,D,_(cc,_(J,K,L,dT,cd,ce),i,_(j,ce,l,cS),E,cT,bO,_(bP,ek,bR,el),cj,ea),bs,_(),bG,_(),co,bh),_(bw,em,by,h,bz,ca,cI,cp,cJ,bn,y,cb,bC,cb,bD,bE,D,_(cc,_(J,K,L,dT,cd,ce),i,_(j,ce,l,cS),E,cT,bO,_(bP,ek,bR,el),cj,ea),bs,_(),bG,_(),co,bh),_(bw,en,by,h,bz,ca,cI,cp,cJ,bn,y,cb,bC,cb,bD,bE,D,_(cc,_(J,K,L,dT,cd,ce),i,_(j,dY,l,cS),E,cT,bO,_(bP,dZ,bR,eo),cj,ea),bs,_(),bG,_(),co,bh),_(bw,ep,by,h,bz,ca,cI,cp,cJ,bn,y,cb,bC,cb,bD,bE,D,_(cc,_(J,K,L,dT,cd,ce),i,_(j,ec,l,cS),E,cT,bO,_(bP,ed,bR,eo),cj,ea),bs,_(),bG,_(),co,bh),_(bw,eq,by,h,bz,ca,cI,cp,cJ,bn,y,cb,bC,cb,bD,bE,D,_(cc,_(J,K,L,dK,cd,ce),i,_(j,dQ,l,cS),E,cT,bO,_(bP,dN,bR,eo)),bs,_(),bG,_(),co,bh),_(bw,er,by,h,bz,ca,cI,cp,cJ,bn,y,cb,bC,cb,bD,bE,D,_(cc,_(J,K,L,dK,cd,ce),i,_(j,eh,l,dM),E,cT,bO,_(bP,dN,bR,es)),bs,_(),bG,_(),co,bh),_(bw,et,by,h,bz,ca,cI,cp,cJ,bn,y,cb,bC,cb,bD,bE,D,_(cc,_(J,K,L,dK,cd,ce),i,_(j,dZ,l,dM),E,cT,bO,_(bP,dN,bR,eu)),bs,_(),bG,_(),co,bh)],D,_(I,_(J,K,L,ev),N,null,O,P,P,Q,R,Z,U,ba,bb,_(J,K,L,bc),bd,U,be,bf,_(bg,bh,bi,bj,bk,bj,bl,bj,L,_(bm,bn,bo,bn,bp,bn,bq,br))),bs,_())])])),ew,_(),ex,_(ey,_(ez,eA),eB,_(ez,eC),eD,_(ez,eE),eF,_(ez,eG),eH,_(ez,eI),eJ,_(ez,eK),eL,_(ez,eM),eN,_(ez,eO),eP,_(ez,eQ),eR,_(ez,eS),eT,_(ez,eU),eV,_(ez,eW),eX,_(ez,eY),eZ,_(ez,fa),fb,_(ez,fc),fd,_(ez,fe),ff,_(ez,fg),fh,_(ez,fi),fj,_(ez,fk),fl,_(ez,fm),fn,_(ez,fo),fp,_(ez,fq),fr,_(ez,fs),ft,_(ez,fu),fv,_(ez,fw),fx,_(ez,fy),fz,_(ez,fA)));}; 
var b="url",c="消息中心2.html",d="generationDate",e=new Date(1747988905292.9),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="currentTime",s="huanmanxielou",t="Checkbox_2",u="Checkbox_3",v="page",w="packageId",x="********************************",y="type",z="Axure:Page",A="消息中心2",B="notes",C="annotations",D="style",E="baseStyle",F="627587b6038d43cca051c114ac41ad32",G="pageAlignment",H="center",I="fill",J="fillType",K="solid",L="color",M=0xFFFFFFFF,N="image",O="imageAlignment",P="near",Q="imageRepeat",R="auto",S="favicon",T="sketchFactor",U="0",V="colorStyle",W="appliedColor",X="fontName",Y="Applied Font",Z="borderWidth",ba="borderVisibility",bb="borderFill",bc=0xFF797979,bd="cornerRadius",be="cornerVisibility",bf="outerShadow",bg="on",bh=false,bi="offsetX",bj=5,bk="offsetY",bl="blurRadius",bm="r",bn=0,bo="g",bp="b",bq="a",br=0.349019607843137,bs="adaptiveStyles",bt="interactionMap",bu="diagram",bv="objects",bw="id",bx="d303dc7755b24e7b8d980cd09a48c067",by="label",bz="friendlyType",bA="母版",bB="referenceDiagramObject",bC="styleType",bD="visible",bE=true,bF=10,bG="imageOverrides",bH="masterId",bI="ccdaf2f6c10440e0bf90f33b1953e85d",bJ="9f6e0d206bba4ba9bc2026ac165e236d",bK="图片 ",bL="imageBox",bM="********************************",bN=32,bO="location",bP="x",bQ=1347,bR="y",bS=7,bT="images",bU="normal~",bV="images/审批通知模板/u144.png",bW="70d90e29271946309ffc3565c4ad37e2",bX=1420,bY="images/审批通知模板/u145.png",bZ="960a3e8d98e94d9bbaea37cdbbb6e2cc",ca="矩形",cb="vectorShape",cc="foreGroundFill",cd="opacity",ce=1,cf=28,cg=18,ch="f064136b413b4b24888e0a27c4f1cd6f",ci=0xFFFF3B30,cj="fontSize",ck="12px",cl="10",cm=1369,cn=2,co="generateCompound",cp="d402dcbae52e4d67b32f95e8bb0f5091",cq="消息提醒",cr="动态面板",cs="dynamicPanel",ct=498,cu=512,cv=1052,cw=100,cx="percentWidth",cy=1,cz="scrollbars",cA="verticalAsNeeded",cB="fitToContent",cC="propagate",cD="diagrams",cE="5285cae4d4e7433c873502a2239cfcf5",cF="消息展开",cG="Axure:PanelDiagram",cH="ea3cc732c7914798a43774fc23177377",cI="parentDynamicPanel",cJ="panelIndex",cK=300,cL=170,cM="033e195fe17b4b8482606377675dd19a",cN=4,cO="42f10e2e958f45c8b964e033a16b8435",cP="fontWeight",cQ="700",cR=47,cS=25,cT="2285372321d148ec80932747449c36c9",cU=154,cV=8,cW="onClick",cX="description",cY="Click时 ",cZ="cases",da="conditionString",db="isNewIfGroup",dc="caseColorHex",dd="9D33FA",de="actions",df="action",dg="linkWindow",dh="打开&nbsp; 在 当前窗口",di="displayName",dj="打开链接",dk="actionInfoDescriptions",dl="打开  在 当前窗口",dm="target",dn="targetType",dp="includeVariables",dq="linkType",dr="current",ds="tabbable",dt="1beff53b71694243a0cf9577cafc3af0",du=102,dv="638df517c97d46249764b209c7dc5de6",dw=26,dx=21,dy="images/审批通知模板/u150.png",dz="b37f0858a4fb40db968652137c7f54d0",dA=147,dB=212,dC="d9538502766048a48f18952538926301",dD=42,dE="25bd0a3948ed42eca8020bc886b21a40",dF=13,dG=20,dH=217,dI="images/审批通知模板/u154.png",dJ="d05fa6d5c61546269777dfeec2212eb9",dK=0xFF000000,dL=306,dM=50,dN=55,dO=35,dP="0d39821e7f6c49bd9d8ba5d8ecf9a944",dQ=312,dR=95,dS="4769e54f104c475599120fbc21bd3579",dT=0xFF0000FF,dU=98,dV=345,dW=429,dX="b327550a69f64362ae8a4c205b48be91",dY=22,dZ=388,ea="11px",eb="f83c55a71761489aade8bf649f25f00d",ec=33,ed=426,ee="22e06156133941cda6cb225398a70336",ef="99bf0e3bf5e54a7eb195c89b3af4587f",eg="d77b236df06b41ac9842eb47ac48b8fe",eh=404,ei=241,ej="82ccfe2daeba4df09afcb170f09fe51f",ek=357,el=257,em="6989a22fe19a4d548464cb52be491d44",en="03aa4a91dd0b4520a7ae2042b5250833",eo=145,ep="499797be3c3b4feb9f4dbe8c73d9b8af",eq="bc0a9a2e6a324e84b7642c962440c16d",er="462d77a70ae54cb6819292f04663028f",es=289,et="a4af3b6eebd446f4ad229008ca0ede27",eu=343,ev=0xFFFFFF,ew="masters",ex="objectPaths",ey="d303dc7755b24e7b8d980cd09a48c067",ez="scriptId",eA="u3445",eB="9f6e0d206bba4ba9bc2026ac165e236d",eC="u3446",eD="70d90e29271946309ffc3565c4ad37e2",eE="u3447",eF="960a3e8d98e94d9bbaea37cdbbb6e2cc",eG="u3448",eH="d402dcbae52e4d67b32f95e8bb0f5091",eI="u3449",eJ="ea3cc732c7914798a43774fc23177377",eK="u3450",eL="42f10e2e958f45c8b964e033a16b8435",eM="u3451",eN="1beff53b71694243a0cf9577cafc3af0",eO="u3452",eP="638df517c97d46249764b209c7dc5de6",eQ="u3453",eR="b37f0858a4fb40db968652137c7f54d0",eS="u3454",eT="d9538502766048a48f18952538926301",eU="u3455",eV="25bd0a3948ed42eca8020bc886b21a40",eW="u3456",eX="d05fa6d5c61546269777dfeec2212eb9",eY="u3457",eZ="0d39821e7f6c49bd9d8ba5d8ecf9a944",fa="u3458",fb="4769e54f104c475599120fbc21bd3579",fc="u3459",fd="b327550a69f64362ae8a4c205b48be91",fe="u3460",ff="f83c55a71761489aade8bf649f25f00d",fg="u3461",fh="22e06156133941cda6cb225398a70336",fi="u3462",fj="99bf0e3bf5e54a7eb195c89b3af4587f",fk="u3463",fl="d77b236df06b41ac9842eb47ac48b8fe",fm="u3464",fn="82ccfe2daeba4df09afcb170f09fe51f",fo="u3465",fp="6989a22fe19a4d548464cb52be491d44",fq="u3466",fr="03aa4a91dd0b4520a7ae2042b5250833",fs="u3467",ft="499797be3c3b4feb9f4dbe8c73d9b8af",fu="u3468",fv="bc0a9a2e6a324e84b7642c962440c16d",fw="u3469",fx="462d77a70ae54cb6819292f04663028f",fy="u3470",fz="a4af3b6eebd446f4ad229008ca0ede27",fA="u3471";
return _creator();
})());