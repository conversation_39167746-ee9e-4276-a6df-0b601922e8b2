﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:1744px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u7803 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u7804_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1248px;
  height:53px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7804 {
  border-width:0px;
  position:absolute;
  left:233px;
  top:862px;
  width:1248px;
  height:53px;
  display:flex;
}
#u7804 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7804_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7805_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u7805 {
  border-width:0px;
  position:absolute;
  left:255px;
  top:876px;
  width:80px;
  height:25px;
  display:flex;
  font-size:16px;
}
#u7805 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7805_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7806_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:135px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u7806 {
  border-width:0px;
  position:absolute;
  left:1256px;
  top:875px;
  width:135px;
  height:25px;
  display:flex;
  font-size:16px;
}
#u7806 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7806_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7807_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:left;
}
#u7807 {
  border-width:0px;
  position:absolute;
  left:339px;
  top:871px;
  width:96px;
  height:35px;
  display:flex;
  text-align:left;
}
#u7807 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7807_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7808_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:15px;
}
#u7808 {
  border-width:0px;
  position:absolute;
  left:415px;
  top:881px;
  width:15px;
  height:15px;
  display:flex;
}
#u7808 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7808_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7809_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u7809 {
  border-width:0px;
  position:absolute;
  left:1385px;
  top:877px;
  width:25px;
  height:25px;
  display:flex;
}
#u7809 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7809_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7810_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(24, 144, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u7810 {
  border-width:0px;
  position:absolute;
  left:1416px;
  top:875px;
  width:28px;
  height:27px;
  display:flex;
  color:#1890FF;
}
#u7810 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7810_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7811_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u7811 {
  border-width:0px;
  position:absolute;
  left:1456px;
  top:878px;
  width:25px;
  height:25px;
  display:flex;
}
#u7811 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7811_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7812_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1300px;
  height:1852px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7812 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:100px;
  width:1300px;
  height:1852px;
  display:flex;
}
#u7812 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7812_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7813_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1299px;
  height:35px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
}
#u7813 {
  border-width:0px;
  position:absolute;
  left:225px;
  top:102px;
  width:1299px;
  height:35px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
}
#u7813 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7813_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7814_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
}
#u7814 {
  border-width:0px;
  position:absolute;
  left:1494px;
  top:112px;
  width:16px;
  height:16px;
  display:flex;
}
#u7814 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7814_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7815_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7815 {
  border-width:0px;
  position:absolute;
  left:260px;
  top:160px;
  width:104px;
  height:25px;
  display:flex;
}
#u7815 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7815_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7816_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1276px;
  height:33px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7816 {
  border-width:0px;
  position:absolute;
  left:226px;
  top:232px;
  width:1276px;
  height:33px;
  display:flex;
}
#u7816 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7816_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7817_input {
  position:absolute;
  left:0px;
  top:0px;
  width:350px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u7817_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:350px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u7817_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:350px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u7817 {
  border-width:0px;
  position:absolute;
  left:364px;
  top:160px;
  width:350px;
  height:25px;
  display:flex;
  color:#AAAAAA;
}
#u7817 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7817_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:350px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u7817.disabled {
}
#u7818_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7818 {
  border-width:0px;
  position:absolute;
  left:772px;
  top:160px;
  width:74px;
  height:25px;
  display:flex;
}
#u7818 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7818_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7819_input {
  position:absolute;
  left:0px;
  top:0px;
  width:347px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u7819_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:347px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u7819_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:347px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u7819 {
  border-width:0px;
  position:absolute;
  left:863px;
  top:160px;
  width:347px;
  height:25px;
  display:flex;
  color:#AAAAAA;
}
#u7819 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7819_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:347px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u7819.disabled {
}
#u7820_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u7820 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:237px;
  width:64px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u7820 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7820_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7821 {
  border-width:0px;
  position:absolute;
  left:459px;
  top:266px;
  width:1065px;
  height:1228px;
}
#u7821_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1065px;
  height:1228px;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7821_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u7822_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1043px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7822 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:1px;
  width:1043px;
  height:37px;
  display:flex;
}
#u7822 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7822_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7823_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1033px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7823 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:38px;
  width:1033px;
  height:37px;
  display:flex;
}
#u7823 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7823_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7824_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u7824 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:46px;
  width:68px;
  height:20px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u7824 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7824_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7825_input {
  position:absolute;
  left:0px;
  top:0px;
  width:1032px;
  height:129px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#555555;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u7825_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:1032px;
  height:129px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#555555;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u7825_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1032px;
  height:129px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u7825 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:114px;
  width:1032px;
  height:129px;
  display:flex;
  color:#555555;
}
#u7825 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7825_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1032px;
  height:129px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u7825.disabled {
}
#u7826_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1034px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7826 {
  border-width:0px;
  position:absolute;
  left:4px;
  top:394px;
  width:1034px;
  height:37px;
  display:flex;
}
#u7826 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7826_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7827_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u7827 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:404px;
  width:112px;
  height:20px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u7827 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7827_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7828_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:402px;
  height:274px;
}
#u7828 {
  border-width:0px;
  position:absolute;
  left:192px;
  top:447px;
  width:402px;
  height:274px;
  display:flex;
}
#u7828 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7828_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7829_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u7829 {
  border-width:0px;
  position:absolute;
  left:793px;
  top:404px;
  width:1px;
  height:25px;
  display:flex;
  color:#1890FF;
}
#u7829 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7829_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7830_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1022px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7830 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:742px;
  width:1022px;
  height:25px;
  display:flex;
}
#u7830 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7830_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7831_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:43px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u7831 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:742px;
  width:43px;
  height:25px;
  display:flex;
  color:#555555;
}
#u7831 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7831_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7832_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1024px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
  text-align:left;
}
#u7832 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:990px;
  width:1024px;
  height:25px;
  display:flex;
  color:#555555;
  text-align:left;
}
#u7832 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7832_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7833_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:2px;
  height:1226px;
}
#u7833 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:-2px;
  width:1px;
  height:1225px;
  display:flex;
  -webkit-transform:rotate(-0.0562212381763651deg);
  -moz-transform:rotate(-0.0562212381763651deg);
  -ms-transform:rotate(-0.0562212381763651deg);
  transform:rotate(-0.0562212381763651deg);
}
#u7833 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7833_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7834_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u7834 {
  border-width:0px;
  position:absolute;
  left:12px;
  top:10px;
  width:72px;
  height:20px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:18px;
}
#u7834 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7834_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7835_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1033px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7835 {
  border-width:0px;
  position:absolute;
  left:5px;
  top:341px;
  width:1033px;
  height:37px;
  display:flex;
}
#u7835 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7835_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7836_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:68px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u7836 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:349px;
  width:68px;
  height:20px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u7836 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7836_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7837 {
  border-width:0px;
  position:absolute;
  left:189px;
  top:850px;
  width:411px;
  height:120px;
}
#u7838_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u7838 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u7838 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7838_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7839_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:30px;
}
#u7839 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:0px;
  width:142px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u7839 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7839_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7840_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:30px;
}
#u7840 {
  border-width:0px;
  position:absolute;
  left:242px;
  top:0px;
  width:169px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u7840 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7840_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7841_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u7841 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:100px;
  height:30px;
  display:flex;
}
#u7841 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7841_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7842_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:30px;
}
#u7842 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:30px;
  width:142px;
  height:30px;
  display:flex;
}
#u7842 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7842_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7843_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:30px;
}
#u7843 {
  border-width:0px;
  position:absolute;
  left:242px;
  top:30px;
  width:169px;
  height:30px;
  display:flex;
}
#u7843 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7843_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7844_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u7844 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:100px;
  height:30px;
  display:flex;
}
#u7844 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7844_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7845_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:30px;
}
#u7845 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:60px;
  width:142px;
  height:30px;
  display:flex;
}
#u7845 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7845_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7846_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:30px;
}
#u7846 {
  border-width:0px;
  position:absolute;
  left:242px;
  top:60px;
  width:169px;
  height:30px;
  display:flex;
}
#u7846 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7846_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7847_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u7847 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:90px;
  width:100px;
  height:30px;
  display:flex;
}
#u7847 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7847_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7848_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:142px;
  height:30px;
}
#u7848 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:90px;
  width:142px;
  height:30px;
  display:flex;
}
#u7848 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7848_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7849_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:30px;
}
#u7849 {
  border-width:0px;
  position:absolute;
  left:242px;
  top:90px;
  width:169px;
  height:30px;
  display:flex;
}
#u7849 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7849_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7850_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1033px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7850 {
  border-width:0px;
  position:absolute;
  left:5px;
  top:774px;
  width:1033px;
  height:37px;
  display:flex;
}
#u7850 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7850_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7851_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:92px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u7851 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:782px;
  width:92px;
  height:20px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u7851 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7851_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7852_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:364px;
  height:37px;
}
#u7852 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:80px;
  width:364px;
  height:37px;
  display:flex;
}
#u7852 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7852_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7853 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u7854_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#3474F0;
}
#u7854 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:402px;
  width:26px;
  height:25px;
  display:flex;
  font-size:13px;
  color:#3474F0;
}
#u7854 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7854_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7855_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#3474F0;
}
#u7855 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:780px;
  width:26px;
  height:25px;
  display:flex;
  font-size:13px;
  color:#3474F0;
}
#u7855 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7855_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7856_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:966px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
}
#u7856 {
  border-width:0px;
  position:absolute;
  left:11px;
  top:140px;
  width:966px;
  height:50px;
  display:flex;
  font-size:13px;
}
#u7856 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7856_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7857 label {
  left:0px;
  width:100%;
}
#u7857_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7857 {
  border-width:0px;
  position:absolute;
  left:77px;
  top:818px;
  width:75px;
  height:25px;
  display:flex;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u7857 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7857_img.selected {
}
#u7857.selected {
}
#u7857_img.disabled {
}
#u7857.disabled {
}
#u7857_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:59px;
  word-wrap:break-word;
  text-transform:none;
}
#u7857_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7858_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Bold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:13px;
}
#u7858 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:818px;
  width:52px;
  height:25px;
  display:flex;
  font-family:'PingFang SC Bold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
  font-size:13px;
}
#u7858 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7858_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7859 label {
  left:0px;
  width:100%;
}
#u7859_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7859 {
  border-width:0px;
  position:absolute;
  left:152px;
  top:818px;
  width:81px;
  height:25px;
  display:flex;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u7859 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7859_img.selected {
}
#u7859.selected {
}
#u7859_img.disabled {
}
#u7859.disabled {
}
#u7859_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:65px;
  word-wrap:break-word;
  text-transform:none;
}
#u7859_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7860 label {
  left:0px;
  width:100%;
}
#u7860_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7860 {
  border-width:0px;
  position:absolute;
  left:233px;
  top:818px;
  width:84px;
  height:25px;
  display:flex;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
#u7860 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7860_img.selected {
}
#u7860.selected {
}
#u7860_img.disabled {
}
#u7860.disabled {
}
#u7860_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:68px;
  word-wrap:break-word;
  text-transform:none;
}
#u7860_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7861_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1026px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
  text-align:left;
}
#u7861 {
  border-width:0px;
  position:absolute;
  left:12px;
  top:1243px;
  width:1026px;
  height:25px;
  display:flex;
  color:#555555;
  text-align:left;
}
#u7861 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7861_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7862_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1035px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7862 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:1027px;
  width:1035px;
  height:37px;
  display:flex;
}
#u7862 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7862_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7863_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:124px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u7863 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:1035px;
  width:124px;
  height:20px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
}
#u7863 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7863_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7864_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#3474F0;
}
#u7864 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:1033px;
  width:26px;
  height:25px;
  display:flex;
  font-size:13px;
  color:#3474F0;
}
#u7864 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7864_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7865 {
  border-width:0px;
  position:absolute;
  left:189px;
  top:1110px;
  width:500px;
  height:90px;
}
#u7866_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:30px;
}
#u7866 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:30px;
  display:flex;
  font-family:'PingFang SC Bold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
}
#u7866 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7866_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7867_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u7867 {
  border-width:0px;
  position:absolute;
  left:83px;
  top:0px;
  width:117px;
  height:30px;
  display:flex;
  font-family:'PingFang SC Bold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
}
#u7867 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7867_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7868_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u7868 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:0px;
  width:100px;
  height:30px;
  display:flex;
  font-family:'PingFang SC Bold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
}
#u7868 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7868_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7869_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u7869 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:0px;
  width:100px;
  height:30px;
  display:flex;
  font-family:'PingFang SC Bold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
}
#u7869 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7869_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7870_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u7870 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:0px;
  width:100px;
  height:30px;
  display:flex;
  font-family:'PingFang SC Bold', 'PingFang SC';
  font-weight:650;
  font-style:normal;
}
#u7870 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7870_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u7871_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:30px;
}
#u7871 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:83px;
  height:30px;
  display:flex;
}
#u7871 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7871_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7872_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u7872 {
  border-width:0px;
  position:absolute;
  left:83px;
  top:30px;
  width:117px;
  height:30px;
  display:flex;
}
#u7872 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7872_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7873_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u7873 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:30px;
  width:100px;
  height:30px;
  display:flex;
}
#u7873 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7873_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7874_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u7874 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:30px;
  width:100px;
  height:30px;
  display:flex;
}
#u7874 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7874_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7875_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u7875 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:30px;
  width:100px;
  height:30px;
  display:flex;
}
#u7875 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7875_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7876_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:30px;
}
#u7876 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:83px;
  height:30px;
  display:flex;
}
#u7876 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7876_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7877_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:117px;
  height:30px;
}
#u7877 {
  border-width:0px;
  position:absolute;
  left:83px;
  top:60px;
  width:117px;
  height:30px;
  display:flex;
}
#u7877 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7877_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7878_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u7878 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:60px;
  width:100px;
  height:30px;
  display:flex;
}
#u7878 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7878_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7879_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u7879 {
  border-width:0px;
  position:absolute;
  left:300px;
  top:60px;
  width:100px;
  height:30px;
  display:flex;
}
#u7879 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7879_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7880_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:30px;
}
#u7880 {
  border-width:0px;
  position:absolute;
  left:400px;
  top:60px;
  width:100px;
  height:30px;
  display:flex;
}
#u7880 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7880_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7881_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#3474F0;
}
#u7881 {
  border-width:0px;
  position:absolute;
  left:972px;
  top:782px;
  width:52px;
  height:25px;
  display:flex;
  font-size:13px;
  color:#3474F0;
}
#u7881 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7881_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7882_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#3474F0;
}
#u7882 {
  border-width:0px;
  position:absolute;
  left:972px;
  top:1033px;
  width:52px;
  height:25px;
  display:flex;
  font-size:13px;
  color:#3474F0;
}
#u7882 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7882_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7883_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#3474F0;
}
#u7883 {
  border-width:0px;
  position:absolute;
  left:946px;
  top:399px;
  width:78px;
  height:25px;
  display:flex;
  font-size:13px;
  color:#3474F0;
}
#u7883 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7883_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7884_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:27px;
}
#u7884 {
  border-width:0px;
  position:absolute;
  left:367px;
  top:85px;
  width:22px;
  height:27px;
  display:flex;
}
#u7884 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7884_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7885_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#3474F0;
}
#u7885 {
  border-width:0px;
  position:absolute;
  left:926px;
  top:13px;
  width:104px;
  height:25px;
  display:flex;
  font-size:13px;
  color:#3474F0;
}
#u7885 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7885_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7886 {
  border-width:0px;
  position:absolute;
  left:843px;
  top:38px;
  width:200px;
  height:170px;
}
#u7886_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:170px;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7886_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u7887_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:209px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(255, 255, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7887 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:209px;
  display:flex;
}
#u7887 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7887_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7888_input {
  position:absolute;
  left:0px;
  top:0px;
  width:163px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u7888_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:163px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u7888_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:163px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#AAAAAA;
}
#u7888 {
  border-width:0px;
  position:absolute;
  left:8px;
  top:0px;
  width:163px;
  height:25px;
  display:flex;
  font-size:10px;
  color:#AAAAAA;
}
#u7888 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7888_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:163px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#AAAAAA;
}
#u7888.disabled {
}
#u7889_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:15px;
}
#u7889 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:3px;
  width:15px;
  height:15px;
  display:flex;
}
#u7889 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7889_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7890 label {
  left:0px;
  width:100%;
}
#u7890_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7890 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:48px;
  width:100px;
  height:25px;
  display:flex;
}
#u7890 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7890_img.selected {
}
#u7890.selected {
}
#u7890_img.disabled {
}
#u7890.disabled {
}
#u7890_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7890_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7891 label {
  left:0px;
  width:100%;
}
#u7891_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7891 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:73px;
  width:100px;
  height:25px;
  display:flex;
}
#u7891 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7891_img.selected {
}
#u7891.selected {
}
#u7891_img.disabled {
}
#u7891.disabled {
}
#u7891_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7891_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7892 label {
  left:0px;
  width:100%;
}
#u7892_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7892 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:23px;
  width:100px;
  height:25px;
  display:flex;
}
#u7892 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7892_img.selected {
}
#u7892.selected {
}
#u7892_img.disabled {
}
#u7892.disabled {
}
#u7892_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7892_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7893 label {
  left:0px;
  width:100%;
}
#u7893_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7893 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:98px;
  width:100px;
  height:25px;
  display:flex;
}
#u7893 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7893_img.selected {
}
#u7893.selected {
}
#u7893_img.disabled {
}
#u7893.disabled {
}
#u7893_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7893_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7894 label {
  left:0px;
  width:100%;
}
#u7894_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7894 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:123px;
  width:100px;
  height:25px;
  display:flex;
}
#u7894 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7894_img.selected {
}
#u7894.selected {
}
#u7894_img.disabled {
}
#u7894.disabled {
}
#u7894_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7894_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7895 label {
  left:0px;
  width:100%;
}
#u7895_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7895 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:148px;
  width:100px;
  height:25px;
  display:flex;
}
#u7895 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7895_img.selected {
}
#u7895.selected {
}
#u7895_img.disabled {
}
#u7895.disabled {
}
#u7895_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7895_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7896 label {
  left:0px;
  width:100%;
}
#u7896_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7896 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:173px;
  width:100px;
  height:25px;
  display:flex;
}
#u7896 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7896_img.selected {
}
#u7896.selected {
}
#u7896_img.disabled {
}
#u7896.disabled {
}
#u7896_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7896_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7897 label {
  left:0px;
  width:100%;
}
#u7897_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7897 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:198px;
  width:100px;
  height:25px;
  display:flex;
}
#u7897 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7897_img.selected {
}
#u7897.selected {
}
#u7897_img.disabled {
}
#u7897.disabled {
}
#u7897_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7897_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7898 label {
  left:0px;
  width:100%;
}
#u7898_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7898 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:223px;
  width:100px;
  height:25px;
  display:flex;
}
#u7898 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7898_img.selected {
}
#u7898.selected {
}
#u7898_img.disabled {
}
#u7898.disabled {
}
#u7898_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7898_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7899 {
  position:absolute;
  left:8px;
  top:249px;
}
#u7899_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:1001px;
  height:50px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7899_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u7900_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:934px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7900 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:934px;
  height:50px;
  display:flex;
}
#u7900 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7900_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7901_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#1890FF;
}
#u7901 {
  border-width:0px;
  position:absolute;
  left:953px;
  top:0px;
  width:48px;
  height:25px;
  display:flex;
  font-size:12px;
  color:#1890FF;
}
#u7901 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7901_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7902_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:13px;
}
#u7902 {
  border-width:0px;
  position:absolute;
  left:58px;
  top:6px;
  width:16px;
  height:13px;
  display:flex;
}
#u7902 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 20px;
  box-sizing:border-box;
  width:100%;
}
#u7902_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7903_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#555555;
}
#u7903 {
  border-width:0px;
  position:absolute;
  left:38px;
  top:274px;
  width:40px;
  height:25px;
  display:flex;
  font-size:10px;
  color:#555555;
}
#u7903 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7903_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7904_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(52, 116, 240, 0.996078431372549);
}
#u7904 {
  border-width:0px;
  position:absolute;
  left:956px;
  top:1064px;
  width:82px;
  height:29px;
  display:flex;
  color:rgba(52, 116, 240, 0.996078431372549);
}
#u7904 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7904_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7905_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u7905 {
  border-width:0px;
  position:absolute;
  left:935px;
  top:1068px;
  width:20px;
  height:20px;
  display:flex;
}
#u7905 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7905_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7906_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(52, 116, 240, 0.996078431372549);
}
#u7906 {
  border-width:0px;
  position:absolute;
  left:956px;
  top:816px;
  width:82px;
  height:29px;
  display:flex;
  color:rgba(52, 116, 240, 0.996078431372549);
}
#u7906 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7906_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7907_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u7907 {
  border-width:0px;
  position:absolute;
  left:935px;
  top:820px;
  width:20px;
  height:20px;
  display:flex;
}
#u7907 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7907_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7908_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:rgba(52, 116, 240, 0.996078431372549);
}
#u7908 {
  border-width:0px;
  position:absolute;
  left:955px;
  top:431px;
  width:82px;
  height:29px;
  display:flex;
  color:rgba(52, 116, 240, 0.996078431372549);
}
#u7908 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7908_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7909_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u7909 {
  border-width:0px;
  position:absolute;
  left:934px;
  top:435px;
  width:20px;
  height:20px;
  display:flex;
}
#u7909 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7909_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7910 {
  border-width:0px;
  position:absolute;
  left:866px;
  top:431px;
  width:171px;
  height:95px;
}
#u7910_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  height:95px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7910_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u7911_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  height:95px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7911 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:171px;
  height:95px;
  display:flex;
}
#u7911 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7911_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7912_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7912 {
  border-width:0px;
  position:absolute;
  left:9px;
  top:4px;
  width:42px;
  height:25px;
  display:flex;
}
#u7912 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7912_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7913_input {
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:22px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u7913_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:22px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u7913_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7913 {
  border-width:0px;
  position:absolute;
  left:51px;
  top:5px;
  width:106px;
  height:22px;
  display:flex;
}
#u7913 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7913_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:22px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7913.disabled {
}
.u7913_input_option {
}
#u7914_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7914 {
  border-width:0px;
  position:absolute;
  left:9px;
  top:36px;
  width:42px;
  height:25px;
  display:flex;
}
#u7914 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u7914_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u7915_input {
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:22px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u7915_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:22px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u7915_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7915 {
  border-width:0px;
  position:absolute;
  left:51px;
  top:37px;
  width:106px;
  height:22px;
  display:flex;
}
#u7915 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7915_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:22px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7915.disabled {
}
.u7915_input_option {
}
#u7916 {
  border-width:0px;
  position:absolute;
  left:226px;
  top:455px;
  width:230px;
  height:396px;
}
#u7916_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:230px;
  height:396px;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7916_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u7917_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:230px;
  height:396px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7917 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:230px;
  height:396px;
  display:flex;
}
#u7917 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7917_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7918 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u7919_input {
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u7919_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u7919_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#AAAAAA;
}
#u7919 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:12px;
  width:228px;
  height:25px;
  display:flex;
  font-size:10px;
  color:#AAAAAA;
}
#u7919 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7919_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:228px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#AAAAAA;
}
#u7919.disabled {
}
#u7920_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:19px;
}
#u7920 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:15px;
  width:18px;
  height:19px;
  display:flex;
}
#u7920 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7920_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7921 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:32px;
  width:48px;
  height:260px;
}
#u7921_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u7922 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:20px;
}
#u7923_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7923 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7923 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7923_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7924 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:20px;
  width:28px;
  height:20px;
}
#u7925_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u7925 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:6px;
  width:9px;
  height:9px;
  display:flex;
  line-height:normal;
}
#u7925 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7925_img.selected {
}
#u7925.selected {
}
#u7925_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7926_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7926 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7926 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7926_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7924_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u7927 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:20px;
  width:28px;
  height:20px;
}
#u7928_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7928 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7928 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7928_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7929 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:40px;
  width:28px;
  height:20px;
}
#u7930_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7930 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7930 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7930_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7931 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:28px;
  height:20px;
}
#u7932_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7932 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7932 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7932_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7933 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:100px;
  width:28px;
  height:20px;
}
#u7934_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u7934 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:6px;
  width:9px;
  height:9px;
  display:flex;
}
#u7934 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7934_img.selected {
}
#u7934.selected {
}
#u7934_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7935_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7935 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7935 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7935_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7933_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u7936 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:20px;
  width:28px;
  height:20px;
}
#u7937_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7937 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7937 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7937_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7938 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:40px;
  width:28px;
  height:20px;
}
#u7939_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7939 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7939 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7939_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7940 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:160px;
  width:28px;
  height:20px;
}
#u7941_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u7941 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:6px;
  width:9px;
  height:9px;
  display:flex;
}
#u7941 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7941_img.selected {
}
#u7941.selected {
}
#u7941_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7942_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7942 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7942 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7942_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7940_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u7943 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:20px;
  width:28px;
  height:20px;
}
#u7944_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7944 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7944 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7944_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7945 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:40px;
  width:28px;
  height:20px;
}
#u7946_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7946 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7946 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7946_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7947 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:220px;
  width:28px;
  height:20px;
}
#u7948_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u7948 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:6px;
  width:9px;
  height:9px;
  display:flex;
}
#u7948 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7948_img.selected {
}
#u7948.selected {
}
#u7948_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7949_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7949 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7949 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7949_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7947_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u7950 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:20px;
  width:28px;
  height:20px;
}
#u7951_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7951 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7951 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7951_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7952 label {
  left:0px;
  width:100%;
}
#u7952_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7952 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:48px;
  width:100px;
  height:25px;
  display:flex;
}
#u7952 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7952_img.selected {
}
#u7952.selected {
}
#u7952_img.disabled {
}
#u7952.disabled {
}
#u7952_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7952_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7953 label {
  left:0px;
  width:100%;
}
#u7953_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7953 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:69px;
  width:130px;
  height:25px;
  display:flex;
  color:rgba(52, 116, 240, 0.996078431372549);
}
#u7953 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7953_img.selected {
}
#u7953.selected {
}
#u7953_img.disabled {
}
#u7953.disabled {
}
#u7953_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:114px;
  word-wrap:break-word;
  text-transform:none;
}
#u7953_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7954 label {
  left:0px;
  width:100%;
}
#u7954_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7954 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:91px;
  width:100px;
  height:25px;
  display:flex;
}
#u7954 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7954_img.selected {
}
#u7954.selected {
}
#u7954_img.disabled {
}
#u7954.disabled {
}
#u7954_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7954_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7955 label {
  left:0px;
  width:100%;
}
#u7955_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7955 {
  border-width:0px;
  position:absolute;
  left:52px;
  top:130px;
  width:100px;
  height:25px;
  display:flex;
}
#u7955 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7955_img.selected {
}
#u7955.selected {
}
#u7955_img.disabled {
}
#u7955.disabled {
}
#u7955_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7955_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7956 label {
  left:0px;
  width:100%;
}
#u7956_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7956 {
  border-width:0px;
  position:absolute;
  left:52px;
  top:190px;
  width:100px;
  height:25px;
  display:flex;
}
#u7956 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7956_img.selected {
}
#u7956.selected {
}
#u7956_img.disabled {
}
#u7956.disabled {
}
#u7956_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7956_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7957 label {
  left:0px;
  width:100%;
}
#u7957_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7957 {
  border-width:0px;
  position:absolute;
  left:52px;
  top:251px;
  width:136px;
  height:25px;
  display:flex;
}
#u7957 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7957_img.selected {
}
#u7957.selected {
}
#u7957_img.disabled {
}
#u7957.disabled {
}
#u7957_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:120px;
  word-wrap:break-word;
  text-transform:none;
}
#u7957_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7958 label {
  left:0px;
  width:100%;
}
#u7958_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7958 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:211px;
  width:100px;
  height:25px;
  display:flex;
}
#u7958 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7958_img.selected {
}
#u7958.selected {
}
#u7958_img.disabled {
}
#u7958.disabled {
}
#u7958_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7958_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7959 label {
  left:0px;
  width:100%;
}
#u7959_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7959 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:232px;
  width:100px;
  height:25px;
  display:flex;
}
#u7959 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7959_img.selected {
}
#u7959.selected {
}
#u7959_img.disabled {
}
#u7959.disabled {
}
#u7959_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7959_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7960 label {
  left:0px;
  width:100%;
}
#u7960_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7960 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:273px;
  width:160px;
  height:25px;
  display:flex;
  color:rgba(52, 116, 240, 0.996078431372549);
}
#u7960 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7960_img.selected {
}
#u7960.selected {
}
#u7960_img.disabled {
}
#u7960.disabled {
}
#u7960_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:144px;
  word-wrap:break-word;
  text-transform:none;
}
#u7960_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7961 label {
  left:0px;
  width:100%;
}
#u7961_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7961 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:170px;
  width:100px;
  height:25px;
  display:flex;
}
#u7961 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7961_img.selected {
}
#u7961.selected {
}
#u7961_img.disabled {
}
#u7961.disabled {
}
#u7961_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7961_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7962 label {
  left:0px;
  width:100%;
}
#u7962_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7962 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:297px;
  width:160px;
  height:25px;
  display:flex;
  color:rgba(52, 116, 240, 0.996078431372549);
}
#u7962 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7962_img.selected {
}
#u7962.selected {
}
#u7962_img.disabled {
}
#u7962.disabled {
}
#u7962_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:144px;
  word-wrap:break-word;
  text-transform:none;
}
#u7962_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7963 label {
  left:0px;
  width:100%;
}
#u7963_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7963 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:151px;
  width:100px;
  height:25px;
  display:flex;
}
#u7963 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7963_img.selected {
}
#u7963.selected {
}
#u7963_img.disabled {
}
#u7963.disabled {
}
#u7963_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7963_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7964 label {
  left:0px;
  width:100%;
}
#u7964_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7964 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:111px;
  width:100px;
  height:25px;
  display:flex;
}
#u7964 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7964_img.selected {
}
#u7964.selected {
}
#u7964_img.disabled {
}
#u7964.disabled {
}
#u7964_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7964_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7916_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:230px;
  height:396px;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u7916_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u7965_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:230px;
  height:543px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7965 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:31px;
  width:230px;
  height:543px;
  display:flex;
}
#u7965 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7965_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7966_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u7966_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u7966_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#AAAAAA;
}
#u7966 {
  border-width:0px;
  position:absolute;
  left:8px;
  top:39px;
  width:200px;
  height:25px;
  display:flex;
  font-size:10px;
  color:#AAAAAA;
}
#u7966 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7966_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#AAAAAA;
}
#u7966.disabled {
}
#u7967_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:19px;
}
#u7967 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:42px;
  width:18px;
  height:19px;
  display:flex;
}
#u7967 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7967_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7968 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:70px;
  width:48px;
  height:240px;
}
#u7968_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u7969 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:20px;
}
#u7970_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7970 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7970 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7970_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7971 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:20px;
  width:28px;
  height:20px;
}
#u7972_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u7972 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:6px;
  width:9px;
  height:9px;
  display:flex;
  line-height:normal;
}
#u7972 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7972_img.selected {
}
#u7972.selected {
}
#u7972_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7973_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7973 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7973 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7973_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7971_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u7974 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:20px;
  width:28px;
  height:20px;
}
#u7975_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7975 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7975 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7975_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7976 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:40px;
  width:28px;
  height:20px;
}
#u7977_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7977 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7977 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7977_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7978 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:28px;
  height:20px;
}
#u7979_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u7979 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:6px;
  width:9px;
  height:9px;
  display:flex;
}
#u7979 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7979_img.selected {
}
#u7979.selected {
}
#u7979_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7980_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7980 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7980 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7980_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7978_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u7981 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:20px;
  width:28px;
  height:20px;
}
#u7982_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7982 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7982 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7982_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7983 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:40px;
  width:28px;
  height:20px;
}
#u7984_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7984 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7984 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7984_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7985 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:140px;
  width:28px;
  height:20px;
}
#u7986_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u7986 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:6px;
  width:9px;
  height:9px;
  display:flex;
}
#u7986 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7986_img.selected {
}
#u7986.selected {
}
#u7986_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7987_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7987 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7987 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7987_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7985_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u7988 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:20px;
  width:28px;
  height:20px;
}
#u7989_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7989 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7989 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7989_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7990 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:40px;
  width:28px;
  height:20px;
}
#u7991_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7991 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7991 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7991_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7992 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:200px;
  width:28px;
  height:20px;
}
#u7993_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u7993 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:6px;
  width:9px;
  height:9px;
  display:flex;
}
#u7993 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u7993_img.selected {
}
#u7993.selected {
}
#u7993_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u7994_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7994 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7994 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7994_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7992_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u7995 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:20px;
  width:28px;
  height:20px;
}
#u7996_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u7996 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u7996 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u7996_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u7997 label {
  left:0px;
  width:100%;
}
#u7997_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7997 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:86px;
  width:100px;
  height:25px;
  display:flex;
}
#u7997 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7997_img.selected {
}
#u7997.selected {
}
#u7997_img.disabled {
}
#u7997.disabled {
}
#u7997_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7997_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7998 label {
  left:0px;
  width:100%;
}
#u7998_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7998 {
  border-width:0px;
  position:absolute;
  left:58px;
  top:111px;
  width:100px;
  height:25px;
  display:flex;
}
#u7998 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7998_img.selected {
}
#u7998.selected {
}
#u7998_img.disabled {
}
#u7998.disabled {
}
#u7998_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7998_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u7999 label {
  left:0px;
  width:100%;
}
#u7999_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u7999 {
  border-width:0px;
  position:absolute;
  left:58px;
  top:131px;
  width:100px;
  height:25px;
  display:flex;
}
#u7999 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u7999_img.selected {
}
#u7999.selected {
}
#u7999_img.disabled {
}
#u7999.disabled {
}
#u7999_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u7999_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8000 label {
  left:0px;
  width:100%;
}
#u8000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8000 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:151px;
  width:100px;
  height:25px;
  display:flex;
}
#u8000 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8000_img.selected {
}
#u8000.selected {
}
#u8000_img.disabled {
}
#u8000.disabled {
}
#u8000_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8000_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8001 label {
  left:0px;
  width:100%;
}
#u8001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8001 {
  border-width:0px;
  position:absolute;
  left:58px;
  top:191px;
  width:100px;
  height:25px;
  display:flex;
}
#u8001 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8001_img.selected {
}
#u8001.selected {
}
#u8001_img.disabled {
}
#u8001.disabled {
}
#u8001_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8001_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8002 label {
  left:0px;
  width:100%;
}
#u8002_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8002 {
  border-width:0px;
  position:absolute;
  left:58px;
  top:169px;
  width:100px;
  height:25px;
  display:flex;
}
#u8002 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8002_img.selected {
}
#u8002.selected {
}
#u8002_img.disabled {
}
#u8002.disabled {
}
#u8002_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8002_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8003 label {
  left:0px;
  width:100%;
}
#u8003_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8003 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:209px;
  width:100px;
  height:25px;
  display:flex;
}
#u8003 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8003_img.selected {
}
#u8003.selected {
}
#u8003_img.disabled {
}
#u8003.disabled {
}
#u8003_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8003_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8004 label {
  left:0px;
  width:100%;
}
#u8004_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8004 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:270px;
  width:100px;
  height:25px;
  display:flex;
}
#u8004 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8004_img.selected {
}
#u8004.selected {
}
#u8004_img.disabled {
}
#u8004.disabled {
}
#u8004_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8004_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8005 label {
  left:0px;
  width:100%;
}
#u8005_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8005 {
  border-width:0px;
  position:absolute;
  left:58px;
  top:229px;
  width:100px;
  height:25px;
  display:flex;
}
#u8005 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8005_img.selected {
}
#u8005.selected {
}
#u8005_img.disabled {
}
#u8005.disabled {
}
#u8005_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8005_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8006 label {
  left:0px;
  width:100%;
}
#u8006_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8006 {
  border-width:0px;
  position:absolute;
  left:58px;
  top:251px;
  width:100px;
  height:25px;
  display:flex;
}
#u8006 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8006_img.selected {
}
#u8006.selected {
}
#u8006_img.disabled {
}
#u8006.disabled {
}
#u8006_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8006_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8007 label {
  left:0px;
  width:100%;
}
#u8007_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8007 {
  border-width:0px;
  position:absolute;
  left:58px;
  top:288px;
  width:100px;
  height:25px;
  display:flex;
}
#u8007 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8007_img.selected {
}
#u8007.selected {
}
#u8007_img.disabled {
}
#u8007.disabled {
}
#u8007_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8007_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8008_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u8008 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:6px;
  width:56px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u8008 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u8008_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u8009_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#1890FF;
}
#u8009 {
  border-width:0px;
  position:absolute;
  left:145px;
  top:6px;
  width:63px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#1890FF;
}
#u8009 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u8009_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u8010 label {
  left:0px;
  width:100%;
}
#u8010_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8010 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:61px;
  width:100px;
  height:25px;
  display:flex;
}
#u8010 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8010_img.selected {
}
#u8010.selected {
}
#u8010_img.disabled {
}
#u8010.disabled {
}
#u8010_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8010_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8011_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:27px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#FFFFFF;
}
#u8011 {
  border-width:0px;
  position:absolute;
  left:1459px;
  top:2068px;
  width:65px;
  height:27px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u8011 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8011_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u8012_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u8012 {
  border-width:0px;
  position:absolute;
  left:1398px;
  top:2068px;
  width:54px;
  height:27px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u8012 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8012_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u8013 {
  border-width:0px;
  position:absolute;
  left:229px;
  top:265px;
  width:230px;
  height:158px;
}
#u8013_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:230px;
  height:158px;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8013_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u8014_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:234px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8014 {
  border-width:0px;
  position:absolute;
  left:-3px;
  top:1px;
  width:234px;
  height:32px;
  display:flex;
}
#u8014 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8014_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8015_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:230px;
  height:127px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8015 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:31px;
  width:230px;
  height:127px;
  display:flex;
}
#u8015 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8015_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8016_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u8016 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:6px;
  width:56px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u8016 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u8016_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u8017 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:40px;
  width:99px;
  height:80px;
}
#u8017_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u8018 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:20px;
}
#u8019_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u8019 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:6px;
  width:9px;
  height:9px;
  display:flex;
  line-height:normal;
}
#u8019 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8019_img.selected {
}
#u8019.selected {
}
#u8019_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8020_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8020 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:57px;
  height:20px;
  display:flex;
}
#u8020 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u8020_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u8018_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u8021 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:20px;
  width:79px;
  height:20px;
  color:rgba(52, 116, 240, 0.996078431372549);
}
#u8022_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8022 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:57px;
  height:20px;
  display:flex;
}
#u8022 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u8022_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u8023 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:40px;
  width:79px;
  height:20px;
}
#u8024_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8024 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:57px;
  height:20px;
  display:flex;
}
#u8024 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u8024_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u8025 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:60px;
  width:79px;
  height:20px;
}
#u8026_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8026 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:57px;
  height:20px;
  display:flex;
}
#u8026 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u8026_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u8027_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:15px;
}
#u8027 {
  border-width:0px;
  position:absolute;
  left:128px;
  top:42px;
  width:32px;
  height:15px;
  display:flex;
}
#u8027 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8027_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8028_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:15px;
}
#u8028 {
  border-width:0px;
  position:absolute;
  left:128px;
  top:63px;
  width:32px;
  height:15px;
  display:flex;
}
#u8028 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8028_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8029 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u8030_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:15px;
}
#u8030 {
  border-width:0px;
  position:absolute;
  left:128px;
  top:84px;
  width:32px;
  height:15px;
  display:flex;
}
#u8030 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8030_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8031_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:14px;
}
#u8031 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:83px;
  width:13px;
  height:14px;
  display:flex;
}
#u8031 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8031_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8032_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:14px;
}
#u8032 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:62px;
  width:13px;
  height:14px;
  display:flex;
}
#u8032 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8032_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8033 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u8034_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:15px;
}
#u8034 {
  border-width:0px;
  position:absolute;
  left:128px;
  top:104px;
  width:32px;
  height:15px;
  display:flex;
}
#u8034 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8034_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8035_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:14px;
}
#u8035 {
  border-width:0px;
  position:absolute;
  left:160px;
  top:103px;
  width:13px;
  height:14px;
  display:flex;
}
#u8035 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8035_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8013_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:230px;
  height:158px;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u8013_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u8036_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:230px;
  height:543px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8036 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:31px;
  width:230px;
  height:543px;
  display:flex;
}
#u8036 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8036_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8037_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u8037_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u8037_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#AAAAAA;
}
#u8037 {
  border-width:0px;
  position:absolute;
  left:8px;
  top:39px;
  width:200px;
  height:25px;
  display:flex;
  font-size:10px;
  color:#AAAAAA;
}
#u8037 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8037_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#AAAAAA;
}
#u8037.disabled {
}
#u8038_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:19px;
}
#u8038 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:42px;
  width:18px;
  height:19px;
  display:flex;
}
#u8038 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8038_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8039 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:70px;
  width:48px;
  height:240px;
}
#u8039_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u8040 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:20px;
}
#u8041_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8041 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u8041 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u8041_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u8042 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:20px;
  width:28px;
  height:20px;
}
#u8043_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u8043 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:6px;
  width:9px;
  height:9px;
  display:flex;
  line-height:normal;
}
#u8043 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8043_img.selected {
}
#u8043.selected {
}
#u8043_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8044_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8044 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u8044 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u8044_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u8042_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u8045 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:20px;
  width:28px;
  height:20px;
}
#u8046_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8046 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u8046 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u8046_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u8047 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:40px;
  width:28px;
  height:20px;
}
#u8048_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8048 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u8048 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u8048_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u8049 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:28px;
  height:20px;
}
#u8050_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u8050 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:6px;
  width:9px;
  height:9px;
  display:flex;
}
#u8050 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8050_img.selected {
}
#u8050.selected {
}
#u8050_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8051_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8051 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u8051 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u8051_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u8049_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u8052 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:20px;
  width:28px;
  height:20px;
}
#u8053_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8053 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u8053 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u8053_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u8054 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:40px;
  width:28px;
  height:20px;
}
#u8055_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8055 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u8055 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u8055_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u8056 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:140px;
  width:28px;
  height:20px;
}
#u8057_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u8057 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:6px;
  width:9px;
  height:9px;
  display:flex;
}
#u8057 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8057_img.selected {
}
#u8057.selected {
}
#u8057_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8058_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8058 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u8058 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u8058_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u8056_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u8059 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:20px;
  width:28px;
  height:20px;
}
#u8060_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8060 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u8060 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u8060_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u8061 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:40px;
  width:28px;
  height:20px;
}
#u8062_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8062 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u8062 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u8062_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u8063 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:200px;
  width:28px;
  height:20px;
}
#u8064_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u8064 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:6px;
  width:9px;
  height:9px;
  display:flex;
}
#u8064 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8064_img.selected {
}
#u8064.selected {
}
#u8064_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8065_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8065 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u8065 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u8065_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u8063_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u8066 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:20px;
  width:28px;
  height:20px;
}
#u8067_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8067 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u8067 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u8067_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u8068 label {
  left:0px;
  width:100%;
}
#u8068_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8068 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:86px;
  width:100px;
  height:25px;
  display:flex;
}
#u8068 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8068_img.selected {
}
#u8068.selected {
}
#u8068_img.disabled {
}
#u8068.disabled {
}
#u8068_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8068_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8069 label {
  left:0px;
  width:100%;
}
#u8069_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8069 {
  border-width:0px;
  position:absolute;
  left:58px;
  top:111px;
  width:100px;
  height:25px;
  display:flex;
}
#u8069 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8069_img.selected {
}
#u8069.selected {
}
#u8069_img.disabled {
}
#u8069.disabled {
}
#u8069_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8069_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8070 label {
  left:0px;
  width:100%;
}
#u8070_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8070 {
  border-width:0px;
  position:absolute;
  left:58px;
  top:131px;
  width:100px;
  height:25px;
  display:flex;
}
#u8070 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8070_img.selected {
}
#u8070.selected {
}
#u8070_img.disabled {
}
#u8070.disabled {
}
#u8070_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8070_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8071 label {
  left:0px;
  width:100%;
}
#u8071_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8071 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:151px;
  width:100px;
  height:25px;
  display:flex;
}
#u8071 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8071_img.selected {
}
#u8071.selected {
}
#u8071_img.disabled {
}
#u8071.disabled {
}
#u8071_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8071_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8072 label {
  left:0px;
  width:100%;
}
#u8072_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8072 {
  border-width:0px;
  position:absolute;
  left:58px;
  top:191px;
  width:100px;
  height:25px;
  display:flex;
}
#u8072 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8072_img.selected {
}
#u8072.selected {
}
#u8072_img.disabled {
}
#u8072.disabled {
}
#u8072_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8072_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8073 label {
  left:0px;
  width:100%;
}
#u8073_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8073 {
  border-width:0px;
  position:absolute;
  left:58px;
  top:169px;
  width:100px;
  height:25px;
  display:flex;
}
#u8073 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8073_img.selected {
}
#u8073.selected {
}
#u8073_img.disabled {
}
#u8073.disabled {
}
#u8073_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8073_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8074 label {
  left:0px;
  width:100%;
}
#u8074_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8074 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:209px;
  width:100px;
  height:25px;
  display:flex;
}
#u8074 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8074_img.selected {
}
#u8074.selected {
}
#u8074_img.disabled {
}
#u8074.disabled {
}
#u8074_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8074_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8075 label {
  left:0px;
  width:100%;
}
#u8075_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8075 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:270px;
  width:100px;
  height:25px;
  display:flex;
}
#u8075 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8075_img.selected {
}
#u8075.selected {
}
#u8075_img.disabled {
}
#u8075.disabled {
}
#u8075_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8075_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8076 label {
  left:0px;
  width:100%;
}
#u8076_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8076 {
  border-width:0px;
  position:absolute;
  left:58px;
  top:229px;
  width:100px;
  height:25px;
  display:flex;
}
#u8076 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8076_img.selected {
}
#u8076.selected {
}
#u8076_img.disabled {
}
#u8076.disabled {
}
#u8076_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8076_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8077 label {
  left:0px;
  width:100%;
}
#u8077_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8077 {
  border-width:0px;
  position:absolute;
  left:58px;
  top:251px;
  width:100px;
  height:25px;
  display:flex;
}
#u8077 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8077_img.selected {
}
#u8077.selected {
}
#u8077_img.disabled {
}
#u8077.disabled {
}
#u8077_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8077_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8078 label {
  left:0px;
  width:100%;
}
#u8078_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8078 {
  border-width:0px;
  position:absolute;
  left:58px;
  top:288px;
  width:100px;
  height:25px;
  display:flex;
}
#u8078 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8078_img.selected {
}
#u8078.selected {
}
#u8078_img.disabled {
}
#u8078.disabled {
}
#u8078_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8078_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8079_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u8079 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:6px;
  width:56px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u8079 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u8079_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u8080_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#1890FF;
}
#u8080 {
  border-width:0px;
  position:absolute;
  left:145px;
  top:6px;
  width:63px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#1890FF;
}
#u8080 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u8080_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u8081 label {
  left:0px;
  width:100%;
}
#u8081_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u8081 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:61px;
  width:100px;
  height:25px;
  display:flex;
}
#u8081 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u8081_img.selected {
}
#u8081.selected {
}
#u8081_img.disabled {
}
#u8081.disabled {
}
#u8081_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u8081_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u8082_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:233px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8082 {
  border-width:0px;
  position:absolute;
  left:226px;
  top:423px;
  width:233px;
  height:32px;
  display:flex;
}
#u8082 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u8082_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u8083_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u8083 {
  border-width:0px;
  position:absolute;
  left:239px;
  top:427px;
  width:56px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u8083 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u8083_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u8084_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:154px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u8084 {
  border-width:0px;
  position:absolute;
  left:1590px;
  top:232px;
  width:154px;
  height:25px;
  display:flex;
}
#u8084 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u8084_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
