# 代码审查自动化工具说明文档

## 1. 概述

本工具是一个自动化代码审查系统，可以从GitLab代码库下载代码文件，使用AI模型进行代码审查，生成审查报告，并通过邮件发送结果。该工具支持Java、C/C++和Go等多种编程语言。

## 2. 文件结构

该工具由以下几个主要Python模块组成：

1. `code_review_orchestrator.py` - 主协调器，负责整个代码审查流程
2. `gitlab_downloader.py` - 从GitLab下载代码文件
3. `ai_reviewer.py` - 使用AI模型进行代码审查
4. `report_generator.py` - 生成Markdown格式的审查报告
5. `email_sender.py` - 发送包含审查报告的邮件
6. `test_code_review.py` - 测试脚本，用于验证各个组件的功能

## 3. 代码逻辑说明

### 3.1 代码审查协调器 (code_review_orchestrator.py)

这是整个系统的主入口点，负责协调整个代码审查流程。主要功能包括：

- 解析命令行参数，获取GitLab、AI模型API和邮件发送的配置信息
- 创建输出目录结构
- 调用GitLab下载器获取代码文件
- 调用AI审查器对每个文件进行审查
- 调用报告生成器生成审查报告
- 调用邮件发送器发送最终报告

主要流程：
1. 解析命令行参数
2. 创建输出目录
3. 将项目ID映射到分支
4. 从GitLab下载文件
5. 使用AI模型审查代码
6. 生成Markdown报告
7. 发送邮件

### 3.2 GitLab下载器 (gitlab_downloader.py)

负责从GitLab代码库下载代码文件。主要功能包括：

- 连接到GitLab服务器
- 获取指定项目和分支的文件列表
- 根据文件扩展名过滤支持的编程语言文件
- 下载文件并保存到本地目录

主要方法：
- `__init__`: 初始化GitLab下载器，设置GitLab URL、访问令牌和输出目录
- `_init_gitlab_client`: 初始化GitLab客户端
- `download_repositories`: 从多个GitLab仓库下载文件
- `download_project_files`: 从特定GitLab项目和分支下载文件
- `_get_file_language`: 根据文件扩展名确定编程语言

### 3.3 AI审查器 (ai_reviewer.py)

负责使用AI模型对代码文件进行审查。主要功能包括：

- 根据文件类型选择适当的提示语
- 将代码发送到AI模型API
- 处理AI模型的响应
- 提取审查结果中的问题、建议和改进代码

主要方法：
- `__init__`: 初始化AI审查器，设置API URL和访问密钥
- `review_file`: 审查单个代码文件
- `_call_ai_api`: 调用AI模型API
- `_process_review_result`: 处理AI模型的响应
- `_extract_issues`: 从文本内容中提取问题
- `_extract_suggestions`: 从文本内容中提取建议
- `_extract_improved_code`: 从文本内容中提取改进后的代码
- `_get_file_language`: 根据文件扩展名确定编程语言

### 3.4 报告生成器 (report_generator.py)

负责生成Markdown格式的审查报告。主要功能包括：

- 为每个文件生成单独的审查报告
- 将所有报告合并为一个综合报告
- 生成目录和导航链接

主要方法：
- `__init__`: 初始化报告生成器，设置输出目录
- `generate_reports`: 为每个文件生成审查报告
- `consolidate_reports`: 将所有报告合并为一个综合报告
- `_generate_file_report`: 为单个文件生成审查报告

### 3.5 邮件发送器 (email_sender.py)

负责发送包含审查报告的邮件。主要功能包括：

- 创建邮件内容
- 附加审查报告
- 通过SMTP服务器发送邮件

主要方法：
- `__init__`: 初始化邮件发送器，设置SMTP服务器信息
- `send_report`: 发送包含审查报告的邮件

## 4. 调用逻辑关系

整个系统的调用逻辑如下：

1. 用户通过命令行调用 `code_review_orchestrator.py`，提供必要的参数
2. `code_review_orchestrator.py` 解析参数并初始化各个组件
3. `code_review_orchestrator.py` 调用 `gitlab_downloader.py` 下载代码文件
4. 对于每个下载的文件，`code_review_orchestrator.py` 调用 `ai_reviewer.py` 进行审查
5. `code_review_orchestrator.py` 调用 `report_generator.py` 生成审查报告
6. `code_review_orchestrator.py` 调用 `email_sender.py` 发送最终报告

调用关系图：

```
用户
  |
  v
code_review_orchestrator.py
  |
  |----> gitlab_downloader.py
  |        |
  |        v
  |      下载代码文件
  |
  |----> ai_reviewer.py
  |        |
  |        v
  |      审查代码
  |
  |----> report_generator.py
  |        |
  |        v
  |      生成报告
  |
  |----> email_sender.py
           |
           v
         发送邮件
```

## 5. 使用方法

要使用此工具，请运行以下命令：

```bash
python code_review_orchestrator.py \
    --gitlab-url https://192.168.50.9 \
    --gitlab-token YOUR_GITLAB_TOKEN \
    --project-ids 270 \
    --branches master \
    --ai-api-url https://your-ai-api-url.com/api \
    --ai-api-key YOUR_AI_API_KEY \
    --output-dir code_review_results \
    --email-to <EMAIL> \
    --email-from <EMAIL> \
    --smtp-server smtp.example.com \
    --smtp-port 587 \
    --smtp-username your_username \
    --smtp-password your_password
```

## 6. 自定义和扩展

您可以通过以下方式自定义和扩展此工具：

1. 在 `gitlab_downloader.py` 中添加更多编程语言的支持
2. 在 `ai_reviewer.py` 中调整AI模型的提示和参数
3. 在 `report_generator.py` 中修改报告格式
4. 在 `email_sender.py` 中自定义邮件模板

## 7. 注意事项

- 确保GitLab令牌具有足够的权限来访问项目和文件
- 确保AI模型API的URL和密钥正确
- 确保SMTP服务器信息正确，且账户有权限发送邮件
- 对于大型代码库，审查过程可能需要较长时间
