﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-202px;
  width:892px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u4360 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4361_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:892px;
  height:595px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4361 {
  border-width:0px;
  position:absolute;
  left:202px;
  top:118px;
  width:892px;
  height:595px;
  display:flex;
}
#u4361 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4361_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4362_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:892px;
  height:34px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
  text-align:left;
}
#u4362 {
  border-width:0px;
  position:absolute;
  left:202px;
  top:84px;
  width:892px;
  height:34px;
  display:flex;
  color:#FFFFFF;
  text-align:left;
}
#u4362 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4362_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4363_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4363 {
  border-width:0px;
  position:absolute;
  left:249px;
  top:146px;
  width:76px;
  height:25px;
  display:flex;
}
#u4363 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4363_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4364_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:30px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u4364 {
  border-width:0px;
  position:absolute;
  left:986px;
  top:663px;
  width:71px;
  height:30px;
  display:flex;
  color:#FFFFFF;
}
#u4364 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4364_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4365_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u4365 {
  border-width:0px;
  position:absolute;
  left:824px;
  top:663px;
  width:71px;
  height:30px;
  display:flex;
  color:#000000;
}
#u4365 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4365_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4366_input {
  position:absolute;
  left:0px;
  top:0px;
  width:311px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4366_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:311px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4366_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:311px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u4366 {
  border-width:0px;
  position:absolute;
  left:339px;
  top:148px;
  width:311px;
  height:25px;
  display:flex;
  color:#AAAAAA;
}
#u4366 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4366_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:311px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u4366.disabled {
}
#u4367_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4367 {
  border-width:0px;
  position:absolute;
  left:249px;
  top:183px;
  width:76px;
  height:25px;
  display:flex;
}
#u4367 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4367_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4368_input {
  position:absolute;
  left:0px;
  top:0px;
  width:312px;
  height:49px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4368_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:312px;
  height:49px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4368_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:312px;
  height:49px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u4368 {
  border-width:0px;
  position:absolute;
  left:339px;
  top:183px;
  width:312px;
  height:49px;
  display:flex;
  color:#AAAAAA;
}
#u4368 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4368_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:312px;
  height:49px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u4368.disabled {
}
#u4369_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u4369 {
  border-width:0px;
  position:absolute;
  left:904px;
  top:663px;
  width:71px;
  height:30px;
  display:flex;
  color:#000000;
}
#u4369 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4369_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4370_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:814px;
  height:413px;
}
#u4370 {
  border-width:0px;
  position:absolute;
  left:244px;
  top:239px;
  width:814px;
  height:413px;
  display:flex;
}
#u4370 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4370_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
