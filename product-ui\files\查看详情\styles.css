﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-202px;
  width:892px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u4350 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4351_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:892px;
  height:719px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4351 {
  border-width:0px;
  position:absolute;
  left:202px;
  top:118px;
  width:892px;
  height:719px;
  display:flex;
}
#u4351 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4351_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4352_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:892px;
  height:34px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
  text-align:left;
}
#u4352 {
  border-width:0px;
  position:absolute;
  left:202px;
  top:84px;
  width:892px;
  height:34px;
  display:flex;
  color:#FFFFFF;
  text-align:left;
}
#u4352 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4352_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4353_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4353 {
  border-width:0px;
  position:absolute;
  left:249px;
  top:146px;
  width:70px;
  height:25px;
  display:flex;
}
#u4353 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4353_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4354_input {
  position:absolute;
  left:0px;
  top:0px;
  width:311px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4354_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:311px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4354_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:311px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u4354 {
  border-width:0px;
  position:absolute;
  left:339px;
  top:148px;
  width:311px;
  height:25px;
  display:flex;
  color:#AAAAAA;
}
#u4354 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4354_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:311px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u4354.disabled {
}
#u4355_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4355 {
  border-width:0px;
  position:absolute;
  left:249px;
  top:183px;
  width:70px;
  height:25px;
  display:flex;
}
#u4355 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4355_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4356_input {
  position:absolute;
  left:0px;
  top:0px;
  width:312px;
  height:49px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4356_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:312px;
  height:49px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4356_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:312px;
  height:49px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u4356 {
  border-width:0px;
  position:absolute;
  left:339px;
  top:183px;
  width:312px;
  height:49px;
  display:flex;
  color:#AAAAAA;
}
#u4356 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4356_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:312px;
  height:49px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u4356.disabled {
}
#u4357_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:812px;
  height:354px;
}
#u4357 {
  border-width:0px;
  position:absolute;
  left:249px;
  top:244px;
  width:812px;
  height:354px;
  display:flex;
}
#u4357 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4357_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4358_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:812px;
  height:216px;
}
#u4358 {
  border-width:0px;
  position:absolute;
  left:249px;
  top:598px;
  width:812px;
  height:216px;
  display:flex;
}
#u4358 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4358_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4359_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:180px;
  height:285px;
}
#u4359 {
  border-width:0px;
  position:absolute;
  left:907px;
  top:244px;
  width:180px;
  height:285px;
  display:flex;
}
#u4359 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4359_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
