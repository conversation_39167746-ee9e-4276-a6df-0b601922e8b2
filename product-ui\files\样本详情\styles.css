﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:1753px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1011_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1011 {
  border-width:0px;
  position:absolute;
  left:255px;
  top:114px;
  width:1px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1011 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1011_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u1012 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1013_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1289px;
  height:53px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1013 {
  border-width:0px;
  position:absolute;
  left:464px;
  top:701px;
  width:1289px;
  height:53px;
  display:flex;
}
#u1013 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1013_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1014_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1014 {
  border-width:0px;
  position:absolute;
  left:487px;
  top:715px;
  width:83px;
  height:25px;
  display:flex;
  font-size:16px;
}
#u1014 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1014_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1015_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1015 {
  border-width:0px;
  position:absolute;
  left:1521px;
  top:714px;
  width:139px;
  height:25px;
  display:flex;
  font-size:16px;
}
#u1015 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1015_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1016_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:100px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:left;
}
#u1016 {
  border-width:0px;
  position:absolute;
  left:574px;
  top:710px;
  width:100px;
  height:35px;
  display:flex;
  text-align:left;
}
#u1016 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1016_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1017_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:15px;
}
#u1017 {
  border-width:0px;
  position:absolute;
  left:652px;
  top:720px;
  width:15px;
  height:15px;
  display:flex;
}
#u1017 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1017_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1018_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u1018 {
  border-width:0px;
  position:absolute;
  left:1653px;
  top:716px;
  width:25px;
  height:25px;
  display:flex;
}
#u1018 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1018_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1019_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(24, 144, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u1019 {
  border-width:0px;
  position:absolute;
  left:1686px;
  top:714px;
  width:29px;
  height:27px;
  display:flex;
  color:#1890FF;
}
#u1019 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1019_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1020_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u1020 {
  border-width:0px;
  position:absolute;
  left:1728px;
  top:717px;
  width:25px;
  height:25px;
  display:flex;
}
#u1020 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1020_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1021_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:85px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#666666;
  text-align:left;
  line-height:18px;
}
#u1021 {
  border-width:0px;
  position:absolute;
  left:238px;
  top:100px;
  width:1175px;
  height:85px;
  display:flex;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#666666;
  text-align:left;
  line-height:18px;
}
#u1021 .text {
  position:absolute;
  align-self:flex-start;
  padding:16px 16px 2px 16px;
  box-sizing:border-box;
  width:100%;
}
#u1021_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1022 {
  border-width:0px;
  position:absolute;
  left:239px;
  top:238px;
  width:33px;
  height:5px;
}
#u1022_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:5px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1022_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1023 {
  border-width:0px;
  position:absolute;
  left:1380px;
  top:238px;
  width:33px;
  height:5px;
}
#u1023_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:5px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1023_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1024 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1025_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:101px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1025 {
  border-width:0px;
  position:absolute;
  left:509px;
  top:99px;
  width:101px;
  height:35px;
  display:flex;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1025 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1025_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1026 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1027_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1027 {
  border-width:0px;
  position:absolute;
  left:1148px;
  top:100px;
  width:72px;
  height:35px;
  display:flex;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1027 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1027_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1028 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1029_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1029 {
  border-width:0px;
  position:absolute;
  left:237px;
  top:134px;
  width:106px;
  height:35px;
  display:flex;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1029 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1029_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1030 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1031_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1031 {
  border-width:0px;
  position:absolute;
  left:509px;
  top:134px;
  width:106px;
  height:35px;
  display:flex;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1031 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1031_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1032_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1032 {
  border-width:0px;
  position:absolute;
  left:615px;
  top:139px;
  width:15px;
  height:25px;
  display:flex;
}
#u1032 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1032_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1033 {
  border-width:0px;
  position:absolute;
  left:238px;
  top:181px;
  width:1191px;
  height:573px;
}
#u1033_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1191px;
  height:573px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1033_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1034_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:218px;
  height:573px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1034 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:218px;
  height:573px;
  display:flex;
}
#u1034 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1034_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1035_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:948px;
  height:412px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1035 {
  border-width:0px;
  position:absolute;
  left:228px;
  top:0px;
  width:948px;
  height:412px;
  display:flex;
}
#u1035 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1035_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1036_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1036 {
  border-width:0px;
  position:absolute;
  left:64px;
  top:149px;
  width:1px;
  height:16px;
  display:flex;
}
#u1036 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1036_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u1037_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:17px;
}
#u1037 {
  border-width:0px;
  position:absolute;
  left:865px;
  top:398px;
  width:12px;
  height:17px;
  display:flex;
}
#u1037 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1037_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1038_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:28px;
}
#u1038 {
  border-width:0px;
  position:absolute;
  left:1092px;
  top:714px;
  width:20px;
  height:28px;
  display:flex;
}
#u1038 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1038_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1039 {
  border-width:0px;
  position:absolute;
  left:228px;
  top:0px;
  width:948px;
  height:348px;
}
#u1039_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:948px;
  height:348px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1039_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1040_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:948px;
  height:45px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1040 {
  border-width:0px;
  position:absolute;
  left:1px;
  top:1px;
  width:948px;
  height:45px;
  display:flex;
}
#u1040 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1040_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1041 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:123px;
  width:659px;
  height:206px;
}
#u1042_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:35px;
}
#u1042 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:35px;
  display:flex;
}
#u1042 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1042_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1043_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:35px;
}
#u1043 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:0px;
  width:37px;
  height:35px;
  display:flex;
}
#u1043 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1043_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1044_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:35px;
}
#u1044 {
  border-width:0px;
  position:absolute;
  left:77px;
  top:0px;
  width:114px;
  height:35px;
  display:flex;
}
#u1044 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1044_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1045_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:153px;
  height:35px;
}
#u1045 {
  border-width:0px;
  position:absolute;
  left:191px;
  top:0px;
  width:153px;
  height:35px;
  display:flex;
}
#u1045 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1045_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1046_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:147px;
  height:35px;
}
#u1046 {
  border-width:0px;
  position:absolute;
  left:344px;
  top:0px;
  width:147px;
  height:35px;
  display:flex;
}
#u1046 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1046_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1047_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:35px;
}
#u1047 {
  border-width:0px;
  position:absolute;
  left:491px;
  top:0px;
  width:168px;
  height:35px;
  display:flex;
}
#u1047 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1047_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1048_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:29px;
}
#u1048 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:35px;
  width:40px;
  height:29px;
  display:flex;
}
#u1048 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1048_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1049_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:29px;
}
#u1049 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:35px;
  width:37px;
  height:29px;
  display:flex;
}
#u1049 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1049_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1050_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:29px;
}
#u1050 {
  border-width:0px;
  position:absolute;
  left:77px;
  top:35px;
  width:114px;
  height:29px;
  display:flex;
  font-size:14px;
}
#u1050 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1050_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1051_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:153px;
  height:29px;
}
#u1051 {
  border-width:0px;
  position:absolute;
  left:191px;
  top:35px;
  width:153px;
  height:29px;
  display:flex;
  font-size:14px;
}
#u1051 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1051_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1052_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:147px;
  height:29px;
}
#u1052 {
  border-width:0px;
  position:absolute;
  left:344px;
  top:35px;
  width:147px;
  height:29px;
  display:flex;
  font-size:14px;
}
#u1052 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1052_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1053_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:29px;
}
#u1053 {
  border-width:0px;
  position:absolute;
  left:491px;
  top:35px;
  width:168px;
  height:29px;
  display:flex;
}
#u1053 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1053_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1054_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:34px;
}
#u1054 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:64px;
  width:40px;
  height:34px;
  display:flex;
}
#u1054 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1054_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1055_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:34px;
}
#u1055 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:64px;
  width:37px;
  height:34px;
  display:flex;
}
#u1055 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1055_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1056_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:34px;
}
#u1056 {
  border-width:0px;
  position:absolute;
  left:77px;
  top:64px;
  width:114px;
  height:34px;
  display:flex;
  font-size:14px;
}
#u1056 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1056_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1057_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:153px;
  height:34px;
}
#u1057 {
  border-width:0px;
  position:absolute;
  left:191px;
  top:64px;
  width:153px;
  height:34px;
  display:flex;
  font-size:14px;
}
#u1057 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1057_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1058_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:147px;
  height:34px;
}
#u1058 {
  border-width:0px;
  position:absolute;
  left:344px;
  top:64px;
  width:147px;
  height:34px;
  display:flex;
  font-size:14px;
}
#u1058 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1058_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1059_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:34px;
}
#u1059 {
  border-width:0px;
  position:absolute;
  left:491px;
  top:64px;
  width:168px;
  height:34px;
  display:flex;
}
#u1059 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1059_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1060_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:36px;
}
#u1060 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:98px;
  width:40px;
  height:36px;
  display:flex;
}
#u1060 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1060_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1061_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:36px;
}
#u1061 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:98px;
  width:37px;
  height:36px;
  display:flex;
}
#u1061 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1061_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1062_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:36px;
}
#u1062 {
  border-width:0px;
  position:absolute;
  left:77px;
  top:98px;
  width:114px;
  height:36px;
  display:flex;
  font-size:14px;
}
#u1062 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1062_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1063_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:153px;
  height:36px;
}
#u1063 {
  border-width:0px;
  position:absolute;
  left:191px;
  top:98px;
  width:153px;
  height:36px;
  display:flex;
  font-size:14px;
}
#u1063 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1063_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1064_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:147px;
  height:36px;
}
#u1064 {
  border-width:0px;
  position:absolute;
  left:344px;
  top:98px;
  width:147px;
  height:36px;
  display:flex;
  font-size:14px;
}
#u1064 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1064_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1065_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:36px;
}
#u1065 {
  border-width:0px;
  position:absolute;
  left:491px;
  top:98px;
  width:168px;
  height:36px;
  display:flex;
}
#u1065 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1065_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1066_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:34px;
}
#u1066 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:134px;
  width:40px;
  height:34px;
  display:flex;
}
#u1066 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1066_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1067_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:34px;
}
#u1067 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:134px;
  width:37px;
  height:34px;
  display:flex;
}
#u1067 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1067_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1068_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:34px;
}
#u1068 {
  border-width:0px;
  position:absolute;
  left:77px;
  top:134px;
  width:114px;
  height:34px;
  display:flex;
  font-size:14px;
}
#u1068 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1068_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1069_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:153px;
  height:34px;
}
#u1069 {
  border-width:0px;
  position:absolute;
  left:191px;
  top:134px;
  width:153px;
  height:34px;
  display:flex;
  font-size:14px;
}
#u1069 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1069_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1070_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:147px;
  height:34px;
}
#u1070 {
  border-width:0px;
  position:absolute;
  left:344px;
  top:134px;
  width:147px;
  height:34px;
  display:flex;
  font-size:14px;
}
#u1070 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1070_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1071_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:34px;
}
#u1071 {
  border-width:0px;
  position:absolute;
  left:491px;
  top:134px;
  width:168px;
  height:34px;
  display:flex;
}
#u1071 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1071_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1072_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:38px;
}
#u1072 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:168px;
  width:40px;
  height:38px;
  display:flex;
}
#u1072 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1072_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1073_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:37px;
  height:38px;
}
#u1073 {
  border-width:0px;
  position:absolute;
  left:40px;
  top:168px;
  width:37px;
  height:38px;
  display:flex;
}
#u1073 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1073_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1074_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:38px;
}
#u1074 {
  border-width:0px;
  position:absolute;
  left:77px;
  top:168px;
  width:114px;
  height:38px;
  display:flex;
  font-size:14px;
}
#u1074 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1074_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1075_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:153px;
  height:38px;
}
#u1075 {
  border-width:0px;
  position:absolute;
  left:191px;
  top:168px;
  width:153px;
  height:38px;
  display:flex;
  font-size:14px;
}
#u1075 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1075_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1076_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:147px;
  height:38px;
}
#u1076 {
  border-width:0px;
  position:absolute;
  left:344px;
  top:168px;
  width:147px;
  height:38px;
  display:flex;
  font-size:14px;
}
#u1076 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1076_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1077_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:38px;
}
#u1077 {
  border-width:0px;
  position:absolute;
  left:491px;
  top:168px;
  width:168px;
  height:38px;
  display:flex;
}
#u1077 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1077_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1078 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1079_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u1079 {
  border-width:0px;
  position:absolute;
  left:85px;
  top:8px;
  width:131px;
  height:31px;
  display:flex;
  color:#AAAAAA;
}
#u1079 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1079_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(1, 119, 255, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u1079.selected {
}
#u1079_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(1, 119, 255, 1);
  border-radius:2px;
  -moz-box-shadow:0px 0px 3px rgba(1, 119, 255, 1);
  -webkit-box-shadow:0px 0px 3px rgba(1, 119, 255, 1);
  box-shadow:0px 0px 3px rgba(1, 119, 255, 1);
  color:#AAAAAA;
}
#u1079.disabled {
}
#u1079_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1080_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:8px;
}
#u1080 {
  border-width:0px;
  position:absolute;
  left:176px;
  top:18px;
  width:14px;
  height:8px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  color:#AAAAAA;
}
#u1080 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1080_img.selected {
}
#u1080.selected {
}
#u1080_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1081_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u1081 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:7px;
  width:70px;
  height:25px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u1081 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1081_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1083 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1084 label {
  left:0px;
  width:100%;
}
#u1084_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u1084 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:110px;
  width:30px;
  height:16px;
  display:flex;
}
#u1084 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u1084_img.selected {
}
#u1084.selected {
}
#u1084_img.disabled {
}
#u1084.disabled {
}
#u1084_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1084_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u1085 label {
  left:0px;
  width:100%;
}
#u1085_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u1085 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:138px;
  width:30px;
  height:16px;
  display:flex;
}
#u1085 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u1085_img.selected {
}
#u1085.selected {
}
#u1085_img.disabled {
}
#u1085.disabled {
}
#u1085_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1085_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u1086 label {
  left:0px;
  width:100%;
}
#u1086_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u1086 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:169px;
  width:30px;
  height:16px;
  display:flex;
}
#u1086 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u1086_img.selected {
}
#u1086.selected {
}
#u1086_img.disabled {
}
#u1086.disabled {
}
#u1086_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1086_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u1087 label {
  left:0px;
  width:100%;
}
#u1087_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u1087 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:202px;
  width:30px;
  height:16px;
  display:flex;
}
#u1087 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u1087_img.selected {
}
#u1087.selected {
}
#u1087_img.disabled {
}
#u1087.disabled {
}
#u1087_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1087_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u1088 label {
  left:0px;
  width:100%;
}
#u1088_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u1088 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:233px;
  width:30px;
  height:16px;
  display:flex;
}
#u1088 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u1088_img.selected {
}
#u1088.selected {
}
#u1088_img.disabled {
}
#u1088.disabled {
}
#u1088_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1088_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u1089 label {
  left:0px;
  width:100%;
}
#u1089_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u1089 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:265px;
  width:30px;
  height:16px;
  display:flex;
}
#u1089 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u1089_img.selected {
}
#u1089.selected {
}
#u1089_img.disabled {
}
#u1089.disabled {
}
#u1089_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1089_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u1090 label {
  left:0px;
  width:100%;
}
#u1090_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u1090 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:300px;
  width:30px;
  height:16px;
  display:flex;
}
#u1090 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u1090_img.selected {
}
#u1090.selected {
}
#u1090_img.disabled {
}
#u1090.disabled {
}
#u1090_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1090_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u1091_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u1091 {
  border-width:0px;
  position:absolute;
  left:13px;
  top:57px;
  width:54px;
  height:27px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u1091 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1091_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1092 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1093_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u1093 {
  border-width:0px;
  position:absolute;
  left:307px;
  top:4px;
  width:131px;
  height:31px;
  display:flex;
  color:#AAAAAA;
}
#u1093 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1093_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(1, 119, 255, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u1093.selected {
}
#u1093_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(1, 119, 255, 1);
  border-radius:2px;
  -moz-box-shadow:0px 0px 3px rgba(1, 119, 255, 1);
  -webkit-box-shadow:0px 0px 3px rgba(1, 119, 255, 1);
  box-shadow:0px 0px 3px rgba(1, 119, 255, 1);
  color:#AAAAAA;
}
#u1093.disabled {
}
#u1093_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1094_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u1094 {
  border-width:0px;
  position:absolute;
  left:237px;
  top:8px;
  width:70px;
  height:25px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u1094 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1094_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1039_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:948px;
  height:348px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1039_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1095 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:46px;
  width:948px;
  height:207px;
}
#u1096_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:52px;
}
#u1096 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:52px;
  display:flex;
}
#u1096 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1096_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1097_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:52px;
}
#u1097 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:0px;
  width:133px;
  height:52px;
  display:flex;
}
#u1097 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1097_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1098_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:52px;
}
#u1098 {
  border-width:0px;
  position:absolute;
  left:302px;
  top:0px;
  width:125px;
  height:52px;
  display:flex;
}
#u1098 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1098_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1099_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:52px;
}
#u1099 {
  border-width:0px;
  position:absolute;
  left:427px;
  top:0px;
  width:141px;
  height:52px;
  display:flex;
}
#u1099 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1099_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1100_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:52px;
}
#u1100 {
  border-width:0px;
  position:absolute;
  left:568px;
  top:0px;
  width:139px;
  height:52px;
  display:flex;
}
#u1100 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1100_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1101_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:241px;
  height:52px;
}
#u1101 {
  border-width:0px;
  position:absolute;
  left:707px;
  top:0px;
  width:241px;
  height:52px;
  display:flex;
}
#u1101 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1101_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1102_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:28px;
}
#u1102 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:52px;
  width:169px;
  height:28px;
  display:flex;
}
#u1102 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1102_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1103_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:28px;
}
#u1103 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:52px;
  width:133px;
  height:28px;
  display:flex;
}
#u1103 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1103_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1104_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:28px;
}
#u1104 {
  border-width:0px;
  position:absolute;
  left:302px;
  top:52px;
  width:125px;
  height:28px;
  display:flex;
}
#u1104 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1104_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1105_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:28px;
}
#u1105 {
  border-width:0px;
  position:absolute;
  left:427px;
  top:52px;
  width:141px;
  height:28px;
  display:flex;
}
#u1105 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1105_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1106_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:28px;
}
#u1106 {
  border-width:0px;
  position:absolute;
  left:568px;
  top:52px;
  width:139px;
  height:28px;
  display:flex;
}
#u1106 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1106_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1107_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:241px;
  height:28px;
}
#u1107 {
  border-width:0px;
  position:absolute;
  left:707px;
  top:52px;
  width:241px;
  height:28px;
  display:flex;
}
#u1107 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1107_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1108_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:36px;
}
#u1108 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:169px;
  height:36px;
  display:flex;
}
#u1108 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1108_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1109_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:36px;
}
#u1109 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:80px;
  width:133px;
  height:36px;
  display:flex;
}
#u1109 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1109_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1110_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:36px;
}
#u1110 {
  border-width:0px;
  position:absolute;
  left:302px;
  top:80px;
  width:125px;
  height:36px;
  display:flex;
}
#u1110 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1110_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1111_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:36px;
}
#u1111 {
  border-width:0px;
  position:absolute;
  left:427px;
  top:80px;
  width:141px;
  height:36px;
  display:flex;
}
#u1111 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1111_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1112_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:36px;
}
#u1112 {
  border-width:0px;
  position:absolute;
  left:568px;
  top:80px;
  width:139px;
  height:36px;
  display:flex;
}
#u1112 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1112_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1113_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:241px;
  height:36px;
}
#u1113 {
  border-width:0px;
  position:absolute;
  left:707px;
  top:80px;
  width:241px;
  height:36px;
  display:flex;
}
#u1113 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1113_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1114_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:36px;
}
#u1114 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:116px;
  width:169px;
  height:36px;
  display:flex;
}
#u1114 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1114_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1115_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:36px;
}
#u1115 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:116px;
  width:133px;
  height:36px;
  display:flex;
}
#u1115 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1115_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1116_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:36px;
}
#u1116 {
  border-width:0px;
  position:absolute;
  left:302px;
  top:116px;
  width:125px;
  height:36px;
  display:flex;
}
#u1116 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1116_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1117_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:36px;
}
#u1117 {
  border-width:0px;
  position:absolute;
  left:427px;
  top:116px;
  width:141px;
  height:36px;
  display:flex;
}
#u1117 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1117_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1118_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:36px;
}
#u1118 {
  border-width:0px;
  position:absolute;
  left:568px;
  top:116px;
  width:139px;
  height:36px;
  display:flex;
}
#u1118 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1118_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1119_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:241px;
  height:36px;
}
#u1119 {
  border-width:0px;
  position:absolute;
  left:707px;
  top:116px;
  width:241px;
  height:36px;
  display:flex;
}
#u1119 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1119_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1120_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:36px;
}
#u1120 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:152px;
  width:169px;
  height:36px;
  display:flex;
}
#u1120 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1120_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1121_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:36px;
}
#u1121 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:152px;
  width:133px;
  height:36px;
  display:flex;
}
#u1121 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1121_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1122_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:36px;
}
#u1122 {
  border-width:0px;
  position:absolute;
  left:302px;
  top:152px;
  width:125px;
  height:36px;
  display:flex;
}
#u1122 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1122_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1123_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:36px;
}
#u1123 {
  border-width:0px;
  position:absolute;
  left:427px;
  top:152px;
  width:141px;
  height:36px;
  display:flex;
}
#u1123 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1123_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1124_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:36px;
}
#u1124 {
  border-width:0px;
  position:absolute;
  left:568px;
  top:152px;
  width:139px;
  height:36px;
  display:flex;
}
#u1124 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1124_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1125_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:241px;
  height:36px;
}
#u1125 {
  border-width:0px;
  position:absolute;
  left:707px;
  top:152px;
  width:241px;
  height:36px;
  display:flex;
}
#u1125 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1125_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1126_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:19px;
}
#u1126 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:188px;
  width:169px;
  height:19px;
  display:flex;
}
#u1126 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1126_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1127_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:19px;
}
#u1127 {
  border-width:0px;
  position:absolute;
  left:169px;
  top:188px;
  width:133px;
  height:19px;
  display:flex;
}
#u1127 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1127_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1128_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:125px;
  height:19px;
}
#u1128 {
  border-width:0px;
  position:absolute;
  left:302px;
  top:188px;
  width:125px;
  height:19px;
  display:flex;
}
#u1128 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1128_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1129_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:141px;
  height:19px;
}
#u1129 {
  border-width:0px;
  position:absolute;
  left:427px;
  top:188px;
  width:141px;
  height:19px;
  display:flex;
}
#u1129 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1129_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1130_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:19px;
}
#u1130 {
  border-width:0px;
  position:absolute;
  left:568px;
  top:188px;
  width:139px;
  height:19px;
  display:flex;
}
#u1130 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1130_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1131_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:241px;
  height:19px;
}
#u1131 {
  border-width:0px;
  position:absolute;
  left:707px;
  top:188px;
  width:241px;
  height:19px;
  display:flex;
}
#u1131 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1131_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1132 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1133_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:951px;
  height:53px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1133 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:268px;
  width:951px;
  height:53px;
  display:flex;
}
#u1133 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1133_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1134_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1134 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:282px;
  width:84px;
  height:25px;
  display:flex;
  font-size:16px;
}
#u1134 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1134_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1135_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1135 {
  border-width:0px;
  position:absolute;
  left:768px;
  top:278px;
  width:140px;
  height:25px;
  display:flex;
  font-size:16px;
}
#u1135 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1135_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1136_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:left;
}
#u1136 {
  border-width:0px;
  position:absolute;
  left:118px;
  top:277px;
  width:78px;
  height:35px;
  display:flex;
  text-align:left;
}
#u1136 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1136_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1137_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:25px;
}
#u1137 {
  border-width:0px;
  position:absolute;
  left:741px;
  top:273px;
  width:20px;
  height:25px;
  display:flex;
}
#u1137 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1137_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1138_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(24, 144, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u1138 {
  border-width:0px;
  position:absolute;
  left:928px;
  top:278px;
  width:23px;
  height:27px;
  display:flex;
  color:#1890FF;
}
#u1138 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1138_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1139_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:25px;
}
#u1139 {
  border-width:0px;
  position:absolute;
  left:903px;
  top:275px;
  width:20px;
  height:25px;
  display:flex;
}
#u1139 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1139_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1140 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1141_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1141 {
  border-width:0px;
  position:absolute;
  left:90px;
  top:1px;
  width:131px;
  height:31px;
  display:flex;
}
#u1141 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1141_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(1, 119, 255, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1141.selected {
}
#u1141_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:131px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(1, 119, 255, 1);
  border-radius:2px;
  -moz-box-shadow:0px 0px 3px rgba(1, 119, 255, 1);
  -webkit-box-shadow:0px 0px 3px rgba(1, 119, 255, 1);
  box-shadow:0px 0px 3px rgba(1, 119, 255, 1);
}
#u1141.disabled {
}
#u1141_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1142_input {
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  padding:3px 2px 3px 2px;
  font-family:'Microsoft YaHei Regular', 'Microsoft YaHei';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  letter-spacing:normal;
  color:#666666;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1142_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  padding:3px 2px 3px 2px;
  font-family:'Microsoft YaHei Regular', 'Microsoft YaHei';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  letter-spacing:normal;
  color:#666666;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1142_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1142 {
  border-width:0px;
  position:absolute;
  left:93px;
  top:6px;
  width:85px;
  height:20px;
  display:flex;
}
#u1142 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1142_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:85px;
  height:20px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1142.disabled {
}
#u1143_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:8px;
}
#u1143 {
  border-width:0px;
  position:absolute;
  left:181px;
  top:11px;
  width:14px;
  height:8px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
}
#u1143 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1143_img.selected {
}
#u1143.selected {
}
#u1143_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1144 {
  border-width:0px;
  position:absolute;
  left:182px;
  top:9px;
  width:11px;
  height:14px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u1145_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u1145 {
  border-width:0px;
  position:absolute;
  left:34px;
  top:6px;
  width:56px;
  height:25px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u1145 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1145_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1146 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1147_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:32px;
  background:inherit;
  background-color:rgba(1, 119, 255, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
}
#u1147 {
  border-width:0px;
  position:absolute;
  left:242px;
  top:0px;
  width:74px;
  height:32px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
}
#u1147 .text {
  position:absolute;
  align-self:center;
  padding:2px 16px 2px 16px;
  box-sizing:border-box;
  width:100%;
}
#u1147_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:32px;
  background:inherit;
  background-color:rgba(0, 153, 255, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
}
#u1147.mouseOver {
}
#u1147_div.mouseDown {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:32px;
  background:inherit;
  background-color:rgba(22, 112, 214, 1);
  border:none;
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#FFFFFF;
}
#u1147.mouseDown {
}
#u1147_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1148_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  color:#666666;
}
#u1148 {
  border-width:0px;
  position:absolute;
  left:327px;
  top:0px;
  width:74px;
  height:32px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  color:#666666;
}
#u1148 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 16px;
  box-sizing:border-box;
  width:100%;
}
#u1148_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(0, 153, 255, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  color:#666666;
}
#u1148.mouseOver {
}
#u1148_div.mouseDown {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:32px;
  background:inherit;
  background-color:rgba(239, 244, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(22, 112, 214, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  color:#666666;
}
#u1148.mouseDown {
}
#u1148_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:32px;
  background:inherit;
  background-color:rgba(245, 245, 245, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  color:#666666;
}
#u1148.disabled {
}
#u1148_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1149 {
  border-width:0px;
  position:absolute;
  left:1096px;
  top:41px;
  width:60px;
  height:24px;
  visibility:hidden;
}
#u1149_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:24px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1149_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1150_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:28px;
}
#u1150 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:28px;
  display:flex;
}
#u1150 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1150_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1151 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:53px;
  width:119px;
  height:80px;
}
#u1151_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1152 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:20px;
}
#u1153_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u1153 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:6px;
  width:9px;
  height:9px;
  display:flex;
}
#u1153 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1153_img.selected {
}
#u1153.selected {
}
#u1153_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1154_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1154 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:57px;
  height:20px;
  display:flex;
}
#u1154 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1154_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1152_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1155 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:20px;
  width:79px;
  height:20px;
}
#u1156_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u1156 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:6px;
  width:9px;
  height:9px;
  display:flex;
}
#u1156 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1156_img.selected {
}
#u1156.selected {
}
#u1156_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1157_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1157 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:57px;
  height:20px;
  display:flex;
}
#u1157 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1157_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1155_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1158 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:20px;
  width:79px;
  height:20px;
}
#u1159_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1159 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:57px;
  height:20px;
  display:flex;
}
#u1159 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1159_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1160 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:40px;
  width:79px;
  height:20px;
}
#u1161_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:57px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1161 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:57px;
  height:20px;
  display:flex;
}
#u1161 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1161_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1162_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1162_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1162_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#AAAAAA;
}
#u1162 {
  border-width:0px;
  position:absolute;
  left:9px;
  top:11px;
  width:200px;
  height:25px;
  display:flex;
  font-size:10px;
  color:#AAAAAA;
}
#u1162 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1162_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#AAAAAA;
}
#u1162.disabled {
}
#u1163_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:19px;
}
#u1163 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:17px;
  width:18px;
  height:19px;
  display:flex;
}
#u1163 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1163_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1033_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1191px;
  height:573px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1033_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1164_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1164 {
  border-width:0px;
  position:absolute;
  left:1px;
  top:0px;
  width:1175px;
  height:48px;
  display:flex;
}
#u1164 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1164_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1165 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1166_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:88px;
  height:48px;
}
#u1166 {
  border-width:0px;
  position:absolute;
  left:509px;
  top:0px;
  width:88px;
  height:48px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u1166 .text {
  position:absolute;
  align-self:center;
  padding:0px 8px 0px 24px;
  box-sizing:border-box;
  width:100%;
}
#u1166_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1167 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1168_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:48px;
}
#u1168 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:0px;
  width:60px;
  height:48px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
#u1168 .text {
  position:absolute;
  align-self:center;
  padding:0px 8px 0px 24px;
  box-sizing:border-box;
  width:100%;
}
#u1168_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1169_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1169 {
  border-width:0px;
  position:absolute;
  left:1px;
  top:48px;
  width:1175px;
  height:48px;
  display:flex;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1169 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1169_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1169.mouseOver {
}
#u1169_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1169.selected {
}
#u1169_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1170_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:47px;
}
#u1170 {
  border-width:0px;
  position:absolute;
  left:517px;
  top:48px;
  width:112px;
  height:47px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:normal;
}
#u1170 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 24px;
  box-sizing:border-box;
  width:100%;
}
#u1170_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1171_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:47px;
}
#u1171 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:48px;
  width:112px;
  height:47px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:normal;
}
#u1171 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 24px;
  box-sizing:border-box;
  width:100%;
}
#u1171_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1172_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1172 {
  border-width:0px;
  position:absolute;
  left:1px;
  top:96px;
  width:1175px;
  height:48px;
  display:flex;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1172 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1172_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1172.mouseOver {
}
#u1172_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1172.selected {
}
#u1172_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1173_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:47px;
}
#u1173 {
  border-width:0px;
  position:absolute;
  left:517px;
  top:96px;
  width:112px;
  height:47px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:normal;
}
#u1173 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 24px;
  box-sizing:border-box;
  width:100%;
}
#u1173_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1174_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1174 {
  border-width:0px;
  position:absolute;
  left:1px;
  top:144px;
  width:1175px;
  height:48px;
  display:flex;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1174 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1174_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1174.mouseOver {
}
#u1174_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1174.selected {
}
#u1174_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1175_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:47px;
}
#u1175 {
  border-width:0px;
  position:absolute;
  left:517px;
  top:144px;
  width:112px;
  height:47px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:normal;
}
#u1175 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 24px;
  box-sizing:border-box;
  width:100%;
}
#u1175_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1176_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:47px;
}
#u1176 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:144px;
  width:112px;
  height:47px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:normal;
}
#u1176 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 24px;
  box-sizing:border-box;
  width:100%;
}
#u1176_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1177_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1177 {
  border-width:0px;
  position:absolute;
  left:1px;
  top:192px;
  width:1175px;
  height:48px;
  display:flex;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1177 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1177_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1177.mouseOver {
}
#u1177_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1177.selected {
}
#u1177_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1178_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:47px;
}
#u1178 {
  border-width:0px;
  position:absolute;
  left:517px;
  top:192px;
  width:112px;
  height:47px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:normal;
}
#u1178 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 24px;
  box-sizing:border-box;
  width:100%;
}
#u1178_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1179_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:47px;
}
#u1179 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:192px;
  width:112px;
  height:47px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:normal;
}
#u1179 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 24px;
  box-sizing:border-box;
  width:100%;
}
#u1179_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1180_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1180 {
  border-width:0px;
  position:absolute;
  left:1px;
  top:240px;
  width:1175px;
  height:48px;
  display:flex;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1180 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1180_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1180.mouseOver {
}
#u1180_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1180.selected {
}
#u1180_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1181_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:47px;
}
#u1181 {
  border-width:0px;
  position:absolute;
  left:517px;
  top:240px;
  width:112px;
  height:47px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:normal;
}
#u1181 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 24px;
  box-sizing:border-box;
  width:100%;
}
#u1181_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1182_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:47px;
}
#u1182 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:240px;
  width:112px;
  height:47px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:normal;
}
#u1182 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 24px;
  box-sizing:border-box;
  width:100%;
}
#u1182_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1183_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1183 {
  border-width:0px;
  position:absolute;
  left:1px;
  top:280px;
  width:1175px;
  height:48px;
  display:flex;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1183 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1183_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1183.mouseOver {
}
#u1183_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1183.selected {
}
#u1183_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1184_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:47px;
}
#u1184 {
  border-width:0px;
  position:absolute;
  left:517px;
  top:280px;
  width:112px;
  height:47px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:normal;
}
#u1184 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 24px;
  box-sizing:border-box;
  width:100%;
}
#u1184_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1185_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:47px;
}
#u1185 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:280px;
  width:112px;
  height:47px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:normal;
}
#u1185 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 24px;
  box-sizing:border-box;
  width:100%;
}
#u1185_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1186_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1186 {
  border-width:0px;
  position:absolute;
  left:1px;
  top:328px;
  width:1175px;
  height:48px;
  display:flex;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1186 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1186_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1186.mouseOver {
}
#u1186_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1175px;
  height:48px;
  background:inherit;
  background-color:rgba(250, 250, 250, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(240, 240, 240, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:10px;
}
#u1186.selected {
}
#u1186_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1187_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:47px;
}
#u1187 {
  border-width:0px;
  position:absolute;
  left:517px;
  top:328px;
  width:112px;
  height:47px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:normal;
}
#u1187 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 24px;
  box-sizing:border-box;
  width:100%;
}
#u1187_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1188_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:47px;
}
#u1188 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:328px;
  width:112px;
  height:47px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:normal;
}
#u1188 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 24px;
  box-sizing:border-box;
  width:100%;
}
#u1188_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1189_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:47px;
}
#u1189 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:96px;
  width:112px;
  height:47px;
  display:flex;
  opacity:0.2;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
  line-height:normal;
}
#u1189 .text {
  position:absolute;
  align-self:center;
  padding:0px 16px 0px 24px;
  box-sizing:border-box;
  width:100%;
}
#u1189_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1033_state2 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1191px;
  height:573px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1033_state2_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1190_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1171px;
  height:514px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-left:0px;
  border-right:0px;
  border-bottom:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1190 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1171px;
  height:514px;
  display:flex;
}
#u1190 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1190_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1191 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:63px;
  width:1144px;
  height:387px;
}
#u1192_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:47px;
}
#u1192 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:47px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1192 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1192_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1193_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:47px;
}
#u1193 {
  border-width:0px;
  position:absolute;
  left:86px;
  top:0px;
  width:137px;
  height:47px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1193 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1193_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1194_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:47px;
}
#u1194 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:0px;
  width:127px;
  height:47px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1194 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1194_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1195_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:152px;
  height:47px;
}
#u1195 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:0px;
  width:152px;
  height:47px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1195 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1195_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1196_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:47px;
}
#u1196 {
  border-width:0px;
  position:absolute;
  left:502px;
  top:0px;
  width:202px;
  height:47px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1196 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1196_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1197_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:47px;
}
#u1197 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:0px;
  width:148px;
  height:47px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1197 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1197_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1198_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:47px;
}
#u1198 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:0px;
  width:292px;
  height:47px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1198 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1198_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1199_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:34px;
}
#u1199 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:47px;
  width:86px;
  height:34px;
  display:flex;
}
#u1199 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1199_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1200_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:34px;
}
#u1200 {
  border-width:0px;
  position:absolute;
  left:86px;
  top:47px;
  width:137px;
  height:34px;
  display:flex;
}
#u1200 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1200_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1201_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:34px;
}
#u1201 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:47px;
  width:127px;
  height:34px;
  display:flex;
}
#u1201 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1201_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1202_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:152px;
  height:34px;
}
#u1202 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:47px;
  width:152px;
  height:34px;
  display:flex;
}
#u1202 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1202_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1203_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:34px;
}
#u1203 {
  border-width:0px;
  position:absolute;
  left:502px;
  top:47px;
  width:202px;
  height:34px;
  display:flex;
}
#u1203 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1203_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1204_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:34px;
}
#u1204 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:47px;
  width:148px;
  height:34px;
  display:flex;
}
#u1204 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1204_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1205_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:34px;
}
#u1205 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:47px;
  width:292px;
  height:34px;
  display:flex;
}
#u1205 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1205_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1206_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:34px;
}
#u1206 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:81px;
  width:86px;
  height:34px;
  display:flex;
}
#u1206 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1206_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1207_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:34px;
}
#u1207 {
  border-width:0px;
  position:absolute;
  left:86px;
  top:81px;
  width:137px;
  height:34px;
  display:flex;
}
#u1207 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1207_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1208_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:34px;
}
#u1208 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:81px;
  width:127px;
  height:34px;
  display:flex;
}
#u1208 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1208_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1209_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:152px;
  height:34px;
}
#u1209 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:81px;
  width:152px;
  height:34px;
  display:flex;
}
#u1209 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1209_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1210_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:34px;
}
#u1210 {
  border-width:0px;
  position:absolute;
  left:502px;
  top:81px;
  width:202px;
  height:34px;
  display:flex;
}
#u1210 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1210_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1211_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:34px;
}
#u1211 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:81px;
  width:148px;
  height:34px;
  display:flex;
}
#u1211 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1211_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1212_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:34px;
}
#u1212 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:81px;
  width:292px;
  height:34px;
  display:flex;
}
#u1212 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1212_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1213_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:34px;
}
#u1213 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:115px;
  width:86px;
  height:34px;
  display:flex;
}
#u1213 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1213_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1214_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:34px;
}
#u1214 {
  border-width:0px;
  position:absolute;
  left:86px;
  top:115px;
  width:137px;
  height:34px;
  display:flex;
}
#u1214 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1214_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1215_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:34px;
}
#u1215 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:115px;
  width:127px;
  height:34px;
  display:flex;
}
#u1215 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1215_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1216_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:152px;
  height:34px;
}
#u1216 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:115px;
  width:152px;
  height:34px;
  display:flex;
}
#u1216 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1216_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1217_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:34px;
}
#u1217 {
  border-width:0px;
  position:absolute;
  left:502px;
  top:115px;
  width:202px;
  height:34px;
  display:flex;
}
#u1217 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1217_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1218_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:34px;
}
#u1218 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:115px;
  width:148px;
  height:34px;
  display:flex;
}
#u1218 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1218_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1219_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:34px;
}
#u1219 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:115px;
  width:292px;
  height:34px;
  display:flex;
}
#u1219 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1219_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1220_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:34px;
}
#u1220 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:149px;
  width:86px;
  height:34px;
  display:flex;
}
#u1220 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1220_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1221_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:34px;
}
#u1221 {
  border-width:0px;
  position:absolute;
  left:86px;
  top:149px;
  width:137px;
  height:34px;
  display:flex;
}
#u1221 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1221_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1222_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:34px;
}
#u1222 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:149px;
  width:127px;
  height:34px;
  display:flex;
}
#u1222 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1222_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1223_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:152px;
  height:34px;
}
#u1223 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:149px;
  width:152px;
  height:34px;
  display:flex;
}
#u1223 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1223_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1224_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:34px;
}
#u1224 {
  border-width:0px;
  position:absolute;
  left:502px;
  top:149px;
  width:202px;
  height:34px;
  display:flex;
}
#u1224 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1224_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1225_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:34px;
}
#u1225 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:149px;
  width:148px;
  height:34px;
  display:flex;
}
#u1225 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1225_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1226_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:34px;
}
#u1226 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:149px;
  width:292px;
  height:34px;
  display:flex;
}
#u1226 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1226_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1227_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:34px;
}
#u1227 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:183px;
  width:86px;
  height:34px;
  display:flex;
}
#u1227 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1227_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1228_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:34px;
}
#u1228 {
  border-width:0px;
  position:absolute;
  left:86px;
  top:183px;
  width:137px;
  height:34px;
  display:flex;
}
#u1228 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1228_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1229_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:34px;
}
#u1229 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:183px;
  width:127px;
  height:34px;
  display:flex;
}
#u1229 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1229_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1230_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:152px;
  height:34px;
}
#u1230 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:183px;
  width:152px;
  height:34px;
  display:flex;
}
#u1230 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1230_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1231_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:34px;
}
#u1231 {
  border-width:0px;
  position:absolute;
  left:502px;
  top:183px;
  width:202px;
  height:34px;
  display:flex;
}
#u1231 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1231_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1232_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:34px;
}
#u1232 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:183px;
  width:148px;
  height:34px;
  display:flex;
}
#u1232 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1232_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1233_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:34px;
}
#u1233 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:183px;
  width:292px;
  height:34px;
  display:flex;
}
#u1233 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1233_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1234_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:34px;
}
#u1234 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:217px;
  width:86px;
  height:34px;
  display:flex;
}
#u1234 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1234_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1235_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:34px;
}
#u1235 {
  border-width:0px;
  position:absolute;
  left:86px;
  top:217px;
  width:137px;
  height:34px;
  display:flex;
}
#u1235 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1235_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1236_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:34px;
}
#u1236 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:217px;
  width:127px;
  height:34px;
  display:flex;
}
#u1236 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1236_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1237_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:152px;
  height:34px;
}
#u1237 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:217px;
  width:152px;
  height:34px;
  display:flex;
}
#u1237 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1237_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1238_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:34px;
}
#u1238 {
  border-width:0px;
  position:absolute;
  left:502px;
  top:217px;
  width:202px;
  height:34px;
  display:flex;
}
#u1238 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1238_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1239_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:34px;
}
#u1239 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:217px;
  width:148px;
  height:34px;
  display:flex;
}
#u1239 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1239_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1240_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:34px;
}
#u1240 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:217px;
  width:292px;
  height:34px;
  display:flex;
}
#u1240 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1240_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1241_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:34px;
}
#u1241 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:251px;
  width:86px;
  height:34px;
  display:flex;
}
#u1241 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1241_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1242_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:34px;
}
#u1242 {
  border-width:0px;
  position:absolute;
  left:86px;
  top:251px;
  width:137px;
  height:34px;
  display:flex;
}
#u1242 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1242_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1243_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:34px;
}
#u1243 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:251px;
  width:127px;
  height:34px;
  display:flex;
}
#u1243 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1243_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1244_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:152px;
  height:34px;
}
#u1244 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:251px;
  width:152px;
  height:34px;
  display:flex;
}
#u1244 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1244_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1245_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:34px;
}
#u1245 {
  border-width:0px;
  position:absolute;
  left:502px;
  top:251px;
  width:202px;
  height:34px;
  display:flex;
}
#u1245 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1245_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1246_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:34px;
}
#u1246 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:251px;
  width:148px;
  height:34px;
  display:flex;
}
#u1246 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1246_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1247_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:34px;
}
#u1247 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:251px;
  width:292px;
  height:34px;
  display:flex;
}
#u1247 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1247_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1248_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:34px;
}
#u1248 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:285px;
  width:86px;
  height:34px;
  display:flex;
}
#u1248 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1248_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1249_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:34px;
}
#u1249 {
  border-width:0px;
  position:absolute;
  left:86px;
  top:285px;
  width:137px;
  height:34px;
  display:flex;
}
#u1249 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1249_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1250_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:34px;
}
#u1250 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:285px;
  width:127px;
  height:34px;
  display:flex;
}
#u1250 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1250_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1251_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:152px;
  height:34px;
}
#u1251 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:285px;
  width:152px;
  height:34px;
  display:flex;
}
#u1251 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1251_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1252_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:34px;
}
#u1252 {
  border-width:0px;
  position:absolute;
  left:502px;
  top:285px;
  width:202px;
  height:34px;
  display:flex;
}
#u1252 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1252_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1253_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:34px;
}
#u1253 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:285px;
  width:148px;
  height:34px;
  display:flex;
}
#u1253 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1253_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1254_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:34px;
}
#u1254 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:285px;
  width:292px;
  height:34px;
  display:flex;
}
#u1254 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1254_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1255_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:34px;
}
#u1255 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:319px;
  width:86px;
  height:34px;
  display:flex;
}
#u1255 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1255_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1256_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:34px;
}
#u1256 {
  border-width:0px;
  position:absolute;
  left:86px;
  top:319px;
  width:137px;
  height:34px;
  display:flex;
}
#u1256 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1256_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1257_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:34px;
}
#u1257 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:319px;
  width:127px;
  height:34px;
  display:flex;
}
#u1257 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1257_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1258_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:152px;
  height:34px;
}
#u1258 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:319px;
  width:152px;
  height:34px;
  display:flex;
}
#u1258 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1258_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1259_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:34px;
}
#u1259 {
  border-width:0px;
  position:absolute;
  left:502px;
  top:319px;
  width:202px;
  height:34px;
  display:flex;
}
#u1259 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1259_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1260_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:34px;
}
#u1260 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:319px;
  width:148px;
  height:34px;
  display:flex;
}
#u1260 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1260_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1261_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:34px;
}
#u1261 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:319px;
  width:292px;
  height:34px;
  display:flex;
}
#u1261 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1261_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1262_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:86px;
  height:34px;
}
#u1262 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:353px;
  width:86px;
  height:34px;
  display:flex;
}
#u1262 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1262_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1263_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:34px;
}
#u1263 {
  border-width:0px;
  position:absolute;
  left:86px;
  top:353px;
  width:137px;
  height:34px;
  display:flex;
}
#u1263 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1263_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1264_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:127px;
  height:34px;
}
#u1264 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:353px;
  width:127px;
  height:34px;
  display:flex;
}
#u1264 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1264_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1265_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:152px;
  height:34px;
}
#u1265 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:353px;
  width:152px;
  height:34px;
  display:flex;
}
#u1265 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1265_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1266_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:202px;
  height:34px;
}
#u1266 {
  border-width:0px;
  position:absolute;
  left:502px;
  top:353px;
  width:202px;
  height:34px;
  display:flex;
}
#u1266 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1266_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1267_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:148px;
  height:34px;
}
#u1267 {
  border-width:0px;
  position:absolute;
  left:704px;
  top:353px;
  width:148px;
  height:34px;
  display:flex;
}
#u1267 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1267_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1268_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:292px;
  height:34px;
}
#u1268 {
  border-width:0px;
  position:absolute;
  left:852px;
  top:353px;
  width:292px;
  height:34px;
  display:flex;
}
#u1268 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1268_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1269_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1269 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:19px;
  width:70px;
  height:25px;
  display:flex;
}
#u1269 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1269_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1270_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1270 {
  border-width:0px;
  position:absolute;
  left:282px;
  top:19px;
  width:70px;
  height:25px;
  display:flex;
}
#u1270 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1270_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1271_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:123px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u1271 {
  border-width:0px;
  position:absolute;
  left:348px;
  top:13px;
  width:123px;
  height:29px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u1271 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1271_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1272_input {
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:29px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1272_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:29px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1272_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u1272 {
  border-width:0px;
  position:absolute;
  left:89px;
  top:13px;
  width:140px;
  height:29px;
  display:flex;
  color:#AAAAAA;
}
#u1272 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1272_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:29px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u1272.disabled {
}
.u1272_input_option {
  color:#AAAAAA;
}
#u1274_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1274 {
  border-width:0px;
  position:absolute;
  left:499px;
  top:19px;
  width:70px;
  height:25px;
  display:flex;
}
#u1274 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1274_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1275_input {
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:29px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1275_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:29px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1275_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u1275 {
  border-width:0px;
  position:absolute;
  left:569px;
  top:13px;
  width:140px;
  height:29px;
  display:flex;
  color:#AAAAAA;
}
#u1275 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1275_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:140px;
  height:29px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u1275.disabled {
}
.u1275_input_option {
  color:#AAAAAA;
}
#u1277_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:168px;
  height:19px;
}
#u1277 {
  border-width:0px;
  position:absolute;
  left:341px;
  top:142px;
  width:168px;
  height:19px;
  display:flex;
  font-size:14px;
  color:#28A9FF;
}
#u1277 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1277_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1278_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:19px;
}
#u1278 {
  border-width:0px;
  position:absolute;
  left:1220px;
  top:108px;
  width:71px;
  height:19px;
  display:flex;
  font-size:14px;
  color:#28A9FF;
}
#u1278 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1278_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1279 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1280_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1280 {
  border-width:0px;
  position:absolute;
  left:709px;
  top:134px;
  width:106px;
  height:35px;
  display:flex;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1280 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1280_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1281_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:19px;
}
#u1281 {
  border-width:0px;
  position:absolute;
  left:815px;
  top:140px;
  width:71px;
  height:19px;
  display:flex;
  font-size:14px;
  color:#28A9FF;
}
#u1281 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1281_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1282 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1283_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1283 {
  border-width:0px;
  position:absolute;
  left:1116px;
  top:132px;
  width:106px;
  height:35px;
  display:flex;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1283 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1283_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1284_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:130px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1284 {
  border-width:0px;
  position:absolute;
  left:1222px;
  top:137px;
  width:130px;
  height:25px;
  display:flex;
}
#u1284 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1284_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1285_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1285 {
  border-width:0px;
  position:absolute;
  left:618px;
  top:104px;
  width:29px;
  height:25px;
  display:flex;
}
#u1285 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1285_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1286 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1287_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1287 {
  border-width:0px;
  position:absolute;
  left:742px;
  top:104px;
  width:72px;
  height:35px;
  display:flex;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1287 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1287_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1288_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1288 {
  border-width:0px;
  position:absolute;
  left:822px;
  top:107px;
  width:42px;
  height:19px;
  display:flex;
}
#u1288 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1288_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1289 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1290_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1290 {
  border-width:0px;
  position:absolute;
  left:927px;
  top:102px;
  width:72px;
  height:35px;
  display:flex;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1290 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1290_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1291_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1291 {
  border-width:0px;
  position:absolute;
  left:1007px;
  top:105px;
  width:70px;
  height:25px;
  display:flex;
}
#u1291 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1291_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1292 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1293_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1293 {
  border-width:0px;
  position:absolute;
  left:894px;
  top:137px;
  width:106px;
  height:35px;
  display:flex;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1293 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1293_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1294_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1294 {
  border-width:0px;
  position:absolute;
  left:1007px;
  top:139px;
  width:56px;
  height:25px;
  display:flex;
}
#u1294 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1294_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1295 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1296_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:72px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1296 {
  border-width:0px;
  position:absolute;
  left:255px;
  top:99px;
  width:72px;
  height:35px;
  display:flex;
  font-family:'Microsoft Tai Le';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(0, 0, 0, 0.847058823529412);
  text-align:right;
  line-height:22px;
}
#u1296 .text {
  position:absolute;
  align-self:flex-start;
  padding:5px 0px 8px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1296_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1297_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:19px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1297 {
  border-width:0px;
  position:absolute;
  left:335px;
  top:102px;
  width:38px;
  height:19px;
  display:flex;
}
#u1297 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1297_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
