﻿<!DOCTYPE html>
<html>
  <head>
    <title>策略说明新增</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/策略说明新增/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/策略说明新增/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- 策略说明 (组合) -->
      <div id="u14432" class="ax_default" data-label="策略说明" data-left="434" data-top="162" data-width="776" data-height="308">

        <!-- 编辑 (组合) -->
        <div id="u14433" class="ax_default" data-label="编辑" data-left="434" data-top="162" data-width="776" data-height="308">

          <!-- 主体框 (矩形) -->
          <div id="u14434" class="ax_default box_3" data-label="主体框">
            <img id="u14434_img" class="img " src="images/用户属性管理/主体框_u11646.svg"/>
            <div id="u14434_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u14435" class="ax_default box_3">
            <img id="u14435_img" class="img " src="images/审批通知模板/u278.svg"/>
            <div id="u14435_text" class="text ">
              <p><span>&nbsp;&nbsp; 新增策略说明</span></p>
            </div>
          </div>

          <!-- 按钮 (组合) -->
          <div id="u14436" class="ax_default" data-label="按钮" data-left="434" data-top="424" data-width="775" data-height="38">

            <!-- Unnamed (矩形) -->
            <div id="u14437" class="ax_default box_1">
              <div id="u14437_div" class=""></div>
              <div id="u14437_text" class="text ">
                <p><span>取消</span></p>
              </div>
            </div>

            <!-- Unnamed (线段) -->
            <div id="u14438" class="ax_default line">
              <img id="u14438_img" class="img " src="images/审批通知模板/u310.svg"/>
              <div id="u14438_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u14439" class="ax_default box_1">
              <div id="u14439_div" class=""></div>
              <div id="u14439_text" class="text ">
                <p><span>确认</span></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u14440" class="ax_default" data-left="526" data-top="214" data-width="604" data-height="176">

            <!-- Unnamed (矩形) -->
            <div id="u14441" class="ax_default box_1">
              <div id="u14441_div" class=""></div>
              <div id="u14441_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- 多行-限制长度 (文本域) -->
            <div id="u14442" class="ax_default text_area" data-label="多行-限制长度">
              <div id="u14442_div" class=""></div>
              <textarea id="u14442_input" class="u14442_input"></textarea>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u14443" class="ax_default label">
              <div id="u14443_div" class=""></div>
              <div id="u14443_text" class="text ">
                <p><span>/256</span></p>
              </div>
            </div>

            <!-- 长文本 计数 (矩形) -->
            <div id="u14444" class="ax_default label" data-label="长文本 计数">
              <div id="u14444_div" class=""></div>
              <div id="u14444_text" class="text ">
                <p><span>0</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
