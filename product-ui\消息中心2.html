﻿<!DOCTYPE html>
<html>
  <head>
    <title>消息中心2</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/消息中心2/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/消息中心2/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (母版) -->
      <div id="u3445" style="display:none; visibility:hidden;"></div>

      <!-- Unnamed (图片 ) -->
      <div id="u3446" class="ax_default _图片_">
        <img id="u3446_img" class="img " src="images/审批通知模板/u144.png"/>
        <div id="u3446_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (图片 ) -->
      <div id="u3447" class="ax_default _图片_">
        <img id="u3447_img" class="img " src="images/审批通知模板/u145.png"/>
        <div id="u3447_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>

      <!-- Unnamed (矩形) -->
      <div id="u3448" class="ax_default refs-design-apple">
        <div id="u3448_div" class=""></div>
        <div id="u3448_text" class="text ">
          <p><span>99+</span></p>
        </div>
      </div>

      <!-- 消息提醒 (动态面板) -->
      <div id="u3449" class="ax_default" data-label="消息提醒">
        <div id="u3449_state0" class="panel_state" data-label="消息展开" style="">
          <div id="u3449_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u3450" class="ax_default box_1">
              <div id="u3450_div" class=""></div>
              <div id="u3450_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u3451" class="ax_default label">
              <div id="u3451_div" class=""></div>
              <div id="u3451_text" class="text ">
                <p><span style="color:#0000FF;">XX</span><span style="color:#000000;">条：</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u3452" class="ax_default label">
              <div id="u3452_div" class=""></div>
              <div id="u3452_text" class="text ">
                <p><span>&nbsp;告警提醒类消息</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u3453" class="ax_default _图片_">
              <img id="u3453_img" class="img " src="images/审批通知模板/u150.png"/>
              <div id="u3453_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u3454" class="ax_default label">
              <div id="u3454_div" class=""></div>
              <div id="u3454_text" class="text ">
                <p><span style="color:#0000FF;">XX</span><span style="color:#000000;">条：</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u3455" class="ax_default label">
              <div id="u3455_div" class=""></div>
              <div id="u3455_text" class="text ">
                <p><span>&nbsp;通知提醒类消息</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u3456" class="ax_default _图片_">
              <img id="u3456_img" class="img " src="images/审批通知模板/u154.png"/>
              <div id="u3456_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u3457" class="ax_default label">
              <div id="u3457_div" class=""></div>
              <div id="u3457_text" class="text ">
                <p><span>1. 【审批协同类】 xx时间，您收到一条待审批的告警任务。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u3458" class="ax_default label">
              <div id="u3458_div" class=""></div>
              <div id="u3458_text" class="text ">
                <p><span>2.&nbsp; 【数据治理类】xx时间，您收到一条已退回的告警申请。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u3459" class="ax_default label">
              <div id="u3459_div" class=""></div>
              <div id="u3459_text" class="text ">
                <p><span>点击查看全部&gt;</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u3460" class="ax_default label">
              <div id="u3460_div" class=""></div>
              <div id="u3460_text" class="text ">
                <p><span>忽略</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u3461" class="ax_default label">
              <div id="u3461_div" class=""></div>
              <div id="u3461_text" class="text ">
                <p><span>去处理</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u3462" class="ax_default label">
              <div id="u3462_div" class=""></div>
              <div id="u3462_text" class="text ">
                <p><span>忽略</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u3463" class="ax_default label">
              <div id="u3463_div" class=""></div>
              <div id="u3463_text" class="text ">
                <p><span>去处理</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u3464" class="ax_default label">
              <div id="u3464_div" class=""></div>
              <div id="u3464_text" class="text ">
                <p><span>1. 【审批协同类】 xx时间，xx用户触发一条告警规则，被阻断操作。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u3465" class="ax_default label">
              <div id="u3465_div" class=""></div>
              <div id="u3465_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u3466" class="ax_default label">
              <div id="u3466_div" class=""></div>
              <div id="u3466_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u3467" class="ax_default label">
              <div id="u3467_div" class=""></div>
              <div id="u3467_text" class="text ">
                <p><span>忽略</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u3468" class="ax_default label">
              <div id="u3468_div" class=""></div>
              <div id="u3468_text" class="text ">
                <p><span>去处理</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u3469" class="ax_default label">
              <div id="u3469_div" class=""></div>
              <div id="u3469_text" class="text ">
                <p><span>3.&nbsp; 【数据治理类】xx时间，您收到一条已退回的告警申请。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u3470" class="ax_default label">
              <div id="u3470_div" class=""></div>
              <div id="u3470_text" class="text ">
                <p><span>2. 【审批协同类】 xx时间，xx用户触发一条告警规则，被阻断操作。</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u3471" class="ax_default label">
              <div id="u3471_div" class=""></div>
              <div id="u3471_text" class="text ">
                <p><span>3. 【审批协同类】 xx时间，xx用户触发一条告警规则，被阻断操作。</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
