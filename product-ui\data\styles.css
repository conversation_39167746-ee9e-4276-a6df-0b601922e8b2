﻿.ax_default {
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#333333;
  vertical-align:none;
  text-align:center;
  line-height:25px;
  text-transform:none;
}
._标题2 {
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:24px;
  text-align:left;
}
._形状 {
}
._图片_ {
}
.button {
}
.primary_button {
  color:#FFFFFF;
}
.link_button {
  color:#169BD5;
}
._一级标题 {
  font-family:'Arial Normal', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:32px;
  text-align:left;
  line-height:20px;
}
._二级标题 {
  font-family:'Arial Normal', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:24px;
  text-align:left;
}
._三级标题 {
  font-family:'Arial Normal', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:18px;
  text-align:left;
}
._四级标题 {
  font-family:'Arial Normal', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
._五级标题 {
  font-family:'Arial Normal', 'Arial';
  font-weight:bold;
  font-style:normal;
  text-align:left;
}
._六级标题 {
  font-family:'Arial Normal', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:10px;
  text-align:left;
}
.label {
  font-size:14px;
  text-align:left;
}
.text_field {
  color:#606266;
  text-align:left;
}
._图片 {
}
._文本段落 {
  text-align:left;
}
.line {
}
.text_field1 {
  color:#000000;
  text-align:left;
}
.text_area {
  color:#000000;
  text-align:left;
}
.droplist {
  color:#000000;
  text-align:left;
}
.list_box {
  color:#000000;
  text-align:left;
}
.checkbox {
  text-align:left;
}
.radio_button {
  text-align:left;
}
.tree_node {
  text-align:left;
}
.table_cell {
}
.menu_item {
}
._线段 {
}
._连接 {
}
.sticky_1 {
  text-align:left;
}
.form_hint {
  color:#999999;
}
._流程形状 {
}
.form_disabled {
}
._表单提示 {
  color:#999999;
}
._表单禁用 {
}
.box_1 {
}
.box_2 {
}
.ellipse {
}
.text_link {
  color:#0000FF;
}
.text_link_mouse_over {
}
.text_link_mouse_down {
}
.icon {
}
.image {
}
.line1 {
}
.flow_shape {
}
.heading_2 {
  font-family:'Arial Normal', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:24px;
  text-align:left;
}
.heading_3 {
  font-family:'Arial Normal', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:18px;
  text-align:left;
}
.box_3 {
}
.heading_1 {
  font-family:'Arial Normal', 'Arial';
  font-weight:bold;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
.shape {
}
._原型规范-背景-1ea {
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#9CAFC3;
  text-align:center;
  line-height:normal;
}
.label1 {
  font-size:14px;
  text-align:left;
}
.form_disabled1 {
}
._图片1 {
  color:#000000;
}
.refs-chart-data {
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
}
.shape1 {
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#FFFFFF;
  text-align:center;
  line-height:normal;
}
._36号字_顶部标题、大按钮、弹窗提示主标题_ {
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  text-align:left;
}
._32号字_单行列表内，右方操作说明的文字__） {
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  text-align:left;
}
._28号字_页面备注信息及列表的表头说明文字__） {
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  text-align:left;
}
._24号字_最小说明文本，例如列表时间态、版权信息等） {
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  text-align:left;
}
.image1 {
  color:#000000;
}
.horizontal_line {
}
._次按钮（动作列表） {
  font-family:'Heiti SC Medium', 'Heiti SC';
  font-weight:200;
  font-style:normal;
  font-size:18px;
  color:#333333;
  text-align:center;
}
._退出按钮（动作列表） {
  font-family:'Heiti SC Medium', 'Heiti SC';
  font-weight:200;
  font-style:normal;
  font-size:18px;
  color:#FF3300;
  text-align:center;
}
.vertical_line {
}
.box_11 {
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  color:#FFFFFF;
}
.paragraph {
  text-align:left;
}
._图像 {
  color:#000000;
}
._垂直线 {
}
.paragraph1 {
  text-align:left;
}
.heading_11 {
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
.heading_21 {
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:24px;
  text-align:left;
}
._默认样式 {
}
.label2 {
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:34px;
  color:#000000;
  text-align:left;
}
.placeholder {
}
.table {
}
.iconfont {
  font-family:'iconfont Medium', 'iconfont';
  font-weight:400;
  font-style:normal;
  font-size:18px;
  color:#666666;
}
._新的样式3 {
  font-family:'Microsoft YaHei Bold', 'Microsoft YaHei';
  font-weight:700;
  font-style:normal;
  font-size:15px;
  color:#FFFFFF;
}
._顶部导航栏-鼠标移入 {
  font-family:'Microsoft YaHei Regular', 'Microsoft YaHei';
  font-weight:400;
  font-style:normal;
  font-size:15px;
  color:#3474F0;
}
._顶部导航栏 {
  font-family:'Microsoft YaHei Regular', 'Microsoft YaHei';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#344360;
}
._左侧导航栏选中 {
  font-family:'Microsoft YaHei Bold', 'Microsoft YaHei';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  color:#1670D9;
  text-align:left;
}
._形状1 {
}
._形状2 {
  font-family:'Microsoft YaHei UI Bold', 'Microsoft YaHei UI Regular', 'Microsoft YaHei UI';
  font-weight:700;
  font-style:normal;
  color:#FF0066;
}
.refs-design-apple {
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
}
._形状3 {
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  color:#FF0066;
}
.box_31 {
}
.box_12 {
}
._输入-输入框-边框 {
  color:#FFFFFF;
  text-align:left;
}
._输入-输入框-内容居左 {
  font-family:'Microsoft YaHei Regular', 'Microsoft YaHei';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
  text-align:left;
}
._组件-图片_图标 {
}
._输入-选择器-边框 {
}
._输入-选择器-单选 {
  font-family:'Microsoft YaHei Regular', 'Microsoft YaHei';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
  text-align:left;
  line-height:30px;
}
._单选框-默认-文字 {
  font-family:'Microsoft YaHei Regular', 'Microsoft YaHei';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
  text-align:left;
}
._单选框-默认-选框 {
  color:#228EF8;
}
.paragraph2 {
  text-align:left;
}
.image2 {
  color:#000000;
}
._布局-默认折叠导航-2级类目_白底 {
  font-family:'Microsoft YaHei Regular', 'Microsoft YaHei';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
  text-align:left;
  line-height:normal;
}
._布局-默认折叠导航-1级类目_白色 {
  font-family:'Microsoft YaHei Regular', 'Microsoft YaHei';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#999999;
  text-align:left;
}
._图片2 {
}
.box_21 {
}
.text_field2 {
  color:#000000;
  text-align:left;
}
.form_hint1 {
  color:#999999;
}
.form_disabled2 {
}
._图片3 {
}
._输出-表格-列值边框 {
}
._输出-表格- 单行表头有边 {
  font-family:'Microsoft YaHei Regular', 'Microsoft YaHei';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#333333;
  text-align:center;
  line-height:normal;
}
._输出-表格-列值居中有边 {
  font-family:'Microsoft YaHei Regular', 'Microsoft YaHei';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#666666;
  text-align:center;
  line-height:18px;
}
._形状4 {
  font-family:'Microsoft YaHei UI Bold', 'Microsoft YaHei UI Regular', 'Microsoft YaHei UI';
  font-weight:700;
  font-style:normal;
  font-size:12px;
  color:#FF0066;
}
.refs-design-fluent {
  font-family:'Microsoft YaHei Regular', 'Microsoft YaHei';
  font-weight:400;
  font-style:normal;
}
.refs-design-material {
  font-family:'Noto Sans CJK SC Medium', 'Noto Sans CJK SC';
  font-weight:400;
  font-style:normal;
}
.box_13 {
}
.form_disabled3 {
}
.label3 {
  font-size:14px;
  text-align:left;
}
.paragraph3 {
  text-align:left;
}
.box_22 {
}
.box_32 {
}
.box_14 {
}
.table_cell1 {
  font-family:'Arial Normal', 'Arial';
  font-weight:normal;
  font-style:normal;
  font-size:13px;
  color:#000000;
}
.tag {
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#409EFF;
  text-align:center;
}
.tag-success {
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#67C23A;
  text-align:center;
}
.tag-warning {
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#E6A23C;
  text-align:center;
}
.tag-info {
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#606266;
  text-align:center;
}
.tag-danger {
  font-family:'PingFang SC Medium', 'PingFang SC';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:#F56C6C;
  text-align:center;
}
.ellipse1 {
}
.label4 {
  font-size:14px;
  text-align:left;
}
.heading_12 {
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:32px;
  text-align:left;
}
textarea, select, input, button { outline: none; }
