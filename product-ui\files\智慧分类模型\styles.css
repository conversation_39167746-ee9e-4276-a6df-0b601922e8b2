﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:1970px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u1513_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1769px;
  height:878px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-top:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  color:#1890FF;
}
#u1513 {
  border-width:0px;
  position:absolute;
  left:201px;
  top:62px;
  width:1769px;
  height:878px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  color:#1890FF;
}
#u1513 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1513_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1514_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:881px;
  background:inherit;
  background-color:rgba(5, 55, 125, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
}
#u1514 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:59px;
  width:200px;
  height:881px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
}
#u1514 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1514_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1515_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1969px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-left:0px;
  border-top:0px;
  border-bottom:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
}
#u1515 {
  border-width:0px;
  position:absolute;
  left:1px;
  top:0px;
  width:1969px;
  height:60px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
}
#u1515 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1515_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1516_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u1516 {
  border-width:0px;
  position:absolute;
  left:65px;
  top:21px;
  width:120px;
  height:25px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u1516 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1516_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1517_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:34px;
}
#u1517 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:16px;
  width:33px;
  height:34px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
}
#u1517 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1517_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1518 {
  position:absolute;
  left:0px;
  top:61px;
}
#u1518_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:100px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1518_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1519 {
  position:absolute;
  left:0px;
  top:0px;
}
#u1519_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:100px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1519_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1520 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1521 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1522_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u1522 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u1522 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1522_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1523_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u1523 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:23px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1523 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1523_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1524_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u1524 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:23px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1524 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1524_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1525 {
  position:absolute;
  left:0px;
  top:50px;
  visibility:hidden;
}
#u1525_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1525_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1526_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1526 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1526 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1526_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1527_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1527 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1527 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1527_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1528_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1528 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1528 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1528_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1529 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1530_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u1530 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:50px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u1530 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1530_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1531_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u1531 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:73px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1531 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1531_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1532_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u1532 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:73px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1532 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1532_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1533 {
  position:absolute;
  left:0px;
  top:100px;
  visibility:hidden;
}
#u1533_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1533_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1534_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1534 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1534 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1534_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1518_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:150px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1518_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1535 {
  position:absolute;
  left:0px;
  top:0px;
}
#u1535_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:150px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1535_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1536 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1537 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1538_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u1538 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u1538 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1538_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1539_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u1539 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:23px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1539 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1539_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1540_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u1540 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:23px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1540 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1540_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1541 {
  position:absolute;
  left:0px;
  top:50px;
  visibility:hidden;
}
#u1541_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1541_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1542_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1542 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1542 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1542_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1543 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1544_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u1544 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:50px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u1544 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1544_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1545_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u1545 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:73px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1545 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1545_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1546_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u1546 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:73px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1546 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1546_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1547 {
  position:absolute;
  left:0px;
  top:100px;
  visibility:hidden;
}
#u1547_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:80px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1547_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1548_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1548 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1548 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1548_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1549_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1549 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1549 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1549_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1550 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1551_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u1551 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:100px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u1551 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1551_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1552_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u1552 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:123px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1552 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1552_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1553_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u1553 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:123px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1553 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1553_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1554 {
  position:absolute;
  left:0px;
  top:150px;
  visibility:hidden;
}
#u1554_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1554_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1555_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1555 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1555 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1555_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1556_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1556 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1556 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1556_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1557_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1557 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1557 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1557_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1518_state2 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:100px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1518_state2_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1558 {
  position:absolute;
  left:0px;
  top:0px;
}
#u1558_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:100px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1558_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1559 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1560 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1561_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u1561 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u1561 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1561_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1562_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u1562 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:23px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1562 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1562_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1563_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u1563 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:23px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1563 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1563_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1564 {
  position:absolute;
  left:0px;
  top:50px;
  visibility:hidden;
}
#u1564_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:319px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1564_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1565_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1565 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1565 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1565_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1566_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1566 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:79px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1566 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1566_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1567_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1567 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:119px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1567 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1567_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1568_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1568 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1568 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1568_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1569_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1569 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:159px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1569 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1569_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1570_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1570 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:199px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1570 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1570_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1571_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1571 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:239px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1571 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1571_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1572_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1572 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:279px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1572 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1572_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1573 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1574_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u1574 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:50px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u1574 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1574_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1575_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u1575 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:73px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1575 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1575_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1576_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u1576 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:73px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1576 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1576_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1577 {
  position:absolute;
  left:0px;
  top:100px;
  visibility:hidden;
}
#u1577_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:159px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1577_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1578_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1578 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1578 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1578_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1579_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1579 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1579 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1579_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1580_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1580 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1580 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1580_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1581_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1581 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:119px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1581 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1581_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1518_state3 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:250px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1518_state3_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1582 {
  position:absolute;
  left:0px;
  top:0px;
}
#u1582_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:250px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1582_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1583 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1584 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1585_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u1585 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u1585 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1585_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1586_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u1586 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:23px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1586 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1586_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1587_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u1587 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:23px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1587 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1587_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1588 {
  position:absolute;
  left:0px;
  top:50px;
  visibility:hidden;
}
#u1588_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:239px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1588_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1589_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1589 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1589 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1589_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1590_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1590 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:79px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1590 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1590_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1591_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1591 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:119px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1591 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1591_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1592_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1592 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:159px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1592 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1592_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1593_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1593 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1593 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1593_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1594_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1594 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:199px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1594 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1594_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1595 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1596_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u1596 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:50px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u1596 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1596_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1597_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u1597 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:73px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1597 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1597_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1598_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u1598 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:73px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1598 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1598_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1599 {
  position:absolute;
  left:0px;
  top:100px;
  visibility:hidden;
}
#u1599_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1599_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1600_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1600 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1600 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1600_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1601_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1601 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1601 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1601_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1602_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1602 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1602 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1602_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1603 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1604_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u1604 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:100px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u1604 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1604_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1605_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u1605 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:123px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1605 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1605_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1606_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u1606 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:123px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1606 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1606_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1607 {
  position:absolute;
  left:0px;
  top:150px;
  visibility:hidden;
}
#u1607_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1607_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1608_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1608 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1608 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1608_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1609 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1610_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u1610 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:150px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u1610 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1610_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1611_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u1611 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:173px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1611 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1611_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1612_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u1612 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:173px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1612 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1612_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1613 {
  position:absolute;
  left:0px;
  top:200px;
  visibility:hidden;
}
#u1613_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1613_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1614_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1614 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1614 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1614_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1615 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1616_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u1616 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:200px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u1616 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1616_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1617_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u1617 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:223px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1617 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1617_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1618_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u1618 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:223px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1618 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1618_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1619 {
  position:absolute;
  left:0px;
  top:250px;
  visibility:hidden;
}
#u1619_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1619_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1620_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1620 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1620 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1620_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1518_state4 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:150px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1518_state4_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1621 {
  position:absolute;
  left:0px;
  top:0px;
}
#u1621_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:150px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1621_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1622 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1623 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1624_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u1624 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u1624 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1624_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1625_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u1625 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:23px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1625 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1625_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1626_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u1626 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:23px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1626 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1626_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1627 {
  position:absolute;
  left:0px;
  top:50px;
  visibility:hidden;
}
#u1627_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:199px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1627_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1628_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1628 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1628 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1628_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1629_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1629 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:79px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1629 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1629_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1630_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1630 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:119px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1630 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1630_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1631_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1631 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1631 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1631_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1632_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1632 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:159px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1632 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1632_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1633 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1634_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u1634 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:50px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u1634 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1634_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1635_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u1635 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:73px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1635 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1635_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1636_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u1636 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:73px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1636 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1636_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1637 {
  position:absolute;
  left:0px;
  top:100px;
  visibility:hidden;
}
#u1637_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:160px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1637_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1638_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1638 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1638 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1638_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1639_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1639 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1639 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1639_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1640_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1640 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1640 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1640_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1641_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1641 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:120px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1641 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1641_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1642 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1643_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u1643 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:100px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u1643 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1643_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1644_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u1644 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:123px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u1644 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1644_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1645_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u1645 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:123px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u1645 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1645_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1646 {
  position:absolute;
  left:0px;
  top:150px;
  visibility:hidden;
}
#u1646_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:80px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1646_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1647_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1647 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1647 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1647_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1648_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1648 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u1648 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u1648_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1649_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1770px;
  height:2px;
}
#u1649 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:60px;
  width:1769px;
  height:1px;
  display:flex;
}
#u1649 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1649_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1650_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:546px;
  height:2px;
}
#u1650 {
  border-width:0px;
  position:absolute;
  left:212px;
  top:50px;
  width:545px;
  height:1px;
  display:flex;
}
#u1650 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1650_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1651_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 231, 237, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u1651 {
  border-width:0px;
  position:absolute;
  left:212px;
  top:16px;
  width:98px;
  height:34px;
  display:flex;
  font-size:16px;
  color:#303133;
}
#u1651 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1651_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 231, 237, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u1651.mouseOver {
}
#u1651_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(64, 158, 255, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u1651.selected {
}
#u1651_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1652_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 231, 237, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u1652 {
  border-width:0px;
  position:absolute;
  left:326px;
  top:16px;
  width:83px;
  height:34px;
  display:flex;
  font-size:16px;
  color:#303133;
}
#u1652 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1652_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 231, 237, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u1652.mouseOver {
}
#u1652_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(64, 158, 255, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u1652.selected {
}
#u1652_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1653_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 231, 237, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u1653 {
  border-width:0px;
  position:absolute;
  left:430px;
  top:16px;
  width:87px;
  height:34px;
  display:flex;
  font-size:16px;
  color:#303133;
}
#u1653 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1653_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 231, 237, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u1653.mouseOver {
}
#u1653_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(64, 158, 255, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u1653.selected {
}
#u1653_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1654_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 231, 237, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u1654 {
  border-width:0px;
  position:absolute;
  left:533px;
  top:16px;
  width:102px;
  height:34px;
  display:flex;
  font-size:16px;
  color:#303133;
}
#u1654 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1654_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 231, 237, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u1654.mouseOver {
}
#u1654_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(64, 158, 255, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u1654.selected {
}
#u1654_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1655_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 231, 237, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u1655 {
  border-width:0px;
  position:absolute;
  left:654px;
  top:16px;
  width:102px;
  height:34px;
  display:flex;
  font-size:16px;
  color:#303133;
}
#u1655 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1655_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 231, 237, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u1655.mouseOver {
}
#u1655_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(64, 158, 255, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u1655.selected {
}
#u1655_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1656_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:32px;
}
#u1656 {
  border-width:0px;
  position:absolute;
  left:1850px;
  top:14px;
  width:32px;
  height:32px;
  display:flex;
}
#u1656 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1656_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1657_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:32px;
}
#u1657 {
  border-width:0px;
  position:absolute;
  left:1923px;
  top:14px;
  width:32px;
  height:32px;
  display:flex;
}
#u1657 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1657_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1658 {
  border-width:0px;
  position:absolute;
  left:1471px;
  top:62px;
  width:498px;
  height:240px;
  visibility:hidden;
}
#u1658_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:498px;
  height:240px;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1658_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1659_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:170px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1659 {
  border-width:0px;
  position:absolute;
  left:4px;
  top:0px;
  width:300px;
  height:170px;
  display:flex;
}
#u1659 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1659_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1660_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1660 {
  border-width:0px;
  position:absolute;
  left:148px;
  top:3px;
  width:47px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1660 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1660_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1661_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1661 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:3px;
  width:102px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1661 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1661_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1662_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u1662 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:0px;
  width:26px;
  height:25px;
  display:flex;
}
#u1662 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1662_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1663 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1664_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1664 {
  border-width:0px;
  position:absolute;
  left:145px;
  top:111px;
  width:47px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1664 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1664_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1665_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1665 {
  border-width:0px;
  position:absolute;
  left:38px;
  top:111px;
  width:102px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1665 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1665_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1666_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:18px;
}
#u1666 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:115px;
  width:21px;
  height:18px;
  display:flex;
}
#u1666 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1666_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1667_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:25px;
  background:inherit;
  background-color:rgba(52, 116, 240, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u1667 {
  border-width:0px;
  position:absolute;
  left:398px;
  top:204px;
  width:93px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u1667 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1667_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1668_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1668 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:28px;
  width:143px;
  height:25px;
  display:flex;
}
#u1668 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1668_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1669_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1669 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:53px;
  width:139px;
  height:25px;
  display:flex;
}
#u1669 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1669_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1670_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1670 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:78px;
  width:139px;
  height:25px;
  display:flex;
}
#u1670 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1670_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1671_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1671 {
  border-width:0px;
  position:absolute;
  left:43px;
  top:141px;
  width:139px;
  height:25px;
  display:flex;
}
#u1671 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1671_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1672_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1672 {
  border-width:0px;
  position:absolute;
  left:43px;
  top:166px;
  width:139px;
  height:25px;
  display:flex;
}
#u1672 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1672_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1673_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1673 {
  border-width:0px;
  position:absolute;
  left:43px;
  top:191px;
  width:139px;
  height:25px;
  display:flex;
}
#u1673 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1673_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1674_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1674 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:28px;
  width:9px;
  height:25px;
  display:flex;
}
#u1674 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1674_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1658_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:498px;
  height:240px;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u1658_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1675_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:170px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1675 {
  border-width:0px;
  position:absolute;
  left:4px;
  top:0px;
  width:300px;
  height:170px;
  display:flex;
}
#u1675 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1675_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1676_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1676 {
  border-width:0px;
  position:absolute;
  left:154px;
  top:8px;
  width:47px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1676 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1676_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1677_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1677 {
  border-width:0px;
  position:absolute;
  left:47px;
  top:8px;
  width:102px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1677 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1677_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1678_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u1678 {
  border-width:0px;
  position:absolute;
  left:21px;
  top:5px;
  width:26px;
  height:25px;
  display:flex;
}
#u1678 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1678_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1679_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1679 {
  border-width:0px;
  position:absolute;
  left:147px;
  top:204px;
  width:47px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1679 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1679_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1680_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1680 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:204px;
  width:102px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1680 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1680_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1681_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:25px;
}
#u1681 {
  border-width:0px;
  position:absolute;
  left:21px;
  top:204px;
  width:21px;
  height:25px;
  display:flex;
}
#u1681 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1681_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1682_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:147px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1682 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:33px;
  width:147px;
  height:25px;
  display:flex;
}
#u1682 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1682_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1683_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1683 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:138px;
  width:139px;
  height:25px;
  display:flex;
}
#u1683 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1683_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1684_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1684 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:163px;
  width:139px;
  height:25px;
  display:flex;
}
#u1684 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1684_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1685_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1685 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:232px;
  width:139px;
  height:25px;
  display:flex;
}
#u1685 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1685_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1686_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1686 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:312px;
  width:139px;
  height:25px;
  display:flex;
}
#u1686 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1686_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1687_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1687 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:337px;
  width:139px;
  height:25px;
  display:flex;
}
#u1687 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1687_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1688_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1688 {
  border-width:0px;
  position:absolute;
  left:54px;
  top:33px;
  width:15px;
  height:25px;
  display:flex;
}
#u1688 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1688_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1689_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:276px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u1689 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:60px;
  width:276px;
  height:25px;
  display:flex;
  color:#000000;
}
#u1689 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1689_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1690_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:312px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u1690 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:85px;
  width:312px;
  height:25px;
  display:flex;
  color:#000000;
}
#u1690 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1690_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1691_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u1691 {
  border-width:0px;
  position:absolute;
  left:82px;
  top:113px;
  width:29px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u1691 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1691_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1692_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(52, 116, 240, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u1692 {
  border-width:0px;
  position:absolute;
  left:374px;
  top:383px;
  width:98px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u1692 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1692_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1693_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u1693 {
  border-width:0px;
  position:absolute;
  left:357px;
  top:60px;
  width:22px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u1693 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1693_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1694_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u1694 {
  border-width:0px;
  position:absolute;
  left:395px;
  top:60px;
  width:33px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u1694 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1694_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1695_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u1695 {
  border-width:0px;
  position:absolute;
  left:357px;
  top:85px;
  width:22px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u1695 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1695_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1696_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u1696 {
  border-width:0px;
  position:absolute;
  left:395px;
  top:85px;
  width:33px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u1696 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1696_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1697_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:276px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u1697 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:257px;
  width:276px;
  height:25px;
  display:flex;
  color:#000000;
}
#u1697 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1697_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1698_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u1698 {
  border-width:0px;
  position:absolute;
  left:357px;
  top:257px;
  width:1px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u1698 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1698_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u1699_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u1699 {
  border-width:0px;
  position:absolute;
  left:79px;
  top:287px;
  width:29px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u1699 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1699_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1700_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u1700 {
  border-width:0px;
  position:absolute;
  left:357px;
  top:257px;
  width:1px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u1700 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1700_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u1701_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 59, 48, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#FFFFFF;
}
#u1701 {
  border-width:0px;
  position:absolute;
  left:1873px;
  top:9px;
  width:27px;
  height:21px;
  display:flex;
  font-size:12px;
  color:#FFFFFF;
}
#u1701 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1701_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1702 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1703_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:400px;
  height:230px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.00784313725490196);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 218, 226, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1703 {
  border-width:0px;
  position:absolute;
  left:1568px;
  top:62px;
  width:400px;
  height:230px;
  display:flex;
}
#u1703 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1703_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1704_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  color:#606266;
}
#u1704 {
  border-width:0px;
  position:absolute;
  left:1620px;
  top:112px;
  width:329px;
  height:25px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  color:#606266;
}
#u1704 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1704_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1705_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:39px;
}
#u1705 {
  border-width:0px;
  position:absolute;
  left:1751px;
  top:73px;
  width:40px;
  height:39px;
  display:flex;
}
#u1705 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1705_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1706_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u1706 {
  border-width:0px;
  position:absolute;
  left:1634px;
  top:160px;
  width:30px;
  height:25px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u1706 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1706_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1707_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u1707 {
  border-width:0px;
  position:absolute;
  left:1775px;
  top:160px;
  width:25px;
  height:25px;
  display:flex;
  font-size:12px;
}
#u1707 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1707_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1708_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u1708 {
  border-width:0px;
  position:absolute;
  left:1809px;
  top:160px;
  width:114px;
  height:25px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u1708 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1708_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1709_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u1709 {
  border-width:0px;
  position:absolute;
  left:1602px;
  top:160px;
  width:25px;
  height:25px;
  display:flex;
  font-size:12px;
}
#u1709 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1709_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1710_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u1710 {
  border-width:0px;
  position:absolute;
  left:1602px;
  top:200px;
  width:25px;
  height:25px;
  display:flex;
  font-size:12px;
}
#u1710 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1710_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1711_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u1711 {
  border-width:0px;
  position:absolute;
  left:1775px;
  top:200px;
  width:25px;
  height:25px;
  display:flex;
  font-size:12px;
}
#u1711 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1711_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1712_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u1712 {
  border-width:0px;
  position:absolute;
  left:1602px;
  top:238px;
  width:25px;
  height:25px;
  display:flex;
  font-size:12px;
}
#u1712 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1712_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1713_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u1713 {
  border-width:0px;
  position:absolute;
  left:1775px;
  top:238px;
  width:25px;
  height:25px;
  display:flex;
  font-size:12px;
}
#u1713 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1713_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1714_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u1714 {
  border-width:0px;
  position:absolute;
  left:1873px;
  top:243px;
  width:20px;
  height:20px;
  display:flex;
  font-size:12px;
}
#u1714 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1714_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1715_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u1715 {
  border-width:0px;
  position:absolute;
  left:1809px;
  top:240px;
  width:48px;
  height:25px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u1715 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1715_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1716_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u1716 {
  border-width:0px;
  position:absolute;
  left:1809px;
  top:200px;
  width:66px;
  height:25px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u1716 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1716_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1717_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u1717 {
  border-width:0px;
  position:absolute;
  left:1635px;
  top:200px;
  width:36px;
  height:25px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u1717 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1717_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1718_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u1718 {
  border-width:0px;
  position:absolute;
  left:1634px;
  top:238px;
  width:48px;
  height:25px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u1718 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1718_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1719_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:401px;
  height:2px;
}
#u1719 {
  border-width:0px;
  position:absolute;
  left:1569px;
  top:142px;
  width:400px;
  height:1px;
  display:flex;
  opacity:0.64;
  color:rgba(153, 144, 144, 0.313725490196078);
}
#u1719 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1719_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1720_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1291px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1720 {
  border-width:0px;
  position:absolute;
  left:233px;
  top:105px;
  width:1291px;
  height:60px;
  display:flex;
}
#u1720 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1720_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1721_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:30px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
}
#u1721 {
  border-width:0px;
  position:absolute;
  left:835px;
  top:124px;
  width:63px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
}
#u1721 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1721_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1722_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#000000;
}
#u1722 {
  border-width:0px;
  position:absolute;
  left:907px;
  top:124px;
  width:56px;
  height:30px;
  display:flex;
  font-size:16px;
  color:#000000;
}
#u1722 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1722_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1723_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1723 {
  border-width:0px;
  position:absolute;
  left:255px;
  top:114px;
  width:1px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1723 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1723_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u1724_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1724 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:123px;
  width:80px;
  height:25px;
  display:flex;
  font-size:16px;
}
#u1724 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1724_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1725_input {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:28px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1725_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:28px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1725_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u1725 {
  border-width:0px;
  position:absolute;
  left:326px;
  top:123px;
  width:193px;
  height:28px;
  display:flex;
  color:#AAAAAA;
}
#u1725 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1725_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:28px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u1725.disabled {
}
#u1726_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1291px;
  height:700px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1726 {
  border-width:0px;
  position:absolute;
  left:233px;
  top:215px;
  width:1291px;
  height:700px;
  display:flex;
}
#u1726 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1726_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1727_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u1727 {
  border-width:0px;
  position:absolute;
  left:1231px;
  top:304px;
  width:28px;
  height:25px;
  display:flex;
  color:#1890FF;
}
#u1727 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1727_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1728 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1729_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1248px;
  height:53px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1729 {
  border-width:0px;
  position:absolute;
  left:233px;
  top:862px;
  width:1248px;
  height:53px;
  display:flex;
}
#u1729 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1729_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1730_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1730 {
  border-width:0px;
  position:absolute;
  left:255px;
  top:876px;
  width:80px;
  height:25px;
  display:flex;
  font-size:16px;
}
#u1730 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1730_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1731_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:135px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1731 {
  border-width:0px;
  position:absolute;
  left:1256px;
  top:875px;
  width:135px;
  height:25px;
  display:flex;
  font-size:16px;
}
#u1731 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1731_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1732_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:left;
}
#u1732 {
  border-width:0px;
  position:absolute;
  left:339px;
  top:871px;
  width:96px;
  height:35px;
  display:flex;
  text-align:left;
}
#u1732 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1732_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1733_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:15px;
}
#u1733 {
  border-width:0px;
  position:absolute;
  left:415px;
  top:881px;
  width:15px;
  height:15px;
  display:flex;
}
#u1733 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1733_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1734_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u1734 {
  border-width:0px;
  position:absolute;
  left:1385px;
  top:877px;
  width:25px;
  height:25px;
  display:flex;
}
#u1734 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1734_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1735_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(24, 144, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u1735 {
  border-width:0px;
  position:absolute;
  left:1416px;
  top:875px;
  width:28px;
  height:27px;
  display:flex;
  color:#1890FF;
}
#u1735 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1735_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1736_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u1736 {
  border-width:0px;
  position:absolute;
  left:1456px;
  top:878px;
  width:25px;
  height:25px;
  display:flex;
}
#u1736 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1736_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1737_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u1737 {
  border-width:0px;
  position:absolute;
  left:1278px;
  top:466px;
  width:28px;
  height:25px;
  display:flex;
  color:#1890FF;
}
#u1737 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1737_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1738_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u1738 {
  border-width:0px;
  position:absolute;
  left:1275px;
  top:359px;
  width:28px;
  height:25px;
  display:flex;
  color:#1890FF;
}
#u1738 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1738_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1739_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u1739 {
  border-width:0px;
  position:absolute;
  left:1278px;
  top:410px;
  width:28px;
  height:25px;
  display:flex;
  color:#1890FF;
}
#u1739 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1739_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1740_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u1740 {
  border-width:0px;
  position:absolute;
  left:1275px;
  top:305px;
  width:28px;
  height:25px;
  display:flex;
  color:#1890FF;
}
#u1740 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1740_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1741_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u1741 {
  border-width:0px;
  position:absolute;
  left:1231px;
  top:359px;
  width:28px;
  height:25px;
  display:flex;
  color:#1890FF;
}
#u1741 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1741_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1742_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u1742 {
  border-width:0px;
  position:absolute;
  left:1231px;
  top:409px;
  width:28px;
  height:25px;
  display:flex;
  color:#1890FF;
}
#u1742 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1742_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1743_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u1743 {
  border-width:0px;
  position:absolute;
  left:1231px;
  top:467px;
  width:28px;
  height:25px;
  display:flex;
  color:#1890FF;
}
#u1743 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1743_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1744_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u1744 {
  border-width:0px;
  position:absolute;
  left:241px;
  top:175px;
  width:54px;
  height:27px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u1744 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1744_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1745 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:228px;
  width:1284px;
  height:612px;
}
#u1745_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1284px;
  height:612px;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1745_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1746 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1256px;
  height:502px;
}
#u1747_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:52px;
}
#u1747 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1747 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1747_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1748_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:52px;
}
#u1748 {
  border-width:0px;
  position:absolute;
  left:38px;
  top:0px;
  width:96px;
  height:52px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  line-height:24px;
}
#u1748 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1748_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1749_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:52px;
}
#u1749 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:0px;
  width:89px;
  height:52px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  line-height:24px;
}
#u1749 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1749_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1750_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:52px;
}
#u1750 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:0px;
  width:99px;
  height:52px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  line-height:24px;
}
#u1750 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1750_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1751_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:52px;
}
#u1751 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:0px;
  width:79px;
  height:52px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  line-height:24px;
}
#u1751 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1751_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1752_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:52px;
}
#u1752 {
  border-width:0px;
  position:absolute;
  left:401px;
  top:0px;
  width:81px;
  height:52px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  line-height:24px;
}
#u1752 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1752_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1753_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:52px;
}
#u1753 {
  border-width:0px;
  position:absolute;
  left:482px;
  top:0px;
  width:96px;
  height:52px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  line-height:24px;
}
#u1753 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1753_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1754_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:52px;
}
#u1754 {
  border-width:0px;
  position:absolute;
  left:578px;
  top:0px;
  width:89px;
  height:52px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  line-height:24px;
}
#u1754 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1754_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1755_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:52px;
}
#u1755 {
  border-width:0px;
  position:absolute;
  left:667px;
  top:0px;
  width:143px;
  height:52px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  line-height:24px;
}
#u1755 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1755_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1756_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:52px;
}
#u1756 {
  border-width:0px;
  position:absolute;
  left:810px;
  top:0px;
  width:89px;
  height:52px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  line-height:24px;
}
#u1756 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1756_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1757_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:52px;
}
#u1757 {
  border-width:0px;
  position:absolute;
  left:899px;
  top:0px;
  width:151px;
  height:52px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  line-height:24px;
}
#u1757 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1757_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1758_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:206px;
  height:52px;
}
#u1758 {
  border-width:0px;
  position:absolute;
  left:1050px;
  top:0px;
  width:206px;
  height:52px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  line-height:24px;
}
#u1758 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1758_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1759_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:52px;
}
#u1759 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:52px;
  width:38px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1759 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1759_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1760_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:52px;
}
#u1760 {
  border-width:0px;
  position:absolute;
  left:38px;
  top:52px;
  width:96px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1760 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1760_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1761_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:52px;
}
#u1761 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:52px;
  width:89px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1761 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1761_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1762_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:52px;
}
#u1762 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:52px;
  width:99px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1762 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1762_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1763_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:52px;
}
#u1763 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:52px;
  width:79px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1763 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1763_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1764_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:52px;
}
#u1764 {
  border-width:0px;
  position:absolute;
  left:401px;
  top:52px;
  width:81px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1764 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1764_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1765_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:52px;
}
#u1765 {
  border-width:0px;
  position:absolute;
  left:482px;
  top:52px;
  width:96px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1765 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1765_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1766_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:52px;
}
#u1766 {
  border-width:0px;
  position:absolute;
  left:578px;
  top:52px;
  width:89px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1766 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1766_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1767_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:52px;
}
#u1767 {
  border-width:0px;
  position:absolute;
  left:667px;
  top:52px;
  width:143px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1767 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1767_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1768_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:52px;
}
#u1768 {
  border-width:0px;
  position:absolute;
  left:810px;
  top:52px;
  width:89px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1768 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1768_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1769_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:52px;
}
#u1769 {
  border-width:0px;
  position:absolute;
  left:899px;
  top:52px;
  width:151px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1769 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1769_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1770_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:206px;
  height:52px;
}
#u1770 {
  border-width:0px;
  position:absolute;
  left:1050px;
  top:52px;
  width:206px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1770 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1770_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1771_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:52px;
}
#u1771 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:104px;
  width:38px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1771 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1771_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1772_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:52px;
}
#u1772 {
  border-width:0px;
  position:absolute;
  left:38px;
  top:104px;
  width:96px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1772 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1772_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1773_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:52px;
}
#u1773 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:104px;
  width:89px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1773 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1773_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1774_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:52px;
}
#u1774 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:104px;
  width:99px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1774 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1774_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1775_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:52px;
}
#u1775 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:104px;
  width:79px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1775 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1775_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1776_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:52px;
}
#u1776 {
  border-width:0px;
  position:absolute;
  left:401px;
  top:104px;
  width:81px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1776 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1776_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1777_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:52px;
}
#u1777 {
  border-width:0px;
  position:absolute;
  left:482px;
  top:104px;
  width:96px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1777 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1777_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1778_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:52px;
}
#u1778 {
  border-width:0px;
  position:absolute;
  left:578px;
  top:104px;
  width:89px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1778 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1778_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1779_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:52px;
}
#u1779 {
  border-width:0px;
  position:absolute;
  left:667px;
  top:104px;
  width:143px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1779 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1779_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1780_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:52px;
}
#u1780 {
  border-width:0px;
  position:absolute;
  left:810px;
  top:104px;
  width:89px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1780 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1780_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1781_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:52px;
}
#u1781 {
  border-width:0px;
  position:absolute;
  left:899px;
  top:104px;
  width:151px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1781 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1781_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1782_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:206px;
  height:52px;
}
#u1782 {
  border-width:0px;
  position:absolute;
  left:1050px;
  top:104px;
  width:206px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1782 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1782_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1783_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:76px;
}
#u1783 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:156px;
  width:38px;
  height:76px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1783 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1783_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1784_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:76px;
}
#u1784 {
  border-width:0px;
  position:absolute;
  left:38px;
  top:156px;
  width:96px;
  height:76px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1784 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1784_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1785_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:76px;
}
#u1785 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:156px;
  width:89px;
  height:76px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1785 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1785_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1786_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:76px;
}
#u1786 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:156px;
  width:99px;
  height:76px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1786 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1786_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1787_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:76px;
}
#u1787 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:156px;
  width:79px;
  height:76px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1787 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1787_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1788_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:76px;
}
#u1788 {
  border-width:0px;
  position:absolute;
  left:401px;
  top:156px;
  width:81px;
  height:76px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1788 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1788_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1789_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:76px;
}
#u1789 {
  border-width:0px;
  position:absolute;
  left:482px;
  top:156px;
  width:96px;
  height:76px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1789 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1789_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1790_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:76px;
}
#u1790 {
  border-width:0px;
  position:absolute;
  left:578px;
  top:156px;
  width:89px;
  height:76px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1790 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1790_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1791_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:76px;
}
#u1791 {
  border-width:0px;
  position:absolute;
  left:667px;
  top:156px;
  width:143px;
  height:76px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1791 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1791_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1792_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:76px;
}
#u1792 {
  border-width:0px;
  position:absolute;
  left:810px;
  top:156px;
  width:89px;
  height:76px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1792 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1792_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1793_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:76px;
}
#u1793 {
  border-width:0px;
  position:absolute;
  left:899px;
  top:156px;
  width:151px;
  height:76px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1793 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1793_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1794_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:206px;
  height:76px;
}
#u1794 {
  border-width:0px;
  position:absolute;
  left:1050px;
  top:156px;
  width:206px;
  height:76px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1794 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1794_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1795_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:54px;
}
#u1795 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:232px;
  width:38px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1795 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1795_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1796_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:54px;
}
#u1796 {
  border-width:0px;
  position:absolute;
  left:38px;
  top:232px;
  width:96px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1796 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1796_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1797_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:54px;
}
#u1797 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:232px;
  width:89px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1797 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1797_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1798_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:54px;
}
#u1798 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:232px;
  width:99px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1798 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1798_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1799_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:54px;
}
#u1799 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:232px;
  width:79px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1799 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1799_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1800_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:54px;
}
#u1800 {
  border-width:0px;
  position:absolute;
  left:401px;
  top:232px;
  width:81px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1800 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1800_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1801_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:54px;
}
#u1801 {
  border-width:0px;
  position:absolute;
  left:482px;
  top:232px;
  width:96px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1801 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1801_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1802_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:54px;
}
#u1802 {
  border-width:0px;
  position:absolute;
  left:578px;
  top:232px;
  width:89px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1802 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1802_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1803_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:54px;
}
#u1803 {
  border-width:0px;
  position:absolute;
  left:667px;
  top:232px;
  width:143px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1803 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1803_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1804_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:54px;
}
#u1804 {
  border-width:0px;
  position:absolute;
  left:810px;
  top:232px;
  width:89px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1804 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1804_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1805_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:54px;
}
#u1805 {
  border-width:0px;
  position:absolute;
  left:899px;
  top:232px;
  width:151px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1805 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1805_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1806_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:206px;
  height:54px;
}
#u1806 {
  border-width:0px;
  position:absolute;
  left:1050px;
  top:232px;
  width:206px;
  height:54px;
  display:flex;
  font-size:14px;
  color:#1890FF;
}
#u1806 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1806_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1807_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:54px;
}
#u1807 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:286px;
  width:38px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1807 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1807_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1808_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:54px;
}
#u1808 {
  border-width:0px;
  position:absolute;
  left:38px;
  top:286px;
  width:96px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1808 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1808_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1809_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:54px;
}
#u1809 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:286px;
  width:89px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1809 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1809_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1810_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:54px;
}
#u1810 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:286px;
  width:99px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1810 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1810_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1811_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:54px;
}
#u1811 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:286px;
  width:79px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1811 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1811_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1812_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:54px;
}
#u1812 {
  border-width:0px;
  position:absolute;
  left:401px;
  top:286px;
  width:81px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1812 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1812_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1813_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:54px;
}
#u1813 {
  border-width:0px;
  position:absolute;
  left:482px;
  top:286px;
  width:96px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1813 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1813_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1814_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:54px;
}
#u1814 {
  border-width:0px;
  position:absolute;
  left:578px;
  top:286px;
  width:89px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1814 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1814_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1815_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:54px;
}
#u1815 {
  border-width:0px;
  position:absolute;
  left:667px;
  top:286px;
  width:143px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1815 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1815_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1816_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:54px;
}
#u1816 {
  border-width:0px;
  position:absolute;
  left:810px;
  top:286px;
  width:89px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1816 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1816_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1817_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:54px;
}
#u1817 {
  border-width:0px;
  position:absolute;
  left:899px;
  top:286px;
  width:151px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1817 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1817_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1818_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:206px;
  height:54px;
}
#u1818 {
  border-width:0px;
  position:absolute;
  left:1050px;
  top:286px;
  width:206px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1818 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1818_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1819_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:54px;
}
#u1819 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:340px;
  width:38px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1819 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1819_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1820_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:54px;
}
#u1820 {
  border-width:0px;
  position:absolute;
  left:38px;
  top:340px;
  width:96px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1820 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1820_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1821_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:54px;
}
#u1821 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:340px;
  width:89px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1821 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1821_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1822_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:54px;
}
#u1822 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:340px;
  width:99px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1822 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1822_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1823_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:54px;
}
#u1823 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:340px;
  width:79px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1823 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1823_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1824_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:54px;
}
#u1824 {
  border-width:0px;
  position:absolute;
  left:401px;
  top:340px;
  width:81px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1824 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1824_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1825_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:54px;
}
#u1825 {
  border-width:0px;
  position:absolute;
  left:482px;
  top:340px;
  width:96px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1825 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1825_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1826_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:54px;
}
#u1826 {
  border-width:0px;
  position:absolute;
  left:578px;
  top:340px;
  width:89px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1826 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1826_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1827_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:54px;
}
#u1827 {
  border-width:0px;
  position:absolute;
  left:667px;
  top:340px;
  width:143px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1827 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1827_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1828_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:54px;
}
#u1828 {
  border-width:0px;
  position:absolute;
  left:810px;
  top:340px;
  width:89px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1828 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1828_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1829_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:54px;
}
#u1829 {
  border-width:0px;
  position:absolute;
  left:899px;
  top:340px;
  width:151px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1829 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1829_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1830_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:206px;
  height:54px;
}
#u1830 {
  border-width:0px;
  position:absolute;
  left:1050px;
  top:340px;
  width:206px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1830 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1830_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1831_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:54px;
}
#u1831 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:394px;
  width:38px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1831 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1831_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1832_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:54px;
}
#u1832 {
  border-width:0px;
  position:absolute;
  left:38px;
  top:394px;
  width:96px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1832 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1832_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1833_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:54px;
}
#u1833 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:394px;
  width:89px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1833 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1833_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1834_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:54px;
}
#u1834 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:394px;
  width:99px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1834 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1834_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1835_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:54px;
}
#u1835 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:394px;
  width:79px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1835 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1835_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1836_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:54px;
}
#u1836 {
  border-width:0px;
  position:absolute;
  left:401px;
  top:394px;
  width:81px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1836 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1836_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1837_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:54px;
}
#u1837 {
  border-width:0px;
  position:absolute;
  left:482px;
  top:394px;
  width:96px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1837 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1837_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1838_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:54px;
}
#u1838 {
  border-width:0px;
  position:absolute;
  left:578px;
  top:394px;
  width:89px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1838 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1838_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1839_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:54px;
}
#u1839 {
  border-width:0px;
  position:absolute;
  left:667px;
  top:394px;
  width:143px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1839 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1839_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1840_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:54px;
}
#u1840 {
  border-width:0px;
  position:absolute;
  left:810px;
  top:394px;
  width:89px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1840 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1840_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1841_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:54px;
}
#u1841 {
  border-width:0px;
  position:absolute;
  left:899px;
  top:394px;
  width:151px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1841 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1841_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1842_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:206px;
  height:54px;
}
#u1842 {
  border-width:0px;
  position:absolute;
  left:1050px;
  top:394px;
  width:206px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1842 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1842_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1843_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:54px;
}
#u1843 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:448px;
  width:38px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1843 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1843_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1844_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:54px;
}
#u1844 {
  border-width:0px;
  position:absolute;
  left:38px;
  top:448px;
  width:96px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1844 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1844_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1845_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:54px;
}
#u1845 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:448px;
  width:89px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1845 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1845_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1846_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:99px;
  height:54px;
}
#u1846 {
  border-width:0px;
  position:absolute;
  left:223px;
  top:448px;
  width:99px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1846 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1846_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1847_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:79px;
  height:54px;
}
#u1847 {
  border-width:0px;
  position:absolute;
  left:322px;
  top:448px;
  width:79px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1847 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1847_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1848_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:81px;
  height:54px;
}
#u1848 {
  border-width:0px;
  position:absolute;
  left:401px;
  top:448px;
  width:81px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u1848 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1848_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1849_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:54px;
}
#u1849 {
  border-width:0px;
  position:absolute;
  left:482px;
  top:448px;
  width:96px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1849 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1849_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1850_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:54px;
}
#u1850 {
  border-width:0px;
  position:absolute;
  left:578px;
  top:448px;
  width:89px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1850 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1850_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1851_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:54px;
}
#u1851 {
  border-width:0px;
  position:absolute;
  left:667px;
  top:448px;
  width:143px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1851 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1851_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1852_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:54px;
}
#u1852 {
  border-width:0px;
  position:absolute;
  left:810px;
  top:448px;
  width:89px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1852 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1852_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1853_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:151px;
  height:54px;
}
#u1853 {
  border-width:0px;
  position:absolute;
  left:899px;
  top:448px;
  width:151px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1853 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1853_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1854_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:206px;
  height:54px;
}
#u1854 {
  border-width:0px;
  position:absolute;
  left:1050px;
  top:448px;
  width:206px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u1854 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u1854_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1855 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u1856 label {
  left:0px;
  width:100%;
}
#u1856_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u1856 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:19px;
  width:30px;
  height:16px;
  display:flex;
}
#u1856 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u1856_img.selected {
}
#u1856.selected {
}
#u1856_img.disabled {
}
#u1856.disabled {
}
#u1856_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1856_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u1857 label {
  left:0px;
  width:100%;
}
#u1857_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u1857 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:72px;
  width:30px;
  height:16px;
  display:flex;
}
#u1857 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u1857_img.selected {
}
#u1857.selected {
}
#u1857_img.disabled {
}
#u1857.disabled {
}
#u1857_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1857_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u1858 label {
  left:0px;
  width:100%;
}
#u1858_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u1858 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:124px;
  width:30px;
  height:16px;
  display:flex;
}
#u1858 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u1858_img.selected {
}
#u1858.selected {
}
#u1858_img.disabled {
}
#u1858.disabled {
}
#u1858_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1858_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u1859 label {
  left:0px;
  width:100%;
}
#u1859_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u1859 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:186px;
  width:30px;
  height:16px;
  display:flex;
}
#u1859 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u1859_img.selected {
}
#u1859.selected {
}
#u1859_img.disabled {
}
#u1859.disabled {
}
#u1859_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1859_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u1860 label {
  left:0px;
  width:100%;
}
#u1860_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u1860 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:253px;
  width:30px;
  height:16px;
  display:flex;
}
#u1860 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u1860_img.selected {
}
#u1860.selected {
}
#u1860_img.disabled {
}
#u1860.disabled {
}
#u1860_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1860_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u1861 label {
  left:0px;
  width:100%;
}
#u1861_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u1861 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:308px;
  width:30px;
  height:16px;
  display:flex;
}
#u1861 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u1861_img.selected {
}
#u1861.selected {
}
#u1861_img.disabled {
}
#u1861.disabled {
}
#u1861_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1861_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u1862 label {
  left:0px;
  width:100%;
}
#u1862_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u1862 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:361px;
  width:30px;
  height:16px;
  display:flex;
}
#u1862 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u1862_img.selected {
}
#u1862.selected {
}
#u1862_img.disabled {
}
#u1862.disabled {
}
#u1862_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1862_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u1863 label {
  left:0px;
  width:100%;
}
#u1863_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u1863 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:414px;
  width:30px;
  height:16px;
  display:flex;
}
#u1863 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u1863_img.selected {
}
#u1863.selected {
}
#u1863_img.disabled {
}
#u1863.disabled {
}
#u1863_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1863_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u1864 label {
  left:0px;
  width:100%;
}
#u1864_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u1864 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:468px;
  width:30px;
  height:16px;
  display:flex;
}
#u1864 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u1864_img.selected {
}
#u1864.selected {
}
#u1864_img.disabled {
}
#u1864.disabled {
}
#u1864_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1864_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u1865_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u1865 {
  border-width:0px;
  position:absolute;
  left:1065px;
  top:68px;
  width:56px;
  height:25px;
  display:flex;
  color:#1890FF;
}
#u1865 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1865_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1866_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u1866 {
  border-width:0px;
  position:absolute;
  left:1201px;
  top:68px;
  width:28px;
  height:25px;
  display:flex;
  color:#1890FF;
}
#u1866 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1866_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1867_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u1867 {
  border-width:0px;
  position:absolute;
  left:1165px;
  top:68px;
  width:28px;
  height:25px;
  display:flex;
  color:#1890FF;
}
#u1867 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1867_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1868_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u1868 {
  border-width:0px;
  position:absolute;
  left:1131px;
  top:68px;
  width:28px;
  height:25px;
  display:flex;
  color:#1890FF;
}
#u1868 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1868_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1869_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u1869 {
  border-width:0px;
  position:absolute;
  left:1065px;
  top:115px;
  width:56px;
  height:25px;
  display:flex;
  color:#1890FF;
}
#u1869 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1869_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1870_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u1870 {
  border-width:0px;
  position:absolute;
  left:1201px;
  top:115px;
  width:28px;
  height:25px;
  display:flex;
  color:#1890FF;
}
#u1870 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1870_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1871_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u1871 {
  border-width:0px;
  position:absolute;
  left:1131px;
  top:115px;
  width:28px;
  height:25px;
  display:flex;
  color:#1890FF;
}
#u1871 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1871_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1872_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u1872 {
  border-width:0px;
  position:absolute;
  left:533px;
  top:124px;
  width:80px;
  height:25px;
  display:flex;
  font-size:16px;
}
#u1872 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1872_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1873_input {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:28px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1873_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:28px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u1873_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u1873 {
  border-width:0px;
  position:absolute;
  left:621px;
  top:124px;
  width:193px;
  height:28px;
  display:flex;
  color:#AAAAAA;
}
#u1873 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1873_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:193px;
  height:28px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u1873.disabled {
}
.u1873_input_option {
  color:#AAAAAA;
}
#u1875 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:165px;
  width:786px;
  height:697px;
}
#u1875_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:786px;
  height:697px;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1875_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1876_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:784px;
  height:731px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1876 {
  border-width:0px;
  position:absolute;
  left:4px;
  top:0px;
  width:784px;
  height:731px;
  display:flex;
}
#u1876 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1876_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1877_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:783px;
  height:35px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
}
#u1877 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:4px;
  width:783px;
  height:35px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
}
#u1877 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1877_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1878_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:108px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1878 {
  border-width:0px;
  position:absolute;
  left:54px;
  top:87px;
  width:108px;
  height:25px;
  display:flex;
}
#u1878 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1878_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1879_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
}
#u1879 {
  border-width:0px;
  position:absolute;
  left:753px;
  top:14px;
  width:16px;
  height:16px;
  display:flex;
}
#u1879 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1879_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1880_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#7F7F7F;
}
#u1880 {
  border-width:0px;
  position:absolute;
  left:696px;
  top:656px;
  width:60px;
  height:28px;
  display:flex;
  color:#7F7F7F;
}
#u1880 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1880_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1881_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1881 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:48px;
  width:64px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1881 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1881_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1882_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:777px;
  height:2px;
}
#u1882 {
  border-width:0px;
  position:absolute;
  left:4px;
  top:78px;
  width:776px;
  height:1px;
  display:flex;
}
#u1882 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1882_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1883_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1883 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:168px;
  width:64px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1883 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1883_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1884_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:777px;
  height:2px;
}
#u1884 {
  border-width:0px;
  position:absolute;
  left:12px;
  top:198px;
  width:776px;
  height:1px;
  display:flex;
}
#u1884 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1884_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1885 {
  border-width:0px;
  position:absolute;
  left:96px;
  top:218px;
  width:623px;
  height:90px;
}
#u1886_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u1886 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1886 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1886_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1887_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:30px;
}
#u1887 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:0px;
  width:189px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1887 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1887_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1888_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:296px;
  height:30px;
}
#u1888 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:0px;
  width:296px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1888 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1888_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1889_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u1889 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:139px;
  height:30px;
  display:flex;
}
#u1889 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1889_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1890_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:30px;
}
#u1890 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:30px;
  width:189px;
  height:30px;
  display:flex;
}
#u1890 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1890_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1891_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:296px;
  height:30px;
}
#u1891 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:30px;
  width:296px;
  height:30px;
  display:flex;
}
#u1891 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1891_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1892_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u1892 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:139px;
  height:30px;
  display:flex;
}
#u1892 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1892_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1893_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:30px;
}
#u1893 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:60px;
  width:189px;
  height:30px;
  display:flex;
}
#u1893 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1893_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1894_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:296px;
  height:30px;
}
#u1894 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:60px;
  width:296px;
  height:30px;
  display:flex;
}
#u1894 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1894_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1895_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1895 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:318px;
  width:64px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1895 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1895_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1896_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:777px;
  height:2px;
}
#u1896 {
  border-width:0px;
  position:absolute;
  left:12px;
  top:348px;
  width:776px;
  height:1px;
  display:flex;
}
#u1896 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1896_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1897 {
  border-width:0px;
  position:absolute;
  left:96px;
  top:378px;
  width:623px;
  height:90px;
}
#u1898_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u1898 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1898 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1898_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1899_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:30px;
}
#u1899 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:0px;
  width:189px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1899 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1899_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1900_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:296px;
  height:30px;
}
#u1900 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:0px;
  width:296px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1900 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1900_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1901_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u1901 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:139px;
  height:30px;
  display:flex;
}
#u1901 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1901_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1902_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:30px;
}
#u1902 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:30px;
  width:189px;
  height:30px;
  display:flex;
}
#u1902 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1902_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1903_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:296px;
  height:30px;
}
#u1903 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:30px;
  width:296px;
  height:30px;
  display:flex;
}
#u1903 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1903_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1904_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u1904 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:139px;
  height:30px;
  display:flex;
}
#u1904 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1904_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1905_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:30px;
}
#u1905 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:60px;
  width:189px;
  height:30px;
  display:flex;
}
#u1905 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1905_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1906_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:296px;
  height:30px;
}
#u1906 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:60px;
  width:296px;
  height:30px;
  display:flex;
}
#u1906 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1906_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1907_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1907 {
  border-width:0px;
  position:absolute;
  left:25px;
  top:486px;
  width:96px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u1907 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1907_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1908_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:777px;
  height:2px;
}
#u1908 {
  border-width:0px;
  position:absolute;
  left:12px;
  top:524px;
  width:776px;
  height:1px;
  display:flex;
}
#u1908 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1908_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1909 {
  border-width:0px;
  position:absolute;
  left:96px;
  top:544px;
  width:623px;
  height:90px;
}
#u1910_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u1910 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1910 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1910_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1911_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:30px;
}
#u1911 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:0px;
  width:189px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1911 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1911_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1912_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:296px;
  height:30px;
}
#u1912 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:0px;
  width:296px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u1912 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1912_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1913_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u1913 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:139px;
  height:30px;
  display:flex;
}
#u1913 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1913_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1914_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:30px;
}
#u1914 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:30px;
  width:189px;
  height:30px;
  display:flex;
}
#u1914 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1914_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1915_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:296px;
  height:30px;
}
#u1915 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:30px;
  width:296px;
  height:30px;
  display:flex;
}
#u1915 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1915_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1916_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:30px;
}
#u1916 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:139px;
  height:30px;
  display:flex;
}
#u1916 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1916_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1917_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:189px;
  height:30px;
}
#u1917 {
  border-width:0px;
  position:absolute;
  left:139px;
  top:60px;
  width:189px;
  height:30px;
  display:flex;
}
#u1917 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1917_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1918_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:296px;
  height:30px;
}
#u1918 {
  border-width:0px;
  position:absolute;
  left:328px;
  top:60px;
  width:296px;
  height:30px;
  display:flex;
}
#u1918 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1918_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1919_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:581px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1919 {
  border-width:0px;
  position:absolute;
  left:407px;
  top:83px;
  width:581px;
  height:25px;
  display:flex;
}
#u1919 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1919_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1920_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:581px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1920 {
  border-width:0px;
  position:absolute;
  left:54px;
  top:122px;
  width:581px;
  height:25px;
  display:flex;
}
#u1920 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1920_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1921 {
  border-width:0px;
  position:absolute;
  left:581px;
  top:289px;
  width:763px;
  height:314px;
  visibility:hidden;
}
#u1921_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:763px;
  height:314px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1921_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1922_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#FFFFFF;
}
#u1922 {
  border-width:0px;
  position:absolute;
  left:427px;
  top:90px;
  width:52px;
  height:25px;
  display:flex;
  font-size:13px;
  color:#FFFFFF;
}
#u1922 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1922_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1923_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#FFFFFF;
}
#u1923 {
  border-width:0px;
  position:absolute;
  left:542px;
  top:90px;
  width:52px;
  height:25px;
  display:flex;
  font-size:13px;
  color:#FFFFFF;
}
#u1923 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1923_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1924_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#FFFFFF;
}
#u1924 {
  border-width:0px;
  position:absolute;
  left:303px;
  top:90px;
  width:53px;
  height:16px;
  display:flex;
  font-size:13px;
  color:#FFFFFF;
}
#u1924 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1924_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u1925_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#FFFFFF;
}
#u1925 {
  border-width:0px;
  position:absolute;
  left:87px;
  top:90px;
  width:104px;
  height:25px;
  display:flex;
  font-size:13px;
  color:#FFFFFF;
}
#u1925 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1925_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1926_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:762px;
  height:28px;
  background:inherit;
  background-color:rgba(49, 163, 221, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1926 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:84px;
  width:762px;
  height:28px;
  display:flex;
}
#u1926 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1926_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1927_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#FFFFFF;
}
#u1927 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:90px;
  width:26px;
  height:25px;
  display:flex;
  font-size:13px;
  color:#FFFFFF;
}
#u1927 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1927_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1928_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:762px;
  height:81px;
  background:inherit;
  background-color:rgba(223, 233, 245, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1928 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:3px;
  width:762px;
  height:81px;
  display:flex;
}
#u1928 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1928_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1929_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u1929 {
  border-width:0px;
  position:absolute;
  left:613px;
  top:44px;
  width:90px;
  height:32px;
  display:flex;
  color:#000000;
}
#u1929 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1929_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:32px;
  background:inherit;
  background-color:rgba(231, 231, 235, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u1929.mouseOver {
}
#u1929_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1930_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:762px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 255, 1);
  border-top:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1930 {
  border-width:0px;
  position:absolute;
  left:3px;
  top:256px;
  width:762px;
  height:37px;
  display:flex;
}
#u1930 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1930_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1931_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:150px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1931 {
  border-width:0px;
  position:absolute;
  left:494px;
  top:265px;
  width:150px;
  height:25px;
  display:flex;
}
#u1931 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1931_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1932_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1932 {
  border-width:0px;
  position:absolute;
  left:661px;
  top:261px;
  width:32px;
  height:27px;
  display:flex;
}
#u1932 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1932_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:27px;
  background:inherit;
  background-color:rgba(231, 231, 235, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1932.mouseOver {
}
#u1932_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1933_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-left:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1933 {
  border-width:0px;
  position:absolute;
  left:693px;
  top:261px;
  width:32px;
  height:27px;
  display:flex;
}
#u1933 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1933_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:27px;
  background:inherit;
  background-color:rgba(231, 231, 235, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-left:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1933.mouseOver {
}
#u1933_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1934_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-left:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1934 {
  border-width:0px;
  position:absolute;
  left:725px;
  top:261px;
  width:32px;
  height:27px;
  display:flex;
}
#u1934 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1934_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:27px;
  background:inherit;
  background-color:rgba(231, 231, 235, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 204, 1);
  border-left:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1934.mouseOver {
}
#u1934_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1935 label {
  left:0px;
  width:100%;
}
#u1935_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u1935 {
  border-width:0px;
  position:absolute;
  left:11px;
  top:90px;
  width:18px;
  height:16px;
  display:flex;
}
#u1935 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
}
#u1935_img.selected {
}
#u1935.selected {
}
#u1935_img.disabled {
}
#u1935.disabled {
}
#u1935_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:2px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1935_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
.u1937_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:762px;
  height:37px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
.u1937 {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:-1px;
  width:762px;
  height:37px;
  display:flex;
}
.u1937 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
.u1937_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:762px;
  height:37px;
  background:inherit;
  background-color:rgba(231, 231, 235, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
.u1937.mouseOver {
}
.u1937_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:762px;
  height:37px;
  background:inherit;
  background-color:rgba(231, 231, 235, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(204, 204, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
.u1937.selected {
}
.u1937_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u1938_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#000000;
  text-align:center;
}
.u1938 {
  border-width:0px;
  position:absolute;
  left:21px;
  top:6px;
  width:26px;
  height:25px;
  display:flex;
  font-size:13px;
  color:#000000;
  text-align:center;
}
.u1938 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
.u1938_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
.u1939 label {
  left:0px;
  width:100%;
}
.u1939_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
.u1939 {
  border-width:0px;
  position:absolute;
  left:7px;
  top:9px;
  width:18px;
  height:16px;
  display:flex;
}
.u1939 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
}
.u1939_img.selected {
}
.u1939.selected {
}
.u1939_img.disabled {
}
.u1939.disabled {
}
.u1939_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:2px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
.u1939_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
.u1940_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:39px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#000000;
  text-align:center;
}
.u1940 {
  border-width:0px;
  position:absolute;
  left:97px;
  top:5px;
  width:39px;
  height:25px;
  display:flex;
  font-size:13px;
  color:#000000;
  text-align:center;
}
.u1940 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
.u1940_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
.u1941_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:121px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#000000;
  text-align:center;
}
.u1941 {
  border-width:0px;
  position:absolute;
  left:460px;
  top:8px;
  width:121px;
  height:25px;
  display:flex;
  font-size:13px;
  color:#000000;
  text-align:center;
}
.u1941 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
.u1941_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
.u1942_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#000000;
  text-align:center;
}
.u1942 {
  border-width:0px;
  position:absolute;
  left:350px;
  top:8px;
  width:8px;
  height:25px;
  display:flex;
  font-size:13px;
  color:#000000;
  text-align:center;
}
.u1942 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
.u1942_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
.u1943_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#000000;
  text-align:center;
}
.u1943 {
  border-width:0px;
  position:absolute;
  left:214px;
  top:8px;
  width:65px;
  height:25px;
  display:flex;
  font-size:13px;
  color:#000000;
  text-align:center;
}
.u1943 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
.u1943_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
.u1944_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:13px;
  color:#0000FF;
  text-align:center;
}
.u1944 {
  border-width:0px;
  position:absolute;
  left:675px;
  top:6px;
  width:26px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:13px;
  color:#0000FF;
  text-align:center;
}
.u1944 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
.u1944_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1936-1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:761px;
  height:36px;
}
.u1939 label {
  left:0px;
  width:100%;
}
#u1936-2 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:36px;
  width:761px;
  height:36px;
}
.u1939 label {
  left:0px;
  width:100%;
}
#u1936-3 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:72px;
  width:761px;
  height:36px;
}
.u1939 label {
  left:0px;
  width:100%;
}
#u1936-4 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:108px;
  width:761px;
  height:36px;
}
.u1939 label {
  left:0px;
  width:100%;
}
#u1936 {
  border-width:0px;
  position:absolute;
  left:4px;
  top:112px;
  width:761px;
  height:144px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
}
#u1945_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#FFFFFF;
}
#u1945 {
  border-width:0px;
  position:absolute;
  left:212px;
  top:87px;
  width:78px;
  height:25px;
  display:flex;
  font-size:13px;
  color:#FFFFFF;
}
#u1945 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1945_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1946_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:110px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#FFFFFF;
}
#u1946 {
  border-width:0px;
  position:absolute;
  left:83px;
  top:87px;
  width:110px;
  height:25px;
  display:flex;
  font-size:13px;
  color:#FFFFFF;
}
#u1946 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1946_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1947_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#FFFFFF;
}
#u1947 {
  border-width:0px;
  position:absolute;
  left:497px;
  top:86px;
  width:52px;
  height:25px;
  display:flex;
  font-size:13px;
  color:#FFFFFF;
}
#u1947 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1947_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1948_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#FFFFFF;
}
#u1948 {
  border-width:0px;
  position:absolute;
  left:680px;
  top:84px;
  width:26px;
  height:25px;
  display:flex;
  font-size:13px;
  color:#FFFFFF;
}
#u1948 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1948_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1949_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
  color:#FFFFFF;
}
#u1949 {
  border-width:0px;
  position:absolute;
  left:330px;
  top:89px;
  width:78px;
  height:24px;
  display:flex;
  font-size:13px;
  color:#FFFFFF;
}
#u1949 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1949_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1950_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u1950 {
  border-width:0px;
  position:absolute;
  left:516px;
  top:44px;
  width:90px;
  height:32px;
  display:flex;
  color:#000000;
}
#u1950 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1950_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:32px;
  background:inherit;
  background-color:rgba(231, 231, 235, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u1950.mouseOver {
}
#u1950_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1951_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
}
#u1951 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:16px;
  width:300px;
  height:28px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:20px;
}
#u1951 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1951_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1952_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:32px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u1952 {
  border-width:0px;
  position:absolute;
  left:710px;
  top:44px;
  width:55px;
  height:32px;
  display:flex;
  color:#000000;
}
#u1952 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1952_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:32px;
  background:inherit;
  background-color:rgba(231, 231, 235, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u1952.mouseOver {
}
#u1952_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u1953 {
  position:absolute;
  left:593px;
  top:207px;
  visibility:hidden;
}
#u1953_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:682px;
  height:590px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1953_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u1954_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:682px;
  height:590px;
}
#u1954 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:682px;
  height:590px;
  display:flex;
}
#u1954 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1954_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1955_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:267px;
  height:41px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1955 {
  border-width:0px;
  position:absolute;
  left:21px;
  top:478px;
  width:267px;
  height:41px;
  display:flex;
}
#u1955 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1955_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1956_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:374px;
  height:41px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1956 {
  border-width:0px;
  position:absolute;
  left:288px;
  top:478px;
  width:374px;
  height:41px;
  display:flex;
}
#u1956 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1956_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u1957_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1957 {
  border-width:0px;
  position:absolute;
  left:99px;
  top:486px;
  width:112px;
  height:25px;
  display:flex;
}
#u1957 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1957_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1958_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:298px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u1958 {
  border-width:0px;
  position:absolute;
  left:341px;
  top:486px;
  width:298px;
  height:25px;
  display:flex;
}
#u1958 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u1958_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u1959_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:584px;
  height:356px;
}
#u1959 {
  border-width:0px;
  position:absolute;
  left:581px;
  top:304px;
  width:584px;
  height:356px;
  display:flex;
}
#u1959 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u1959_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
