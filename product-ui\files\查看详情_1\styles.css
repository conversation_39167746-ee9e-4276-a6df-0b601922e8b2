﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-202px;
  width:611px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u4665 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4666_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:611px;
  height:484px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4666 {
  border-width:0px;
  position:absolute;
  left:202px;
  top:118px;
  width:611px;
  height:484px;
  display:flex;
}
#u4666 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4666_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4667_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:611px;
  height:34px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
  text-align:left;
}
#u4667 {
  border-width:0px;
  position:absolute;
  left:202px;
  top:84px;
  width:611px;
  height:34px;
  display:flex;
  color:#FFFFFF;
  text-align:left;
}
#u4667 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4667_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4668_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4668 {
  border-width:0px;
  position:absolute;
  left:249px;
  top:146px;
  width:76px;
  height:25px;
  display:flex;
}
#u4668 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4668_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4669_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u4669 {
  border-width:0px;
  position:absolute;
  left:725px;
  top:548px;
  width:71px;
  height:30px;
  display:flex;
  color:#000000;
}
#u4669 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4669_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4670_input {
  position:absolute;
  left:0px;
  top:0px;
  width:355px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4670_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:355px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4670_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:355px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u4670 {
  border-width:0px;
  position:absolute;
  left:339px;
  top:148px;
  width:355px;
  height:25px;
  display:flex;
  color:#AAAAAA;
}
#u4670 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4670_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:355px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u4670.disabled {
}
#u4671_input {
  position:absolute;
  left:0px;
  top:0px;
  width:355px;
  height:108px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4671_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:355px;
  height:108px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4671_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:355px;
  height:108px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u4671 {
  border-width:0px;
  position:absolute;
  left:339px;
  top:195px;
  width:355px;
  height:108px;
  display:flex;
  color:#AAAAAA;
}
#u4671 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4671_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:355px;
  height:108px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u4671.disabled {
}
#u4672 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4673_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:70px;
}
#u4673 {
  border-width:0px;
  position:absolute;
  left:348px;
  top:214px;
  width:70px;
  height:70px;
  display:flex;
}
#u4673 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4673_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4674_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u4674 {
  border-width:0px;
  position:absolute;
  left:406px;
  top:207px;
  width:20px;
  height:20px;
  display:flex;
}
#u4674 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4674_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4675 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4676_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:70px;
}
#u4676 {
  border-width:0px;
  position:absolute;
  left:475px;
  top:214px;
  width:70px;
  height:70px;
  display:flex;
}
#u4676 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4676_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4677_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u4677 {
  border-width:0px;
  position:absolute;
  left:533px;
  top:207px;
  width:20px;
  height:20px;
  display:flex;
}
#u4677 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4677_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4678 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4679_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:70px;
}
#u4679 {
  border-width:0px;
  position:absolute;
  left:587px;
  top:214px;
  width:70px;
  height:70px;
  display:flex;
}
#u4679 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4679_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4680_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u4680 {
  border-width:0px;
  position:absolute;
  left:645px;
  top:207px;
  width:20px;
  height:20px;
  display:flex;
}
#u4680 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4680_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4681_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:445px;
  height:186px;
}
#u4681 {
  border-width:0px;
  position:absolute;
  left:249px;
  top:330px;
  width:445px;
  height:186px;
  display:flex;
}
#u4681 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4681_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
