﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:1550px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u3446_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:32px;
}
#u3446 {
  border-width:0px;
  position:absolute;
  left:1347px;
  top:7px;
  width:32px;
  height:32px;
  display:flex;
}
#u3446 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3446_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3447_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:32px;
}
#u3447 {
  border-width:0px;
  position:absolute;
  left:1420px;
  top:7px;
  width:32px;
  height:32px;
  display:flex;
}
#u3447 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3447_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3448_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:18px;
  background:inherit;
  background-color:rgba(255, 59, 48, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#FFFFFF;
}
#u3448 {
  border-width:0px;
  position:absolute;
  left:1369px;
  top:2px;
  width:28px;
  height:18px;
  display:flex;
  font-size:12px;
  color:#FFFFFF;
}
#u3448 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3448_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3449 {
  border-width:0px;
  position:absolute;
  left:1052px;
  top:100px;
  width:498px;
  height:512px;
}
#u3449_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:498px;
  height:512px;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3449_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u3450_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:170px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u3450 {
  border-width:0px;
  position:absolute;
  left:4px;
  top:0px;
  width:300px;
  height:170px;
  display:flex;
}
#u3450 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3450_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3451_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u3451 {
  border-width:0px;
  position:absolute;
  left:154px;
  top:8px;
  width:47px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u3451 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3451_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3452_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u3452 {
  border-width:0px;
  position:absolute;
  left:47px;
  top:8px;
  width:102px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u3452 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3452_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3453_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u3453 {
  border-width:0px;
  position:absolute;
  left:21px;
  top:5px;
  width:26px;
  height:25px;
  display:flex;
}
#u3453 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3453_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3454_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u3454 {
  border-width:0px;
  position:absolute;
  left:147px;
  top:212px;
  width:47px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u3454 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3454_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3455_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u3455 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:212px;
  width:102px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u3455 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3455_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3456_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:13px;
  height:20px;
}
#u3456 {
  border-width:0px;
  position:absolute;
  left:21px;
  top:217px;
  width:13px;
  height:20px;
  display:flex;
}
#u3456 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u3456_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u3457_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:306px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u3457 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:35px;
  width:306px;
  height:50px;
  display:flex;
  color:#000000;
}
#u3457 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3457_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3458_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:312px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u3458 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:95px;
  width:312px;
  height:25px;
  display:flex;
  color:#000000;
}
#u3458 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3458_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3459_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#0000FF;
}
#u3459 {
  border-width:0px;
  position:absolute;
  left:345px;
  top:429px;
  width:98px;
  height:25px;
  display:flex;
  color:#0000FF;
}
#u3459 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3459_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3460_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u3460 {
  border-width:0px;
  position:absolute;
  left:388px;
  top:35px;
  width:22px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u3460 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3460_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3461_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u3461 {
  border-width:0px;
  position:absolute;
  left:426px;
  top:35px;
  width:33px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u3461 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3461_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3462_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u3462 {
  border-width:0px;
  position:absolute;
  left:388px;
  top:95px;
  width:22px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u3462 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3462_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3463_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u3463 {
  border-width:0px;
  position:absolute;
  left:426px;
  top:95px;
  width:33px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u3463 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3463_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3464_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:404px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u3464 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:241px;
  width:404px;
  height:50px;
  display:flex;
  color:#000000;
}
#u3464 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3464_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3465_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u3465 {
  border-width:0px;
  position:absolute;
  left:357px;
  top:257px;
  width:1px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u3465 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3465_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u3466_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u3466 {
  border-width:0px;
  position:absolute;
  left:357px;
  top:257px;
  width:1px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u3466 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3466_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u3467_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u3467 {
  border-width:0px;
  position:absolute;
  left:388px;
  top:145px;
  width:22px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u3467 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3467_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3468_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u3468 {
  border-width:0px;
  position:absolute;
  left:426px;
  top:145px;
  width:33px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u3468 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3468_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u3469_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:312px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u3469 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:145px;
  width:312px;
  height:25px;
  display:flex;
  color:#000000;
}
#u3469 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3469_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3470_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:404px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u3470 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:289px;
  width:404px;
  height:50px;
  display:flex;
  color:#000000;
}
#u3470 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3470_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u3471_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:388px;
  height:50px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u3471 {
  border-width:0px;
  position:absolute;
  left:55px;
  top:343px;
  width:388px;
  height:50px;
  display:flex;
  color:#000000;
}
#u3471 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u3471_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
