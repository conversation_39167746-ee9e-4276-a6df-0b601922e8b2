﻿<!DOCTYPE html>
<html>
  <head>
    <title>策略配置</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/策略配置/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/策略配置/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- 策略配置弹框 (组合) -->
      <div id="u14039" class="ax_default" data-label="策略配置弹框" data-left="0" data-top="0" data-width="0" data-height="0">

        <!-- 新增策略 (组合) -->
        <div id="u14040" class="ax_default" data-label="新增策略" data-left="0" data-top="0" data-width="0" data-height="0">

          <!-- 编辑 (组合) -->
          <div id="u14041" class="ax_default ax_default_hidden" data-label="编辑" style="display:none; visibility: hidden" data-left="0" data-top="0" data-width="0" data-height="0">
          </div>
        </div>
      </div>

      <!-- 弹框 (组合) -->
      <div id="u14042" class="ax_default" data-label="弹框" data-left="354" data-top="212" data-width="776" data-height="448">

        <!-- 主体框 (矩形) -->
        <div id="u14043" class="ax_default box_3" data-label="主体框">
          <img id="u14043_img" class="img " src="images/域名控制管理/主体框_u13925.svg"/>
          <div id="u14043_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u14044" class="ax_default box_3">
          <img id="u14044_img" class="img " src="images/审批通知模板/u278.svg"/>
          <div id="u14044_text" class="text ">
            <p><span>&nbsp;&nbsp; 域名控制</span></p>
          </div>
        </div>

        <!-- 按钮 (组合) -->
        <div id="u14045" class="ax_default" data-label="按钮" data-left="354" data-top="609" data-width="775" data-height="42">

          <!-- Unnamed (矩形) -->
          <div id="u14046" class="ax_default box_1">
            <div id="u14046_div" class=""></div>
            <div id="u14046_text" class="text ">
              <p><span>取消</span></p>
            </div>
          </div>

          <!-- Unnamed (线段) -->
          <div id="u14047" class="ax_default line">
            <img id="u14047_img" class="img " src="images/审批通知模板/u310.svg"/>
            <div id="u14047_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u14048" class="ax_default box_1">
            <div id="u14048_div" class=""></div>
            <div id="u14048_text" class="text ">
              <p><span>确认</span></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u14049" class="ax_default" data-left="548" data-top="280" data-width="180" data-height="32">

          <!-- Unnamed (矩形) -->
          <div id="u14050" class="ax_default box_1">
            <div id="u14050_div" class=""></div>
            <div id="u14050_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u14051" class="ax_default text_field">
            <div id="u14051_div" class=""></div>
            <input id="u14051_input" type="text" value="" class="u14051_input"/>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u14052" class="ax_default label">
          <div id="u14052_div" class=""></div>
          <div id="u14052_text" class="text ">
            <p><span style="color:#DD2C2C;">*</span><span style="color:#1A1919;">邮箱</span><span style="color:#0B0B0B;">域名：</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u14053" class="ax_default label">
          <div id="u14053_div" class=""></div>
          <div id="u14053_text" class="text ">
            <p><span style="color:#DD2C2C;">*</span><span style="color:#131212;">策略控制</span><span style="color:#0B0B0B;">：</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u14054" class="ax_default label">
          <div id="u14054_div" class=""></div>
          <div id="u14054_text" class="text ">
            <p><span style="color:#DD2C2C;">*</span><span style="color:#1A1919;">OA对接</span><span style="color:#0B0B0B;">：</span></p>
          </div>
        </div>

        <!-- Unnamed (动态面板) -->
        <div id="u14055" class="ax_default">
          <div id="u14055_state0" class="panel_state" data-label="关" style="">
            <div id="u14055_state0_content" class="panel_state_content">

              <!-- Unnamed (组合) -->
              <div id="u14056" class="ax_default" data-left="0" data-top="0" data-width="36" data-height="18">

                <!-- Unnamed (矩形) -->
                <div id="u14057" class="ax_default box_2">
                  <div id="u14057_div" class=""></div>
                  <div id="u14057_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- Unnamed (圆形) -->
                <div id="u14058" class="ax_default ellipse">
                  <img id="u14058_img" class="img " src="images/关键字_正则/u5380.svg"/>
                  <div id="u14058_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div id="u14055_state1" class="panel_state" data-label="开" style="visibility: hidden;">
            <div id="u14055_state1_content" class="panel_state_content">

              <!-- Unnamed (组合) -->
              <div id="u14059" class="ax_default" data-left="0" data-top="0" data-width="36" data-height="18">

                <!-- Unnamed (矩形) -->
                <div id="u14060" class="ax_default box_2">
                  <div id="u14060_div" class=""></div>
                  <div id="u14060_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- Unnamed (圆形) -->
                <div id="u14061" class="ax_default ellipse">
                  <img id="u14061_img" class="img " src="images/关键字_正则/u5380.svg"/>
                  <div id="u14061_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u14062" class="ax_default" data-left="553" data-top="335" data-width="378" data-height="265">

          <!-- Unnamed (矩形) -->
          <div id="u14063" class="ax_default box_1">
            <div id="u14063_div" class=""></div>
            <div id="u14063_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u14064" class="ax_default box_1">
            <div id="u14064_div" class=""></div>
            <div id="u14064_text" class="text ">
              <p><span>所有策略</span></p>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u14065" class="ax_default" data-left="570" data-top="348" data-width="12" data-height="12">

            <!-- Unnamed (矩形) -->
            <div id="u14066" class="ax_default ellipse">
              <div id="u14066_div" class=""></div>
              <div id="u14066_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (形状) -->
            <div id="u14067" class="ax_default _形状3">
              <img id="u14067_img" class="img " src="images/审批通知模板/u231.svg"/>
              <div id="u14067_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (动态面板) -->
          <div id="u14068" class="ax_default">
            <div id="u14068_state0" class="panel_state" data-label="State1" style="">
              <div id="u14068_state0_content" class="panel_state_content">

                <!-- Unnamed (组合) -->
                <div id="u14069" class="ax_default" data-left="1" data-top="1" data-width="158" data-height="36">

                  <!-- Unnamed (矩形) -->
                  <div id="u14070" class="ax_default shape">
                    <img id="u14070_img" class="img " src="images/域名控制管理/u13952.svg"/>
                    <div id="u14070_text" class="text " style="display:none; visibility: hidden">
                      <p></p>
                    </div>
                  </div>

                  <!-- Unnamed (组合) -->
                  <div id="u14071" class="ax_default" data-left="17" data-top="9" data-width="61" data-height="25">

                    <!-- Unnamed (矩形) -->
                    <div id="u14072" class="ax_default label">
                      <div id="u14072_div" class=""></div>
                      <div id="u14072_text" class="text ">
                        <p><span>选项一</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u14073" class="ax_default ellipse">
                      <div id="u14073_div" class=""></div>
                      <div id="u14073_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>

                    <!-- Unnamed (形状) -->
                    <div id="u14074" class="ax_default _形状3">
                      <img id="u14074_img" class="img " src="images/审批通知模板/u231.svg"/>
                      <div id="u14074_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Unnamed (组合) -->
                <div id="u14075" class="ax_default" data-left="1" data-top="37" data-width="158" data-height="36">

                  <!-- Unnamed (矩形) -->
                  <div id="u14076" class="ax_default shape">
                    <img id="u14076_img" class="img " src="images/域名控制管理/u13952.svg"/>
                    <div id="u14076_text" class="text " style="display:none; visibility: hidden">
                      <p></p>
                    </div>
                  </div>

                  <!-- Unnamed (组合) -->
                  <div id="u14077" class="ax_default" data-left="17" data-top="45" data-width="61" data-height="25">

                    <!-- Unnamed (矩形) -->
                    <div id="u14078" class="ax_default label">
                      <div id="u14078_div" class=""></div>
                      <div id="u14078_text" class="text ">
                        <p><span>选项二</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u14079" class="ax_default ellipse">
                      <div id="u14079_div" class=""></div>
                      <div id="u14079_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>

                    <!-- Unnamed (形状) -->
                    <div id="u14080" class="ax_default _形状3">
                      <img id="u14080_img" class="img " src="images/审批通知模板/u231.svg"/>
                      <div id="u14080_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Unnamed (组合) -->
                <div id="u14081" class="ax_default" data-left="1" data-top="73" data-width="158" data-height="36">

                  <!-- Unnamed (矩形) -->
                  <div id="u14082" class="ax_default shape">
                    <img id="u14082_img" class="img " src="images/域名控制管理/u13952.svg"/>
                    <div id="u14082_text" class="text " style="display:none; visibility: hidden">
                      <p></p>
                    </div>
                  </div>

                  <!-- Unnamed (组合) -->
                  <div id="u14083" class="ax_default" data-left="17" data-top="81" data-width="61" data-height="25">

                    <!-- Unnamed (矩形) -->
                    <div id="u14084" class="ax_default label">
                      <div id="u14084_div" class=""></div>
                      <div id="u14084_text" class="text ">
                        <p><span>选项三</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u14085" class="ax_default ellipse">
                      <div id="u14085_div" class=""></div>
                      <div id="u14085_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Unnamed (组合) -->
                <div id="u14086" class="ax_default" data-left="1" data-top="109" data-width="158" data-height="36">

                  <!-- Unnamed (矩形) -->
                  <div id="u14087" class="ax_default shape">
                    <img id="u14087_img" class="img " src="images/域名控制管理/u13952.svg"/>
                    <div id="u14087_text" class="text " style="display:none; visibility: hidden">
                      <p></p>
                    </div>
                  </div>

                  <!-- Unnamed (组合) -->
                  <div id="u14088" class="ax_default" data-left="17" data-top="117" data-width="61" data-height="25">

                    <!-- Unnamed (矩形) -->
                    <div id="u14089" class="ax_default label">
                      <div id="u14089_div" class=""></div>
                      <div id="u14089_text" class="text ">
                        <p><span>选项四</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u14090" class="ax_default ellipse">
                      <div id="u14090_div" class=""></div>
                      <div id="u14090_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>

                    <!-- Unnamed (形状) -->
                    <div id="u14091" class="ax_default _形状3">
                      <img id="u14091_img" class="img " src="images/审批通知模板/u231.svg"/>
                      <div id="u14091_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Unnamed (组合) -->
                <div id="u14092" class="ax_default" data-left="1" data-top="145" data-width="158" data-height="36">

                  <!-- Unnamed (矩形) -->
                  <div id="u14093" class="ax_default shape">
                    <img id="u14093_img" class="img " src="images/域名控制管理/u13952.svg"/>
                    <div id="u14093_text" class="text " style="display:none; visibility: hidden">
                      <p></p>
                    </div>
                  </div>

                  <!-- Unnamed (组合) -->
                  <div id="u14094" class="ax_default" data-left="17" data-top="153" data-width="61" data-height="25">

                    <!-- Unnamed (矩形) -->
                    <div id="u14095" class="ax_default label">
                      <div id="u14095_div" class=""></div>
                      <div id="u14095_text" class="text ">
                        <p><span>选项五</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u14096" class="ax_default ellipse">
                      <div id="u14096_div" class=""></div>
                      <div id="u14096_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>

                    <!-- Unnamed (形状) -->
                    <div id="u14097" class="ax_default _形状3">
                      <img id="u14097_img" class="img " src="images/审批通知模板/u231.svg"/>
                      <div id="u14097_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Unnamed (组合) -->
                <div id="u14098" class="ax_default" data-left="1" data-top="181" data-width="158" data-height="36">

                  <!-- Unnamed (矩形) -->
                  <div id="u14099" class="ax_default shape">
                    <img id="u14099_img" class="img " src="images/域名控制管理/u13952.svg"/>
                    <div id="u14099_text" class="text " style="display:none; visibility: hidden">
                      <p></p>
                    </div>
                  </div>

                  <!-- Unnamed (组合) -->
                  <div id="u14100" class="ax_default" data-left="17" data-top="189" data-width="61" data-height="25">

                    <!-- Unnamed (矩形) -->
                    <div id="u14101" class="ax_default label">
                      <div id="u14101_div" class=""></div>
                      <div id="u14101_text" class="text ">
                        <p><span>选项六</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u14102" class="ax_default ellipse">
                      <div id="u14102_div" class=""></div>
                      <div id="u14102_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>

                    <!-- Unnamed (形状) -->
                    <div id="u14103" class="ax_default _形状3">
                      <img id="u14103_img" class="img " src="images/审批通知模板/u231.svg"/>
                      <div id="u14103_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Unnamed (组合) -->
                <div id="u14104" class="ax_default" data-left="1" data-top="217" data-width="158" data-height="36">

                  <!-- Unnamed (矩形) -->
                  <div id="u14105" class="ax_default shape">
                    <img id="u14105_img" class="img " src="images/域名控制管理/u13952.svg"/>
                    <div id="u14105_text" class="text " style="display:none; visibility: hidden">
                      <p></p>
                    </div>
                  </div>

                  <!-- Unnamed (组合) -->
                  <div id="u14106" class="ax_default" data-left="17" data-top="225" data-width="61" data-height="25">

                    <!-- Unnamed (矩形) -->
                    <div id="u14107" class="ax_default label">
                      <div id="u14107_div" class=""></div>
                      <div id="u14107_text" class="text ">
                        <p><span>选项七</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u14108" class="ax_default ellipse">
                      <div id="u14108_div" class=""></div>
                      <div id="u14108_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>

                    <!-- Unnamed (形状) -->
                    <div id="u14109" class="ax_default _形状3">
                      <img id="u14109_img" class="img " src="images/审批通知模板/u231.svg"/>
                      <div id="u14109_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u14110" class="ax_default shape">
            <div id="u14110_div" class=""></div>
            <div id="u14110_text" class="text ">
              <p><span>&gt;</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u14111" class="ax_default shape">
            <div id="u14111_div" class=""></div>
            <div id="u14111_text" class="text ">
              <p><span>&lt; </span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u14112" class="ax_default heading_11">
            <div id="u14112_div" class=""></div>
            <div id="u14112_text" class="text ">
              <p><span>2/14</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u14113" class="ax_default box_1">
            <div id="u14113_div" class=""></div>
            <div id="u14113_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u14114" class="ax_default box_1">
            <div id="u14114_div" class=""></div>
            <div id="u14114_text" class="text ">
              <p><span>已选策略</span></p>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u14115" class="ax_default" data-left="788" data-top="348" data-width="12" data-height="12">

            <!-- Unnamed (矩形) -->
            <div id="u14116" class="ax_default ellipse">
              <div id="u14116_div" class=""></div>
              <div id="u14116_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (形状) -->
            <div id="u14117" class="ax_default _形状3">
              <img id="u14117_img" class="img " src="images/审批通知模板/u231.svg"/>
              <div id="u14117_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (动态面板) -->
          <div id="u14118" class="ax_default">
            <div id="u14118_state0" class="panel_state" data-label="State1" style="">
              <div id="u14118_state0_content" class="panel_state_content">

                <!-- Unnamed (组合) -->
                <div id="u14119" class="ax_default" data-left="1" data-top="1" data-width="158" data-height="36">

                  <!-- Unnamed (矩形) -->
                  <div id="u14120" class="ax_default shape">
                    <img id="u14120_img" class="img " src="images/域名控制管理/u13952.svg"/>
                    <div id="u14120_text" class="text " style="display:none; visibility: hidden">
                      <p></p>
                    </div>
                  </div>

                  <!-- Unnamed (组合) -->
                  <div id="u14121" class="ax_default" data-left="17" data-top="9" data-width="61" data-height="25">

                    <!-- Unnamed (矩形) -->
                    <div id="u14122" class="ax_default label">
                      <div id="u14122_div" class=""></div>
                      <div id="u14122_text" class="text ">
                        <p><span>选项一</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u14123" class="ax_default ellipse">
                      <div id="u14123_div" class=""></div>
                      <div id="u14123_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>

                    <!-- Unnamed (形状) -->
                    <div id="u14124" class="ax_default _形状3">
                      <img id="u14124_img" class="img " src="images/审批通知模板/u231.svg"/>
                      <div id="u14124_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Unnamed (组合) -->
                <div id="u14125" class="ax_default" data-left="1" data-top="37" data-width="158" data-height="36">

                  <!-- Unnamed (矩形) -->
                  <div id="u14126" class="ax_default shape">
                    <img id="u14126_img" class="img " src="images/域名控制管理/u13952.svg"/>
                    <div id="u14126_text" class="text " style="display:none; visibility: hidden">
                      <p></p>
                    </div>
                  </div>

                  <!-- Unnamed (组合) -->
                  <div id="u14127" class="ax_default" data-left="17" data-top="45" data-width="61" data-height="25">

                    <!-- Unnamed (矩形) -->
                    <div id="u14128" class="ax_default label">
                      <div id="u14128_div" class=""></div>
                      <div id="u14128_text" class="text ">
                        <p><span>选项二</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u14129" class="ax_default ellipse">
                      <div id="u14129_div" class=""></div>
                      <div id="u14129_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>

                    <!-- Unnamed (形状) -->
                    <div id="u14130" class="ax_default _形状3">
                      <img id="u14130_img" class="img " src="images/审批通知模板/u231.svg"/>
                      <div id="u14130_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Unnamed (组合) -->
                <div id="u14131" class="ax_default" data-left="1" data-top="73" data-width="158" data-height="36">

                  <!-- Unnamed (矩形) -->
                  <div id="u14132" class="ax_default shape">
                    <img id="u14132_img" class="img " src="images/域名控制管理/u13952.svg"/>
                    <div id="u14132_text" class="text " style="display:none; visibility: hidden">
                      <p></p>
                    </div>
                  </div>

                  <!-- Unnamed (组合) -->
                  <div id="u14133" class="ax_default" data-left="17" data-top="81" data-width="61" data-height="25">

                    <!-- Unnamed (矩形) -->
                    <div id="u14134" class="ax_default label">
                      <div id="u14134_div" class=""></div>
                      <div id="u14134_text" class="text ">
                        <p><span>选项三</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u14135" class="ax_default ellipse">
                      <div id="u14135_div" class=""></div>
                      <div id="u14135_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Unnamed (组合) -->
                <div id="u14136" class="ax_default" data-left="1" data-top="109" data-width="158" data-height="36">

                  <!-- Unnamed (矩形) -->
                  <div id="u14137" class="ax_default shape">
                    <img id="u14137_img" class="img " src="images/域名控制管理/u13952.svg"/>
                    <div id="u14137_text" class="text " style="display:none; visibility: hidden">
                      <p></p>
                    </div>
                  </div>

                  <!-- Unnamed (组合) -->
                  <div id="u14138" class="ax_default" data-left="17" data-top="117" data-width="61" data-height="25">

                    <!-- Unnamed (矩形) -->
                    <div id="u14139" class="ax_default label">
                      <div id="u14139_div" class=""></div>
                      <div id="u14139_text" class="text ">
                        <p><span>选项四</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u14140" class="ax_default ellipse">
                      <div id="u14140_div" class=""></div>
                      <div id="u14140_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>

                    <!-- Unnamed (形状) -->
                    <div id="u14141" class="ax_default _形状3">
                      <img id="u14141_img" class="img " src="images/审批通知模板/u231.svg"/>
                      <div id="u14141_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Unnamed (组合) -->
                <div id="u14142" class="ax_default" data-left="1" data-top="145" data-width="158" data-height="36">

                  <!-- Unnamed (矩形) -->
                  <div id="u14143" class="ax_default shape">
                    <img id="u14143_img" class="img " src="images/域名控制管理/u13952.svg"/>
                    <div id="u14143_text" class="text " style="display:none; visibility: hidden">
                      <p></p>
                    </div>
                  </div>

                  <!-- Unnamed (组合) -->
                  <div id="u14144" class="ax_default" data-left="17" data-top="153" data-width="61" data-height="25">

                    <!-- Unnamed (矩形) -->
                    <div id="u14145" class="ax_default label">
                      <div id="u14145_div" class=""></div>
                      <div id="u14145_text" class="text ">
                        <p><span>选项五</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u14146" class="ax_default ellipse">
                      <div id="u14146_div" class=""></div>
                      <div id="u14146_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>

                    <!-- Unnamed (形状) -->
                    <div id="u14147" class="ax_default _形状3">
                      <img id="u14147_img" class="img " src="images/审批通知模板/u231.svg"/>
                      <div id="u14147_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u14148" class="ax_default heading_11">
            <div id="u14148_div" class=""></div>
            <div id="u14148_text" class="text ">
              <p><span>0/5</span></p>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u14149" class="ax_default" data-left="561" data-top="377" data-width="144" data-height="28">

            <!-- Unnamed (矩形) -->
            <div id="u14150" class="ax_default box_1">
              <div id="u14150_div" class=""></div>
              <div id="u14150_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u14151" class="ax_default text_field">
              <div id="u14151_div" class=""></div>
              <input id="u14151_input" type="search" value="" class="u14151_input"/>
            </div>

            <!-- Unnamed (形状) -->
            <div id="u14152" class="ax_default _形状3">
              <img id="u14152_img" class="img " src="images/域名控制管理/u14034.svg"/>
              <div id="u14152_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u14153" class="ax_default" data-left="779" data-top="377" data-width="144" data-height="28">

            <!-- Unnamed (矩形) -->
            <div id="u14154" class="ax_default box_1">
              <div id="u14154_div" class=""></div>
              <div id="u14154_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u14155" class="ax_default text_field">
              <div id="u14155_div" class=""></div>
              <input id="u14155_input" type="search" value="" class="u14155_input"/>
            </div>

            <!-- Unnamed (形状) -->
            <div id="u14156" class="ax_default _形状3">
              <img id="u14156_img" class="img " src="images/域名控制管理/u14034.svg"/>
              <div id="u14156_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
