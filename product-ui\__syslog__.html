﻿<!DOCTYPE html>
<html>
  <head>
    <title>新建syslog规则</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/__syslog__/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/__syslog__/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- 新增规则 (组合) -->
      <div id="u2993" class="ax_default" data-label="新增规则" data-left="398.000151089958" data-top="180" data-width="602.999697820084" data-height="1080">

        <!-- 主体框 (矩形) -->
        <div id="u2994" class="ax_default box_3" data-label="主体框">
          <img id="u2994_img" class="img " src="images/syslog____/主体框_u2804.svg"/>
          <div id="u2994_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u2995" class="ax_default box_3">
          <img id="u2995_img" class="img " src="images/syslog____/u2805.svg"/>
          <div id="u2995_text" class="text ">
            <p><span>新增规则配置</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u2996" class="ax_default" data-left="398.000151089958" data-top="1207.91596261112" data-width="602.999697820084" data-height="40.0840373888771">

          <!-- Unnamed (矩形) -->
          <div id="u2997" class="ax_default box_1">
            <div id="u2997_div" class=""></div>
            <div id="u2997_text" class="text ">
              <p><span>重置</span></p>
            </div>
          </div>

          <!-- Unnamed (线段) -->
          <div id="u2998" class="ax_default line">
            <img id="u2998_img" class="img " src="images/syslog____/u2808.svg"/>
            <div id="u2998_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u2999" class="ax_default box_1">
            <div id="u2999_div" class=""></div>
            <div id="u2999_text" class="text ">
              <p><span>确认</span></p>
            </div>
          </div>

          <!-- Unnamed (矩形) -->
          <div id="u3000" class="ax_default box_1">
            <div id="u3000_div" class=""></div>
            <div id="u3000_text" class="text ">
              <p><span>取消</span></p>
            </div>
          </div>
        </div>

        <!-- 主体框 (矩形) -->
        <div id="u3001" class="ax_default box_3" data-label="主体框">
          <img id="u3001_img" class="img " src="images/syslog____/主体框_u2811.svg"/>
          <div id="u3001_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u3002" class="ax_default line">
          <img id="u3002_img" class="img " src="images/syslog____/u2812.svg"/>
          <div id="u3002_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u3003" class="ax_default label">
          <div id="u3003_div" class=""></div>
          <div id="u3003_text" class="text ">
            <p><span>基本信息</span></p>
          </div>
        </div>

        <!-- 主体框 (矩形) -->
        <div id="u3004" class="ax_default box_3" data-label="主体框">
          <img id="u3004_img" class="img " src="images/syslog____/主体框_u2814.svg"/>
          <div id="u3004_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u3005" class="ax_default line">
          <img id="u3005_img" class="img " src="images/syslog____/u2812.svg"/>
          <div id="u3005_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u3006" class="ax_default label">
          <div id="u3006_div" class=""></div>
          <div id="u3006_text" class="text ">
            <p><span>配置信息</span></p>
          </div>
        </div>

        <!-- 主体框 (矩形) -->
        <div id="u3007" class="ax_default box_3" data-label="主体框">
          <img id="u3007_img" class="img " src="images/syslog____/主体框_u2817.svg"/>
          <div id="u3007_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (线段) -->
        <div id="u3008" class="ax_default line">
          <img id="u3008_img" class="img " src="images/syslog____/u2812.svg"/>
          <div id="u3008_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u3009" class="ax_default label">
          <div id="u3009_div" class=""></div>
          <div id="u3009_text" class="text ">
            <p><span>过滤信息</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u3010" class="ax_default label">
          <div id="u3010_div" class=""></div>
          <div id="u3010_text" class="text ">
            <p><span style="color:#DD2C2C;">*</span><span style="color:#0F0F0F;">规则名称</span><span style="color:#0B0B0B;">：</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u3011" class="ax_default" data-left="620" data-top="260" data-width="283" data-height="29">

          <!-- Unnamed (矩形) -->
          <div id="u3012" class="ax_default box_1">
            <div id="u3012_div" class=""></div>
            <div id="u3012_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u3013" class="ax_default text_field">
            <div id="u3013_div" class=""></div>
            <input id="u3013_input" type="text" value="请输入规则名称" class="u3013_input"/>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u3014" class="ax_default label">
          <div id="u3014_div" class=""></div>
          <div id="u3014_text" class="text ">
            <p><span style="color:#DD2C2C;">*</span><span style="color:#0F0F0F;">syslog地址</span><span style="color:#0B0B0B;">：</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u3015" class="ax_default" data-left="620" data-top="301" data-width="283" data-height="29">

          <!-- Unnamed (矩形) -->
          <div id="u3016" class="ax_default box_1">
            <div id="u3016_div" class=""></div>
            <div id="u3016_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u3017" class="ax_default text_field">
            <div id="u3017_div" class=""></div>
            <input id="u3017_input" type="text" value="请输入IP:端口或者域名，多个地址之间请用英文换行符进行分割" class="u3017_input"/>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u3018" class="ax_default label">
          <div id="u3018_div" class=""></div>
          <div id="u3018_text" class="text ">
            <p><span style="color:#DD2C2C;">*</span><span style="color:#0F0F0F;">传输协议</span><span style="color:#0B0B0B;">：</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u3019" class="ax_default" data-left="620" data-top="342" data-width="283" data-height="32">

          <!-- Unnamed (组合) -->
          <div id="u3020" class="ax_default" data-left="620" data-top="342" data-width="283" data-height="32">

            <!-- 请选择 (矩形) -->
            <div id="u3021" class="ax_default box_1" data-label="请选择">
              <div id="u3021_div" class=""></div>
              <div id="u3021_text" class="text ">
                <p><span>全部</span></p>
              </div>
            </div>

            <!-- 下拉箭头 (形状) -->
            <div id="u3022" class="ax_default _形状3" data-label="下拉箭头">
              <img id="u3022_img" class="img " src="images/syslog____/下拉箭头_u2834.svg"/>
              <div id="u3022_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>

          <!-- 选择器基础用法 (动态面板) -->
          <div id="u3023" class="ax_default ax_default_hidden" data-label="选择器基础用法" style="display:none; visibility: hidden">
            <div id="u3023_state0" class="panel_state" data-label="State1" style="">
              <div id="u3023_state0_content" class="panel_state_content">

                <!-- Unnamed (矩形) -->
                <div id="u3024" class="ax_default box_1">
                  <div id="u3024_div" class=""></div>
                  <div id="u3024_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- Unnamed (三角形) -->
                <div id="u3025" class="ax_default box_1">
                  <img id="u3025_img" class="img " src="images/审批通知模板/u271.svg"/>
                  <div id="u3025_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- Unnamed (形状) -->
                <div id="u3026" class="ax_default box_2" selectiongroup="基础-小">
                  <img id="u3026_img" class="img " src="images/审批通知模板/u272.svg"/>
                  <div id="u3026_text" class="text ">
                    <p><span>TCP</span></p>
                  </div>
                </div>

                <!-- Unnamed (形状) -->
                <div id="u3027" class="ax_default box_2" selectiongroup="基础-小">
                  <img id="u3027_img" class="img " src="images/syslog____/u2839.svg"/>
                  <div id="u3027_text" class="text ">
                    <p><span>UDP</span></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u3028" class="ax_default label">
          <div id="u3028_div" class=""></div>
          <div id="u3028_text" class="text ">
            <p><span style="color:#DD2C2C;">*</span><span style="color:#0F0F0F;">Syslog等级</span><span style="color:#0B0B0B;">：</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u3029" class="ax_default" data-left="620" data-top="379" data-width="283" data-height="32">

          <!-- Unnamed (组合) -->
          <div id="u3030" class="ax_default" data-left="620" data-top="379" data-width="283" data-height="32">

            <!-- 请选择 (矩形) -->
            <div id="u3031" class="ax_default box_1" data-label="请选择">
              <div id="u3031_div" class=""></div>
              <div id="u3031_text" class="text ">
                <p><span>全部</span></p>
              </div>
            </div>

            <!-- 下拉箭头 (形状) -->
            <div id="u3032" class="ax_default _形状3" data-label="下拉箭头">
              <img id="u3032_img" class="img " src="images/syslog____/下拉箭头_u2834.svg"/>
              <div id="u3032_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>

          <!-- 选择器基础用法 (动态面板) -->
          <div id="u3033" class="ax_default ax_default_hidden" data-label="选择器基础用法" style="display:none; visibility: hidden">
            <div id="u3033_state0" class="panel_state" data-label="State1" style="">
              <div id="u3033_state0_content" class="panel_state_content">

                <!-- Unnamed (矩形) -->
                <div id="u3034" class="ax_default box_1">
                  <div id="u3034_div" class=""></div>
                  <div id="u3034_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- Unnamed (三角形) -->
                <div id="u3035" class="ax_default box_1">
                  <img id="u3035_img" class="img " src="images/审批通知模板/u271.svg"/>
                  <div id="u3035_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- Unnamed (形状) -->
                <div id="u3036" class="ax_default box_2" selectiongroup="基础-小">
                  <img id="u3036_img" class="img " src="images/审批通知模板/u272.svg"/>
                  <div id="u3036_text" class="text ">
                    <p><span>EMERG</span></p>
                  </div>
                </div>

                <!-- Unnamed (形状) -->
                <div id="u3037" class="ax_default box_2" selectiongroup="基础-小">
                  <img id="u3037_img" class="img " src="images/syslog____/u2839.svg"/>
                  <div id="u3037_text" class="text ">
                    <p><span>ALERT</span></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u3038" class="ax_default label">
          <div id="u3038_div" class=""></div>
          <div id="u3038_text" class="text ">
            <p><span style="color:#DD2C2C;">*</span><span style="color:#0F0F0F;">Syslog类型</span><span style="color:#0B0B0B;">：</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u3039" class="ax_default" data-left="620" data-top="416" data-width="283" data-height="32">

          <!-- Unnamed (组合) -->
          <div id="u3040" class="ax_default" data-left="620" data-top="416" data-width="283" data-height="32">

            <!-- 请选择 (矩形) -->
            <div id="u3041" class="ax_default box_1" data-label="请选择">
              <div id="u3041_div" class=""></div>
              <div id="u3041_text" class="text ">
                <p><span>全部</span></p>
              </div>
            </div>

            <!-- 下拉箭头 (形状) -->
            <div id="u3042" class="ax_default _形状3" data-label="下拉箭头">
              <img id="u3042_img" class="img " src="images/syslog____/下拉箭头_u2834.svg"/>
              <div id="u3042_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>

          <!-- 选择器基础用法 (动态面板) -->
          <div id="u3043" class="ax_default ax_default_hidden" data-label="选择器基础用法" style="display:none; visibility: hidden">
            <div id="u3043_state0" class="panel_state" data-label="State1" style="">
              <div id="u3043_state0_content" class="panel_state_content">

                <!-- Unnamed (矩形) -->
                <div id="u3044" class="ax_default box_1">
                  <div id="u3044_div" class=""></div>
                  <div id="u3044_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- Unnamed (三角形) -->
                <div id="u3045" class="ax_default box_1">
                  <img id="u3045_img" class="img " src="images/审批通知模板/u271.svg"/>
                  <div id="u3045_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- Unnamed (形状) -->
                <div id="u3046" class="ax_default box_2" selectiongroup="基础-小">
                  <img id="u3046_img" class="img " src="images/审批通知模板/u272.svg"/>
                  <div id="u3046_text" class="text ">
                    <p><span>kern</span></p>
                  </div>
                </div>

                <!-- Unnamed (形状) -->
                <div id="u3047" class="ax_default box_2" selectiongroup="基础-小">
                  <img id="u3047_img" class="img " src="images/syslog____/u2839.svg"/>
                  <div id="u3047_text" class="text ">
                    <p><span>user</span></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u3048" class="ax_default label">
          <div id="u3048_div" class=""></div>
          <div id="u3048_text" class="text ">
            <p><span style="color:#DD2C2C;">*</span><span style="color:#0F0F0F;">字符编码</span><span style="color:#0B0B0B;">：</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u3049" class="ax_default" data-left="620" data-top="514" data-width="283" data-height="32">

          <!-- Unnamed (组合) -->
          <div id="u3050" class="ax_default" data-left="620" data-top="514" data-width="283" data-height="32">

            <!-- 请选择 (矩形) -->
            <div id="u3051" class="ax_default box_1" data-label="请选择">
              <div id="u3051_div" class=""></div>
              <div id="u3051_text" class="text ">
                <p><span>全部</span></p>
              </div>
            </div>

            <!-- 下拉箭头 (形状) -->
            <div id="u3052" class="ax_default _形状3" data-label="下拉箭头">
              <img id="u3052_img" class="img " src="images/syslog____/下拉箭头_u2834.svg"/>
              <div id="u3052_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>

          <!-- 选择器基础用法 (动态面板) -->
          <div id="u3053" class="ax_default ax_default_hidden" data-label="选择器基础用法" style="display:none; visibility: hidden">
            <div id="u3053_state0" class="panel_state" data-label="State1" style="">
              <div id="u3053_state0_content" class="panel_state_content">

                <!-- Unnamed (矩形) -->
                <div id="u3054" class="ax_default box_1">
                  <div id="u3054_div" class=""></div>
                  <div id="u3054_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- Unnamed (三角形) -->
                <div id="u3055" class="ax_default box_1">
                  <img id="u3055_img" class="img " src="images/审批通知模板/u271.svg"/>
                  <div id="u3055_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- Unnamed (形状) -->
                <div id="u3056" class="ax_default box_2" selectiongroup="基础-小">
                  <img id="u3056_img" class="img " src="images/审批通知模板/u272.svg"/>
                  <div id="u3056_text" class="text ">
                    <p><span>UTF-8</span></p>
                  </div>
                </div>

                <!-- Unnamed (形状) -->
                <div id="u3057" class="ax_default box_2" selectiongroup="基础-小">
                  <img id="u3057_img" class="img " src="images/syslog____/u2839.svg"/>
                  <div id="u3057_text" class="text ">
                    <p><span>ANSI</span></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u3058" class="ax_default label">
          <div id="u3058_div" class=""></div>
          <div id="u3058_text" class="text ">
            <p><span style="color:#DD2C2C;">*</span><span style="color:#0F0F0F;">自定义分隔符</span><span style="color:#0B0B0B;">：</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u3059" class="ax_default" data-left="620" data-top="553" data-width="283" data-height="29">

          <!-- Unnamed (矩形) -->
          <div id="u3060" class="ax_default box_1">
            <div id="u3060_div" class=""></div>
            <div id="u3060_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u3061" class="ax_default text_field">
            <div id="u3061_div" class=""></div>
            <input id="u3061_input" type="text" value="请输入自定义分隔符" class="u3061_input"/>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u3062" class="ax_default label">
          <div id="u3062_div" class=""></div>
          <div id="u3062_text" class="text ">
            <p><span style="color:#DD2C2C;">*</span><span style="color:#0F0F0F;">日志前缀</span><span style="color:#0B0B0B;">：</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u3063" class="ax_default" data-left="620" data-top="591" data-width="283" data-height="29">

          <!-- Unnamed (矩形) -->
          <div id="u3064" class="ax_default box_1">
            <div id="u3064_div" class=""></div>
            <div id="u3064_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (文本框) -->
          <div id="u3065" class="ax_default text_field">
            <div id="u3065_div" class=""></div>
            <input id="u3065_input" type="text" value="请输入日志前缀" class="u3065_input"/>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u3066" class="ax_default label">
          <div id="u3066_div" class=""></div>
          <div id="u3066_text" class="text ">
            <p><span style="color:#DD2C2C;">*</span><span style="color:#0F0F0F;">时间格式</span><span style="color:#0B0B0B;">：</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u3067" class="ax_default" data-left="620" data-top="633" data-width="283" data-height="32">

          <!-- Unnamed (组合) -->
          <div id="u3068" class="ax_default" data-left="620" data-top="633" data-width="283" data-height="32">

            <!-- 请选择 (矩形) -->
            <div id="u3069" class="ax_default box_1" data-label="请选择">
              <div id="u3069_div" class=""></div>
              <div id="u3069_text" class="text ">
                <p><span>全部</span></p>
              </div>
            </div>

            <!-- 下拉箭头 (形状) -->
            <div id="u3070" class="ax_default _形状3" data-label="下拉箭头">
              <img id="u3070_img" class="img " src="images/syslog____/下拉箭头_u2834.svg"/>
              <div id="u3070_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>

          <!-- 选择器基础用法 (动态面板) -->
          <div id="u3071" class="ax_default ax_default_hidden" data-label="选择器基础用法" style="display:none; visibility: hidden">
            <div id="u3071_state0" class="panel_state" data-label="State1" style="">
              <div id="u3071_state0_content" class="panel_state_content">

                <!-- Unnamed (矩形) -->
                <div id="u3072" class="ax_default box_1">
                  <div id="u3072_div" class=""></div>
                  <div id="u3072_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- Unnamed (三角形) -->
                <div id="u3073" class="ax_default box_1">
                  <img id="u3073_img" class="img " src="images/审批通知模板/u271.svg"/>
                  <div id="u3073_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- Unnamed (形状) -->
                <div id="u3074" class="ax_default box_2" selectiongroup="基础-小">
                  <img id="u3074_img" class="img " src="images/syslog____/u2886.svg"/>
                  <div id="u3074_text" class="text ">
                    <p><span>YYYY-MM-DD HH:MM:SS</span></p>
                  </div>
                </div>

                <!-- Unnamed (形状) -->
                <div id="u3075" class="ax_default box_2" selectiongroup="基础-小">
                  <img id="u3075_img" class="img " src="images/syslog____/u2887.svg"/>
                  <div id="u3075_text" class="text ">
                    <p><span>YYYY-MM-DD</span></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u3076" class="ax_default label">
          <div id="u3076_div" class=""></div>
          <div id="u3076_text" class="text ">
            <p><span style="color:#DD2C2C;">*</span><span style="color:#0F0F0F;">Syslog应用</span><span style="color:#0B0B0B;">：</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u3077" class="ax_default" data-left="620" data-top="674" data-width="283" data-height="32">

          <!-- Unnamed (组合) -->
          <div id="u3078" class="ax_default" data-left="620" data-top="674" data-width="283" data-height="32">

            <!-- 请选择 (矩形) -->
            <div id="u3079" class="ax_default box_1" data-label="请选择">
              <div id="u3079_div" class=""></div>
              <div id="u3079_text" class="text ">
                <p><span>全部</span></p>
              </div>
            </div>

            <!-- 下拉箭头 (形状) -->
            <div id="u3080" class="ax_default _形状3" data-label="下拉箭头">
              <img id="u3080_img" class="img " src="images/syslog____/下拉箭头_u2834.svg"/>
              <div id="u3080_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>

          <!-- 选择器基础用法 (动态面板) -->
          <div id="u3081" class="ax_default ax_default_hidden" data-label="选择器基础用法" style="display:none; visibility: hidden">
            <div id="u3081_state0" class="panel_state" data-label="State1" style="">
              <div id="u3081_state0_content" class="panel_state_content">

                <!-- Unnamed (矩形) -->
                <div id="u3082" class="ax_default box_1">
                  <div id="u3082_div" class=""></div>
                  <div id="u3082_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- Unnamed (三角形) -->
                <div id="u3083" class="ax_default box_1">
                  <img id="u3083_img" class="img " src="images/审批通知模板/u271.svg"/>
                  <div id="u3083_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- Unnamed (形状) -->
                <div id="u3084" class="ax_default box_2" selectiongroup="基础-小">
                  <img id="u3084_img" class="img " src="images/syslog____/u2886.svg"/>
                  <div id="u3084_text" class="text ">
                    <p><span>YYYY-MM-DD HH:MM:SS</span></p>
                  </div>
                </div>

                <!-- Unnamed (形状) -->
                <div id="u3085" class="ax_default box_2" selectiongroup="基础-小">
                  <img id="u3085_img" class="img " src="images/syslog____/u2887.svg"/>
                  <div id="u3085_text" class="text ">
                    <p><span>YYYY-MM-DD</span></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u3086" class="ax_default label">
          <div id="u3086_div" class=""></div>
          <div id="u3086_text" class="text ">
            <p><span style="color:#DD2C2C;">*</span><span style="color:#0F0F0F;">Syslog字段</span><span style="color:#0B0B0B;">：</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u3087" class="ax_default" data-left="620" data-top="711" data-width="283" data-height="32">

          <!-- Unnamed (组合) -->
          <div id="u3088" class="ax_default" data-left="620" data-top="711" data-width="283" data-height="32">

            <!-- 请选择 (矩形) -->
            <div id="u3089" class="ax_default box_1" data-label="请选择">
              <div id="u3089_div" class=""></div>
              <div id="u3089_text" class="text ">
                <p><span>全部</span></p>
              </div>
            </div>

            <!-- 下拉箭头 (形状) -->
            <div id="u3090" class="ax_default _形状3" data-label="下拉箭头">
              <img id="u3090_img" class="img " src="images/syslog____/下拉箭头_u2834.svg"/>
              <div id="u3090_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>

          <!-- 选择器基础用法 (动态面板) -->
          <div id="u3091" class="ax_default ax_default_hidden" data-label="选择器基础用法" style="display:none; visibility: hidden">
            <div id="u3091_state0" class="panel_state" data-label="State1" style="">
              <div id="u3091_state0_content" class="panel_state_content">

                <!-- Unnamed (矩形) -->
                <div id="u3092" class="ax_default box_1">
                  <div id="u3092_div" class=""></div>
                  <div id="u3092_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- Unnamed (三角形) -->
                <div id="u3093" class="ax_default box_1">
                  <img id="u3093_img" class="img " src="images/审批通知模板/u271.svg"/>
                  <div id="u3093_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- Unnamed (形状) -->
                <div id="u3094" class="ax_default box_2" selectiongroup="基础-小">
                  <img id="u3094_img" class="img " src="images/syslog____/u2886.svg"/>
                  <div id="u3094_text" class="text ">
                    <p><span>YYYY-MM-DD HH:MM:SS</span></p>
                  </div>
                </div>

                <!-- Unnamed (形状) -->
                <div id="u3095" class="ax_default box_2" selectiongroup="基础-小">
                  <img id="u3095_img" class="img " src="images/syslog____/u2887.svg"/>
                  <div id="u3095_text" class="text ">
                    <p><span>YYYY-MM-DD</span></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u3096" class="ax_default label">
          <div id="u3096_div" class=""></div>
          <div id="u3096_text" class="text ">
            <p><span style="color:#0F0F0F;">筛选过滤</span><span style="color:#0B0B0B;">：</span></p>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u3097" class="ax_default" data-left="620" data-top="830" data-width="283" data-height="32">

          <!-- Unnamed (组合) -->
          <div id="u3098" class="ax_default" data-left="620" data-top="830" data-width="283" data-height="32">

            <!-- 请选择 (矩形) -->
            <div id="u3099" class="ax_default box_1" data-label="请选择">
              <div id="u3099_div" class=""></div>
              <div id="u3099_text" class="text ">
                <p><span>请选择筛选过滤条件</span></p>
              </div>
            </div>

            <!-- 下拉箭头 (形状) -->
            <div id="u3100" class="ax_default _形状3" data-label="下拉箭头">
              <img id="u3100_img" class="img " src="images/syslog____/下拉箭头_u2834.svg"/>
              <div id="u3100_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>

          <!-- 选择器基础用法 (动态面板) -->
          <div id="u3101" class="ax_default ax_default_hidden" data-label="选择器基础用法" style="display:none; visibility: hidden">
            <div id="u3101_state0" class="panel_state" data-label="State1" style="">
              <div id="u3101_state0_content" class="panel_state_content">

                <!-- Unnamed (矩形) -->
                <div id="u3102" class="ax_default box_1">
                  <div id="u3102_div" class=""></div>
                  <div id="u3102_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- Unnamed (三角形) -->
                <div id="u3103" class="ax_default box_1">
                  <img id="u3103_img" class="img " src="images/审批通知模板/u271.svg"/>
                  <div id="u3103_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- Unnamed (形状) -->
                <div id="u3104" class="ax_default box_2" selectiongroup="基础-小">
                  <img id="u3104_img" class="img " src="images/syslog____/u2886.svg"/>
                  <div id="u3104_text" class="text ">
                    <p><span>IP信息</span></p>
                  </div>
                </div>

                <!-- Unnamed (形状) -->
                <div id="u3105" class="ax_default box_2" selectiongroup="基础-小">
                  <img id="u3105_img" class="img " src="images/syslog____/u2887.svg"/>
                  <div id="u3105_text" class="text ">
                    <p><span>策略</span></p>
                  </div>
                </div>

                <!-- Unnamed (形状) -->
                <div id="u3106" class="ax_default box_2" selectiongroup="基础-小">
                  <img id="u3106_img" class="img " src="images/syslog____/u2887.svg"/>
                  <div id="u3106_text" class="text ">
                    <p><span>事件严重性</span></p>
                  </div>
                </div>

                <!-- Unnamed (形状) -->
                <div id="u3107" class="ax_default box_2" selectiongroup="基础-小">
                  <img id="u3107_img" class="img " src="images/syslog____/u2887.svg"/>
                  <div id="u3107_text" class="text ">
                    <p><span>发件人</span></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- IP设置 (组合) -->
        <div id="u3108" class="ax_default" data-label="IP设置" data-left="533" data-top="867" data-width="432" data-height="93">

          <!-- 测试 (矩形) -->
          <div id="u3109" class="ax_default label" data-label="测试">
            <div id="u3109_div" class=""></div>
            <div id="u3109_text" class="text ">
              <p><span style="color:#0F0F0F;">IP设置</span><span style="color:#0B0B0B;">：</span></p>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u3110" class="ax_default" selectiongroup="基础用法" data-left="620" data-top="867" data-width="33" data-height="25">

            <!-- Unnamed (矩形) -->
            <div id="u3111" class="ax_default label selected">
              <div id="u3111_div" class="selected"></div>
              <div id="u3111_text" class="text ">
                <p><span>IP</span></p>
              </div>
            </div>

            <!-- Unnamed (圆形) -->
            <div id="u3112" class="ax_default ellipse selected">
              <img id="u3112_img" class="img " src="images/审批通知模板/u292_selected.svg"/>
              <div id="u3112_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u3113" class="ax_default" selectiongroup="基础用法" data-left="670" data-top="867" data-width="47" data-height="25">

            <!-- Unnamed (矩形) -->
            <div id="u3114" class="ax_default label">
              <div id="u3114_div" class=""></div>
              <div id="u3114_text" class="text ">
                <p><span>IP段</span></p>
              </div>
            </div>

            <!-- Unnamed (圆形) -->
            <div id="u3115" class="ax_default ellipse">
              <img id="u3115_img" class="img " src="images/审批通知模板/u292.svg"/>
              <div id="u3115_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u3116" class="ax_default" data-left="620" data-top="892" data-width="283" data-height="29">

            <!-- Unnamed (矩形) -->
            <div id="u3117" class="ax_default box_1">
              <div id="u3117_div" class=""></div>
              <div id="u3117_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u3118" class="ax_default text_field">
              <div id="u3118_div" class=""></div>
              <input id="u3118_input" type="text" value="请输入IP信息" class="u3118_input"/>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u3119" class="ax_default" data-left="620" data-top="931" data-width="130" data-height="29">

            <!-- Unnamed (矩形) -->
            <div id="u3120" class="ax_default box_1">
              <div id="u3120_div" class=""></div>
              <div id="u3120_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u3121" class="ax_default text_field">
              <div id="u3121_div" class=""></div>
              <input id="u3121_input" type="text" value="请输入IP信息" class="u3121_input"/>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u3122" class="ax_default" data-left="773" data-top="931" data-width="130" data-height="29">

            <!-- Unnamed (矩形) -->
            <div id="u3123" class="ax_default box_1">
              <div id="u3123_div" class=""></div>
              <div id="u3123_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u3124" class="ax_default text_field">
              <div id="u3124_div" class=""></div>
              <input id="u3124_input" type="text" value="请输入IP信息" class="u3124_input"/>
            </div>
          </div>

          <!-- 测试 (矩形) -->
          <div id="u3125" class="ax_default label" data-label="测试">
            <div id="u3125_div" class=""></div>
            <div id="u3125_text" class="text ">
              <p><span>-</span></p>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u3126" class="ax_default" data-left="915" data-top="897" data-width="50" data-height="20">

            <!-- Unnamed (形状) -->
            <div id="u3127" class="ax_default _形状4">
              <img id="u3127_img" class="img " src="images/syslog____/u2954.svg"/>
              <div id="u3127_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (形状) -->
            <div id="u3128" class="ax_default _形状4">
              <img id="u3128_img" class="img " src="images/syslog____/u2955.svg"/>
              <div id="u3128_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u3129" class="ax_default" data-left="915" data-top="936" data-width="50" data-height="20">

            <!-- Unnamed (形状) -->
            <div id="u3130" class="ax_default _形状4">
              <img id="u3130_img" class="img " src="images/syslog____/u2954.svg"/>
              <div id="u3130_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (形状) -->
            <div id="u3131" class="ax_default _形状4">
              <img id="u3131_img" class="img " src="images/syslog____/u2955.svg"/>
              <div id="u3131_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>
        </div>

        <!-- 策略设置 (组合) -->
        <div id="u3132" class="ax_default" data-label="策略设置" data-left="541" data-top="970" data-width="362" data-height="32">

          <!-- Unnamed (矩形) -->
          <div id="u3133" class="ax_default label">
            <div id="u3133_div" class=""></div>
            <div id="u3133_text" class="text ">
              <p><span style="color:#0F0F0F;">策略</span><span style="color:#0B0B0B;">：</span></p>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u3134" class="ax_default" data-left="620" data-top="970" data-width="283" data-height="32">

            <!-- 请选择 (矩形) -->
            <div id="u3135" class="ax_default box_1" data-label="请选择">
              <div id="u3135_div" class=""></div>
              <div id="u3135_text" class="text ">
                <p><span>请选择</span></p>
              </div>
            </div>

            <!-- 下拉箭头 (形状) -->
            <div id="u3136" class="ax_default _形状3" data-label="下拉箭头">
              <img id="u3136_img" class="img " src="images/syslog____/下拉箭头_u2925.svg"/>
              <div id="u3136_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>

          <!-- 选择器基础用法 (动态面板) -->
          <div id="u3137" class="ax_default ax_default_hidden" data-label="选择器基础用法" style="display:none; visibility: hidden">
            <div id="u3137_state0" class="panel_state" data-label="State1" style="">
              <div id="u3137_state0_content" class="panel_state_content">

                <!-- Unnamed (矩形) -->
                <div id="u3138" class="ax_default box_1">
                  <div id="u3138_div" class=""></div>
                  <div id="u3138_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- Unnamed (三角形) -->
                <div id="u3139" class="ax_default box_1">
                  <img id="u3139_img" class="img " src="images/审批通知模板/u271.svg"/>
                  <div id="u3139_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- 列表中继器 (中继器) -->
                <div id="u3140" class="ax_default" data-label="列表中继器">
                  <script id="u3140_script" type="axure-repeater-template" data-label="列表中继器">

                    <!-- 基础多选选项 (组合) -->
                    <div id="u3141" class="ax_default u3141" data-label="基础多选选项" data-left="0" data-top="0" data-width="238" data-height="30">

                      <!-- 选项 (矩形) -->
                      <div id="u3142" class="ax_default box_2 u3142" data-label="选项" selectiongroup="基础-默认">
                        <div id="u3142_div" class="u3142_div"></div>
                        <div id="u3142_text" class="text u3142_text">
                          <p><span>黄金糕</span></p>
                        </div>
                      </div>

                      <!-- 选中标志 (形状) -->
                      <div id="u3143" class="ax_default _形状3 u3143" data-label="选中标志">
                        <img id="u3143_img" class="img u3143_img" src="images/syslog____/选中标志_u2932.svg"/>
                        <div id="u3143_text" class="text u3143_text" style="display:none; visibility: hidden">
                          <p></p>
                        </div>
                      </div>
                    </div>
                  </script>
                  <div id="u3140-1" class="preeval" style="width: 238px; height: 30px;">

                    <!-- 基础多选选项 (组合) -->
                    <div id="u3141-1" class="ax_default u3141" data-label="基础多选选项" style="visibility: inherit" data-left="0" data-top="0" data-width="238" data-height="30">

                      <!-- 选项 (矩形) -->
                      <div id="u3142-1" class="ax_default box_2 u3142" data-label="选项" selectiongroup="基础-默认-1" style="width: 238px; height: 30px; left: 0px; top: 0px;visibility: inherit">
                        <div id="u3142-1_div" class="u3142_div" style="width: 238px; height: 30px;visibility: inherit"></div>
                        <div id="u3142-1_text" class="text u3142_text" style="visibility: inherit">
                          <p><span>策略1</span></p>
                        </div>
                      </div>

                      <!-- 选中标志 (形状) -->
                      <div id="u3143-1" class="ax_default _形状3 u3143" data-label="选中标志" style="width: 12px; height: 9px; left: 213px; top: 11px;visibility: inherit">
                        <img id="u3143-1_img" class="img u3143_img" src="images/syslog____/选中标志_u2932.svg"/>
                        <div id="u3143-1_text" class="text u3143_text" style="display:none; visibility: hidden">
                          <p></p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div id="u3140-2" class="preeval" style="width: 238px; height: 30px;">

                    <!-- 基础多选选项 (组合) -->
                    <div id="u3141-2" class="ax_default u3141" data-label="基础多选选项" style="visibility: inherit" data-left="0" data-top="0" data-width="238" data-height="30">

                      <!-- 选项 (矩形) -->
                      <div id="u3142-2" class="ax_default box_2 u3142" data-label="选项" selectiongroup="基础-默认-2" style="width: 238px; height: 30px; left: 0px; top: 0px;visibility: inherit">
                        <div id="u3142-2_div" class="u3142_div" style="width: 238px; height: 30px;visibility: inherit"></div>
                        <div id="u3142-2_text" class="text u3142_text" style="visibility: inherit">
                          <p><span>策略2</span></p>
                        </div>
                      </div>

                      <!-- 选中标志 (形状) -->
                      <div id="u3143-2" class="ax_default _形状3 u3143" data-label="选中标志" style="width: 12px; height: 9px; left: 213px; top: 11px;visibility: inherit">
                        <img id="u3143-2_img" class="img u3143_img" src="images/syslog____/选中标志_u2932.svg"/>
                        <div id="u3143-2_text" class="text u3143_text" style="display:none; visibility: hidden">
                          <p></p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div id="u3140-3" class="preeval" style="width: 238px; height: 30px;">

                    <!-- 基础多选选项 (组合) -->
                    <div id="u3141-3" class="ax_default u3141" data-label="基础多选选项" style="visibility: inherit" data-left="0" data-top="0" data-width="238" data-height="30">

                      <!-- 选项 (矩形) -->
                      <div id="u3142-3" class="ax_default box_2 u3142" data-label="选项" selectiongroup="基础-默认-3" style="width: 238px; height: 30px; left: 0px; top: 0px;visibility: inherit">
                        <div id="u3142-3_div" class="u3142_div" style="width: 238px; height: 30px;visibility: inherit"></div>
                        <div id="u3142-3_text" class="text u3142_text" style="visibility: inherit">
                          <p><span>策略3</span></p>
                        </div>
                      </div>

                      <!-- 选中标志 (形状) -->
                      <div id="u3143-3" class="ax_default _形状3 u3143" data-label="选中标志" style="width: 12px; height: 9px; left: 213px; top: 11px;visibility: inherit">
                        <img id="u3143-3_img" class="img u3143_img" src="images/syslog____/选中标志_u2932.svg"/>
                        <div id="u3143-3_text" class="text u3143_text" style="display:none; visibility: hidden">
                          <p></p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div id="u3140-4" class="preeval" style="width: 238px; height: 30px;">

                    <!-- 基础多选选项 (组合) -->
                    <div id="u3141-4" class="ax_default u3141" data-label="基础多选选项" style="visibility: inherit" data-left="0" data-top="0" data-width="238" data-height="30">

                      <!-- 选项 (矩形) -->
                      <div id="u3142-4" class="ax_default box_2 u3142" data-label="选项" selectiongroup="基础-默认-4" style="width: 238px; height: 30px; left: 0px; top: 0px;visibility: inherit">
                        <div id="u3142-4_div" class="u3142_div" style="width: 238px; height: 30px;visibility: inherit"></div>
                        <div id="u3142-4_text" class="text u3142_text" style="visibility: inherit">
                          <p><span>策略4</span></p>
                        </div>
                      </div>

                      <!-- 选中标志 (形状) -->
                      <div id="u3143-4" class="ax_default _形状3 u3143" data-label="选中标志" style="width: 12px; height: 9px; left: 213px; top: 11px;visibility: inherit">
                        <img id="u3143-4_img" class="img u3143_img" src="images/syslog____/选中标志_u2932.svg"/>
                        <div id="u3143-4_text" class="text u3143_text" style="display:none; visibility: hidden">
                          <p></p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div id="u3140-5" class="preeval" style="width: 238px; height: 30px;">

                    <!-- 基础多选选项 (组合) -->
                    <div id="u3141-5" class="ax_default u3141" data-label="基础多选选项" style="visibility: inherit" data-left="0" data-top="0" data-width="238" data-height="30">

                      <!-- 选项 (矩形) -->
                      <div id="u3142-5" class="ax_default box_2 u3142" data-label="选项" selectiongroup="基础-默认-5" style="width: 238px; height: 30px; left: 0px; top: 0px;visibility: inherit">
                        <div id="u3142-5_div" class="u3142_div" style="width: 238px; height: 30px;visibility: inherit"></div>
                        <div id="u3142-5_text" class="text u3142_text" style="visibility: inherit">
                          <p><span>策略5</span></p>
                        </div>
                      </div>

                      <!-- 选中标志 (形状) -->
                      <div id="u3143-5" class="ax_default _形状3 u3143" data-label="选中标志" style="width: 12px; height: 9px; left: 213px; top: 11px;visibility: inherit">
                        <img id="u3143-5_img" class="img u3143_img" src="images/syslog____/选中标志_u2932.svg"/>
                        <div id="u3143-5_text" class="text u3143_text" style="display:none; visibility: hidden">
                          <p></p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 严重性设置 (组合) -->
        <div id="u3144" class="ax_default" data-label="严重性设置" data-left="535" data-top="1031" data-width="368" data-height="32">

          <!-- Unnamed (矩形) -->
          <div id="u3145" class="ax_default label">
            <div id="u3145_div" class=""></div>
            <div id="u3145_text" class="text ">
              <p><span style="color:#0F0F0F;">严重性</span><span style="color:#0B0B0B;">：</span></p>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u3146" class="ax_default" data-left="620" data-top="1031" data-width="283" data-height="32">

            <!-- 请选择 (矩形) -->
            <div id="u3147" class="ax_default box_1" data-label="请选择">
              <div id="u3147_div" class=""></div>
              <div id="u3147_text" class="text ">
                <p><span>请选择</span></p>
              </div>
            </div>

            <!-- 下拉箭头 (形状) -->
            <div id="u3148" class="ax_default _形状3" data-label="下拉箭头">
              <img id="u3148_img" class="img " src="images/syslog____/下拉箭头_u2925.svg"/>
              <div id="u3148_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>
          </div>

          <!-- 选择器基础用法 (动态面板) -->
          <div id="u3149" class="ax_default ax_default_hidden" data-label="选择器基础用法" style="display:none; visibility: hidden">
            <div id="u3149_state0" class="panel_state" data-label="State1" style="">
              <div id="u3149_state0_content" class="panel_state_content">

                <!-- Unnamed (矩形) -->
                <div id="u3150" class="ax_default box_1">
                  <div id="u3150_div" class=""></div>
                  <div id="u3150_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- Unnamed (三角形) -->
                <div id="u3151" class="ax_default box_1">
                  <img id="u3151_img" class="img " src="images/审批通知模板/u271.svg"/>
                  <div id="u3151_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- 列表中继器 (中继器) -->
                <div id="u3152" class="ax_default" data-label="列表中继器">
                  <script id="u3152_script" type="axure-repeater-template" data-label="列表中继器">

                    <!-- 基础多选选项 (组合) -->
                    <div id="u3153" class="ax_default u3153" data-label="基础多选选项" data-left="0" data-top="0" data-width="238" data-height="30">

                      <!-- 选项 (矩形) -->
                      <div id="u3154" class="ax_default box_2 u3154" data-label="选项" selectiongroup="基础-默认">
                        <div id="u3154_div" class="u3154_div"></div>
                        <div id="u3154_text" class="text u3154_text">
                          <p><span>黄金糕</span></p>
                        </div>
                      </div>

                      <!-- 选中标志 (形状) -->
                      <div id="u3155" class="ax_default _形状3 u3155" data-label="选中标志">
                        <img id="u3155_img" class="img u3155_img" src="images/syslog____/选中标志_u2932.svg"/>
                        <div id="u3155_text" class="text u3155_text" style="display:none; visibility: hidden">
                          <p></p>
                        </div>
                      </div>
                    </div>
                  </script>
                  <div id="u3152-1" class="preeval" style="width: 238px; height: 30px;">

                    <!-- 基础多选选项 (组合) -->
                    <div id="u3153-1" class="ax_default u3153" data-label="基础多选选项" style="visibility: inherit" data-left="0" data-top="0" data-width="238" data-height="30">

                      <!-- 选项 (矩形) -->
                      <div id="u3154-1" class="ax_default box_2 u3154" data-label="选项" selectiongroup="基础-默认-1" style="width: 238px; height: 30px; left: 0px; top: 0px;visibility: inherit">
                        <div id="u3154-1_div" class="u3154_div" style="width: 238px; height: 30px;visibility: inherit"></div>
                        <div id="u3154-1_text" class="text u3154_text" style="visibility: inherit">
                          <p><span>高</span></p>
                        </div>
                      </div>

                      <!-- 选中标志 (形状) -->
                      <div id="u3155-1" class="ax_default _形状3 u3155" data-label="选中标志" style="width: 12px; height: 9px; left: 213px; top: 11px;visibility: inherit">
                        <img id="u3155-1_img" class="img u3155_img" src="images/syslog____/选中标志_u2932.svg"/>
                        <div id="u3155-1_text" class="text u3155_text" style="display:none; visibility: hidden">
                          <p></p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div id="u3152-2" class="preeval" style="width: 238px; height: 30px;">

                    <!-- 基础多选选项 (组合) -->
                    <div id="u3153-2" class="ax_default u3153" data-label="基础多选选项" style="visibility: inherit" data-left="0" data-top="0" data-width="238" data-height="30">

                      <!-- 选项 (矩形) -->
                      <div id="u3154-2" class="ax_default box_2 u3154" data-label="选项" selectiongroup="基础-默认-2" style="width: 238px; height: 30px; left: 0px; top: 0px;visibility: inherit">
                        <div id="u3154-2_div" class="u3154_div" style="width: 238px; height: 30px;visibility: inherit"></div>
                        <div id="u3154-2_text" class="text u3154_text" style="visibility: inherit">
                          <p><span>中</span></p>
                        </div>
                      </div>

                      <!-- 选中标志 (形状) -->
                      <div id="u3155-2" class="ax_default _形状3 u3155" data-label="选中标志" style="width: 12px; height: 9px; left: 213px; top: 11px;visibility: inherit">
                        <img id="u3155-2_img" class="img u3155_img" src="images/syslog____/选中标志_u2932.svg"/>
                        <div id="u3155-2_text" class="text u3155_text" style="display:none; visibility: hidden">
                          <p></p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div id="u3152-3" class="preeval" style="width: 238px; height: 30px;">

                    <!-- 基础多选选项 (组合) -->
                    <div id="u3153-3" class="ax_default u3153" data-label="基础多选选项" style="visibility: inherit" data-left="0" data-top="0" data-width="238" data-height="30">

                      <!-- 选项 (矩形) -->
                      <div id="u3154-3" class="ax_default box_2 u3154" data-label="选项" selectiongroup="基础-默认-3" style="width: 238px; height: 30px; left: 0px; top: 0px;visibility: inherit">
                        <div id="u3154-3_div" class="u3154_div" style="width: 238px; height: 30px;visibility: inherit"></div>
                        <div id="u3154-3_text" class="text u3154_text" style="visibility: inherit">
                          <p><span>低</span></p>
                        </div>
                      </div>

                      <!-- 选中标志 (形状) -->
                      <div id="u3155-3" class="ax_default _形状3 u3155" data-label="选中标志" style="width: 12px; height: 9px; left: 213px; top: 11px;visibility: inherit">
                        <img id="u3155-3_img" class="img u3155_img" src="images/syslog____/选中标志_u2932.svg"/>
                        <div id="u3155-3_text" class="text u3155_text" style="display:none; visibility: hidden">
                          <p></p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 发件人设置 (组合) -->
        <div id="u3156" class="ax_default" data-label="发件人设置" data-left="539" data-top="1088" data-width="426" data-height="72">

          <!-- Unnamed (矩形) -->
          <div id="u3157" class="ax_default label">
            <div id="u3157_div" class=""></div>
            <div id="u3157_text" class="text ">
              <p><span style="color:#0F0F0F;">发件人</span><span style="color:#0B0B0B;">：</span></p>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u3158" class="ax_default" data-left="620" data-top="1088" data-width="283" data-height="29">

            <!-- Unnamed (矩形) -->
            <div id="u3159" class="ax_default box_1">
              <div id="u3159_div" class=""></div>
              <div id="u3159_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u3160" class="ax_default text_field">
              <div id="u3160_div" class=""></div>
              <input id="u3160_input" type="text" value="请输入信息" class="u3160_input"/>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u3161" class="ax_default _形状4">
            <img id="u3161_img" class="img " src="images/syslog____/u2954.svg"/>
            <div id="u3161_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u3162" class="ax_default _形状4">
            <img id="u3162_img" class="img " src="images/syslog____/u2955.svg"/>
            <div id="u3162_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (组合) -->
          <div id="u3163" class="ax_default" data-left="620" data-top="1131" data-width="283" data-height="29">

            <!-- Unnamed (矩形) -->
            <div id="u3164" class="ax_default box_1">
              <div id="u3164_div" class=""></div>
              <div id="u3164_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u3165" class="ax_default text_field">
              <div id="u3165_div" class=""></div>
              <input id="u3165_input" type="text" value="请输入信息" class="u3165_input"/>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u3166" class="ax_default _形状4">
            <img id="u3166_img" class="img " src="images/syslog____/u2954.svg"/>
            <div id="u3166_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (形状) -->
          <div id="u3167" class="ax_default _形状4">
            <img id="u3167_img" class="img " src="images/syslog____/u2955.svg"/>
            <div id="u3167_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>
      </div>

      <!-- Unnamed (形状) -->
      <div id="u3168" class="ax_default _形状4">
        <img id="u3168_img" class="img " src="images/syslog____/u2933.svg"/>
        <div id="u3168_text" class="text " style="display:none; visibility: hidden">
          <p></p>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
