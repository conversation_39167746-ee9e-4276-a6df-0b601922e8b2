﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:1896px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u13643_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1896px;
  height:911px;
}
#u13643 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1896px;
  height:911px;
  display:flex;
}
#u13643 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13643_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u13644 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u13645_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:133px;
  height:122px;
}
#u13645 {
  border-width:0px;
  position:absolute;
  left:675px;
  top:269px;
  width:133px;
  height:122px;
  display:flex;
}
#u13645 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13645_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u13646_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:393px;
  height:122px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  color:#0B0B0B;
}
#u13646 {
  border-width:0px;
  position:absolute;
  left:819px;
  top:269px;
  width:393px;
  height:122px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  color:#0B0B0B;
}
#u13646 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13646_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13647_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:480px;
  height:159px;
}
#u13647 {
  border-width:0px;
  position:absolute;
  left:675px;
  top:409px;
  width:480px;
  height:159px;
  display:flex;
}
#u13647 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13647_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
