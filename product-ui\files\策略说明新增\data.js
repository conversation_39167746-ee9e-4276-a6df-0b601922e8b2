﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q,r,s,t,u],v,_(w,x,y,z,g,A,B,_(),C,[],D,_(E,F,G,H,I,_(J,K,L,M),N,null,O,P,P,Q,R,S,null,T,U,V,W,X,Y,Z,U,ba,bb,_(J,K,L,bc),bd,U,be,bf,_(bg,bh,bi,bj,bk,bj,bl,bj,L,_(bm,bn,bo,bn,bp,bn,bq,br)),i,_(j,k,l,k)),bs,_(),bt,_(),bu,_(bv,[_(bw,bx,by,bz,bA,bB,y,bC,bD,bC,bE,bF,D,_(),bs,_(),bG,_(),bH,[_(bw,bI,by,bJ,bA,bB,y,bC,bD,bC,bE,bF,D,_(bK,_(bL,bM,bN,bO)),bs,_(),bG,_(),bH,[_(bw,bP,by,bQ,bA,bR,y,bS,bD,bS,bE,bF,D,_(X,bT,bU,bV,i,_(j,bW,l,bX),E,bY,bK,_(bL,bZ,bN,ca),I,_(J,K,L,M),cb,cc,Z,cd,bb,_(J,K,L,ce)),bs,_(),bG,_(),cf,_(cg,ch),ci,bh),_(bw,cj,by,h,bA,bR,y,bS,bD,bS,bE,bF,D,_(X,bT,bU,bV,i,_(j,ck,l,cl),E,bY,I,_(J,K,L,cm),cb,cc,bK,_(bL,bZ,bN,cn),co,cp,Z,cd,bb,_(J,K,L,ce)),bs,_(),bG,_(),cf,_(cg,cq),ci,bh),_(bw,cr,by,cs,bA,bB,y,bC,bD,bC,bE,bF,D,_(bK,_(bL,bM,bN,ct)),bs,_(),bG,_(),bH,[_(bw,cu,by,h,bA,bR,y,bS,bD,bS,bE,bF,D,_(X,bT,bU,bV,cv,_(J,K,L,cw,cx,cy),i,_(j,cz,l,cA),E,cB,bb,_(J,K,L,cC),bd,cD,cE,_(cF,_(cv,_(J,K,L,cG,cx,cy),I,_(J,K,L,cH),bb,_(J,K,L,cI)),cJ,_(cv,_(J,K,L,cK,cx,cy),I,_(J,K,L,cH),bb,_(J,K,L,cK),Z,cd,cL,K),cM,_(cv,_(J,K,L,cN,cx,cy),bb,_(J,K,L,cO),Z,cd,cL,K)),bK,_(bL,cP,bN,cQ),cb,cc),bs,_(),bG,_(),bt,_(cR,_(cS,cT,cU,[_(cS,h,cV,h,cW,bh,cX,cY,cZ,[_(da,db,cS,dc,dd,de,df,_(dc,_(h,dc)),dg,[_(dh,[bI],di,_(dj,dk,dl,_(dm,dn,dp,bh)))])])])),dq,bF,ci,bh),_(bw,dr,by,h,bA,ds,y,bS,bD,dt,bE,bF,D,_(X,bT,i,_(j,bW,l,cy),E,du,bK,_(bL,bZ,bN,dv),bb,_(J,K,L,dw),cb,cc),bs,_(),bG,_(),cf,_(cg,dx),ci,bh),_(bw,dy,by,h,bA,bR,y,bS,bD,bS,bE,bF,D,_(X,bT,bU,bV,cv,_(J,K,L,M,cx,cy),i,_(j,cz,l,cA),E,cB,bb,_(J,K,L,cC),bd,cD,cE,_(cF,_(cv,_(J,K,L,cG,cx,cy),I,_(J,K,L,cH),bb,_(J,K,L,cI)),cJ,_(cv,_(J,K,L,cK,cx,cy),I,_(J,K,L,cH),bb,_(J,K,L,cK),Z,cd,cL,K),cM,_(cv,_(J,K,L,cN,cx,cy),bb,_(J,K,L,cO),Z,cd,cL,K)),bK,_(bL,dz,bN,cQ),cb,cc,I,_(J,K,L,dA)),bs,_(),bG,_(),ci,bh)],dB,bh),_(bw,dC,by,h,bA,bB,y,bC,bD,bC,bE,bF,D,_(bK,_(bL,dD,bN,dE)),bs,_(),bG,_(),bH,[_(bw,dF,by,h,bA,bR,y,bS,bD,bS,bE,bF,D,_(i,_(j,dG,l,dH),E,dI,bK,_(bL,dJ,bN,dK),bb,_(J,K,L,cC),cE,_(cF,_(bb,_(J,K,L,cN)),dL,_(bb,_(J,K,L,cG))),bd,cD),bs,_(),bG,_(),ci,bh),_(bw,dM,by,dN,bA,dO,y,dP,bD,dP,bE,bF,D,_(cv,_(J,K,L,cw,cx,cy),i,_(j,dQ,l,dR),cE,_(dS,_(cv,_(J,K,L,cN,cx,cy),cb,cc),cM,_(E,dT)),E,dU,bK,_(bL,dV,bN,dW),cb,cc,Z,U),dX,bh,bs,_(),bG,_(),bt,_(dY,_(cS,dZ,cU,[_(cS,h,cV,h,cW,bh,cX,cY,cZ,[_(da,ea,cS,eb,dd,ec,df,_(ed,_(h,ee)),ef,_(eg,eh,ei,[_(eg,ej,ek,el,em,[_(eg,en,eo,bh,ep,bh,eq,bh,er,[es]),_(eg,et,er,eu,ev,_(ew,_(eg,ej,ek,ex,em,[_(eg,en,eo,bh,ep,bh,eq,bh,er,[dM])])),ey,[_(ez,eA,eB,eC,eD,_(eE,eF,eB,eG,g,ew),eH,eI)]),_(eg,eJ,er,bF)])])),_(da,ea,cS,eK,dd,ec,df,_(eL,_(h,eM)),ef,_(eg,eh,ei,[_(eg,ej,ek,eN,em,[_(eg,en,eo,bh,ep,bh,eq,bh,er,[dM]),_(eg,et,er,eO,ev,_(ew,_(eg,ej,ek,ex,em,[_(eg,en,eo,bh,ep,bh,eq,bh,er,[dM])])),ey,[_(ez,eF,eB,eP,eD,_(eE,eF,eB,eG,g,ew),eQ,eR,em,[_(ez,eA,eB,eS,er,k),_(ez,eA,eB,eS,er,eT)])])])]))])]),eU,_(cS,eV,cU,[_(cS,h,cV,h,cW,bh,cX,cY,cZ,[_(da,ea,cS,eW,dd,eX,df,_(eY,_(h,eZ)),ef,_(eg,eh,ei,[_(eg,ej,ek,fa,em,[_(eg,en,eo,bh,ep,bh,eq,bh,er,[dF]),_(eg,et,er,fb,ey,[])])]))])]),fc,_(cS,fd,cU,[_(cS,h,cV,h,cW,bh,cX,cY,cZ,[_(da,ea,cS,fe,dd,eX,df,_(ff,_(h,fg)),ef,_(eg,eh,ei,[_(eg,ej,ek,fa,em,[_(eg,en,eo,bh,ep,bh,eq,bh,er,[dF]),_(eg,et,er,fh,ey,[])])]))])])),fi,fj),_(bw,fk,by,h,bA,bR,y,bS,bD,bS,bE,bF,D,_(X,fl,cv,_(J,K,L,fm,cx,cy),i,_(j,fn,l,fo),E,fp,cb,fq,bK,_(bL,fr,bN,fs)),bs,_(),bG,_(),ci,bh),_(bw,es,by,ft,bA,bR,y,bS,bD,bS,bE,bF,D,_(X,fl,cv,_(J,K,L,fm,cx,cy),i,_(j,fu,l,fo),E,fp,cb,fq,bK,_(bL,fv,bN,fs),co,fw),bs,_(),bG,_(),ci,bh)],dB,bF)],dB,bh)],dB,bh)])),fx,_(),fy,_(fz,_(fA,fB),fC,_(fA,fD),fE,_(fA,fF),fG,_(fA,fH),fI,_(fA,fJ),fK,_(fA,fL),fM,_(fA,fN),fO,_(fA,fP),fQ,_(fA,fR),fS,_(fA,fT),fU,_(fA,fV),fW,_(fA,fX),fY,_(fA,fZ)));}; 
var b="url",c="策略说明新增.html",d="generationDate",e=new Date(1747988956362.09),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="currentTime",s="huanmanxielou",t="Checkbox_2",u="Checkbox_3",v="page",w="packageId",x="********************************",y="type",z="Axure:Page",A="策略说明新增",B="notes",C="annotations",D="style",E="baseStyle",F="627587b6038d43cca051c114ac41ad32",G="pageAlignment",H="center",I="fill",J="fillType",K="solid",L="color",M=0xFFFFFFFF,N="image",O="imageAlignment",P="near",Q="imageRepeat",R="auto",S="favicon",T="sketchFactor",U="0",V="colorStyle",W="appliedColor",X="fontName",Y="Applied Font",Z="borderWidth",ba="borderVisibility",bb="borderFill",bc=0xFF797979,bd="cornerRadius",be="cornerVisibility",bf="outerShadow",bg="on",bh=false,bi="offsetX",bj=5,bk="offsetY",bl="blurRadius",bm="r",bn=0,bo="g",bp="b",bq="a",br=0.349019607843137,bs="adaptiveStyles",bt="interactionMap",bu="diagram",bv="objects",bw="id",bx="e6d6228985f84dc18c6947784781ca98",by="label",bz="策略说明",bA="friendlyType",bB="组合",bC="layer",bD="styleType",bE="visible",bF=true,bG="imageOverrides",bH="objs",bI="0a51085000bd451790706b5a06372e22",bJ="编辑",bK="location",bL="x",bM=569,bN="y",bO=226,bP="9541cc4a6d4d4811a931e544fcd6e587",bQ="主体框",bR="矩形",bS="vectorShape",bT="'黑体'",bU="fontWeight",bV="400",bW=775,bX=277,bY="175041b32ed04479b41fd79c36e2b057",bZ=434,ca=193,cb="fontSize",cc="14px",cd="1",ce=0x40797979,cf="images",cg="normal~",ch="images/用户属性管理/主体框_u11646.svg",ci="generateCompound",cj="7cc952ec65814c85843650ffdfd2456e",ck=776,cl=31,cm=0xFFFEFEFF,cn=162,co="horizontalAlignment",cp="left",cq="images/审批通知模板/u278.svg",cr="3cfc085f1d424c4da802bfa186cb8841",cs="按钮",ct=488,cu="115019c863bb4564b0ce0daf200d266a",cv="foreGroundFill",cw=0xFF606266,cx="opacity",cy=1,cz=60,cA=32,cB="033e195fe17b4b8482606377675dd19a",cC=0xFFDCDFE6,cD="4",cE="stateStyles",cF="mouseOver",cG=0xFF409EFF,cH=0xFFECF5FF,cI=0xFFC6E2FF,cJ="mouseDown",cK=0xFF3A8EE6,cL="linePattern",cM="disabled",cN=0xFFC0C4CC,cO=0xFFEBEEF5,cP=1061,cQ=430,cR="onClick",cS="description",cT="Click时 ",cU="cases",cV="conditionString",cW="isNewIfGroup",cX="caseColorHex",cY="9D33FA",cZ="actions",da="action",db="fadeWidget",dc="隐藏 编辑",dd="displayName",de="显示/隐藏",df="actionInfoDescriptions",dg="objectsToFades",dh="objectPath",di="fadeInfo",dj="fadeType",dk="hide",dl="options",dm="showType",dn="none",dp="bringToFront",dq="tabbable",dr="4a77beb0545d46dc9418359df0a46af0",ds="线段",dt="horizontalLine",du="619b2148ccc1497285562264d51992f9",dv=424,dw=0x6F707070,dx="images/审批通知模板/u310.svg",dy="e8ddeb759dda4fb69eacc684f2242994",dz=1132,dA=0xFF145FFF,dB="propagate",dC="38d74f3d3fbf452f9369ae85378f22aa",dD=566,dE=656,dF="e55c969344584a4abacc88ae436eb21b",dG=604,dH=176,dI="b6e25c05c2cf4d1096e0e772d33f6983",dJ=526,dK=214,dL="selected",dM="8898d438cd9547d9819c0e4574336597",dN="多行-限制长度",dO="文本域",dP="textArea",dQ=576,dR=150.857142857143,dS="hint",dT="14f03900eb8b4ec99b22adfbfc5c9350",dU="966109b2377a47958631dfd70efb0bb6",dV=540,dW=226.571428571429,dX="HideHintOnFocused",dY="onTextChange",dZ="TextChange时 ",ea="setFunction",eb="设置 文字于 长文本 计数等于&quot;[[LVAR1.length]]&quot;",ec="设置文本",ed="长文本 计数 为 \"[[LVAR1.length]]\"",ee="文字于 长文本 计数等于\"[[LVAR1.length]]\"",ef="expr",eg="exprType",eh="block",ei="subExprs",ej="fcall",ek="functionName",el="SetWidgetRichText",em="arguments",en="pathLiteral",eo="isThis",ep="isFocused",eq="isTarget",er="value",es="8e628b34c8f54df4bd3134440237a08e",et="stringLiteral",eu="[[LVAR1.length]]",ev="localVariables",ew="lvar1",ex="GetWidgetText",ey="stos",ez="computedType",eA="int",eB="sto",eC="propCall",eD="thisSTO",eE="desiredType",eF="string",eG="var",eH="prop",eI="length",eJ="booleanLiteral",eK="设置 文字于 多行-限制长度等于&quot;[[LVAR1.substr(0,30)]]&quot;",eL="多行-限制长度 为 \"[[LVAR1.substr(0,30)]]\"",eM="文字于 多行-限制长度等于\"[[LVAR1.substr(0,30)]]\"",eN="SetWidgetFormText",eO="[[LVAR1.substr(0,30)]]",eP="fCall",eQ="func",eR="substr",eS="literal",eT=30,eU="onFocus",eV="获取焦点时 ",eW="设置&nbsp; 选中状态于 (矩形)等于&quot;真&quot;",eX="设置选中",eY="(矩形) 为 \"真\"",eZ=" 选中状态于 (矩形)等于\"真\"",fa="SetCheckState",fb="true",fc="onLostFocus",fd="LostFocus时 ",fe="设置&nbsp; 选中状态于 (矩形)等于&quot;假&quot;",ff="(矩形) 为 \"假\"",fg=" 选中状态于 (矩形)等于\"假\"",fh="false",fi="placeholderText",fj="请输入内容",fk="8b3c3c2d39cb490985bdaf3f90068016",fl="'Microsoft YaHei UI'",fm=0xFF909399,fn=28,fo=21,fp="daabdf294b764ecb8b0bc3c5ddcc6e40",fq="12px",fr=1056,fs=361,ft="长文本 计数",fu=11,fv=1037,fw="right",fx="masters",fy="objectPaths",fz="e6d6228985f84dc18c6947784781ca98",fA="scriptId",fB="u14432",fC="0a51085000bd451790706b5a06372e22",fD="u14433",fE="9541cc4a6d4d4811a931e544fcd6e587",fF="u14434",fG="7cc952ec65814c85843650ffdfd2456e",fH="u14435",fI="3cfc085f1d424c4da802bfa186cb8841",fJ="u14436",fK="115019c863bb4564b0ce0daf200d266a",fL="u14437",fM="4a77beb0545d46dc9418359df0a46af0",fN="u14438",fO="e8ddeb759dda4fb69eacc684f2242994",fP="u14439",fQ="38d74f3d3fbf452f9369ae85378f22aa",fR="u14440",fS="e55c969344584a4abacc88ae436eb21b",fT="u14441",fU="8898d438cd9547d9819c0e4574336597",fV="u14442",fW="8b3c3c2d39cb490985bdaf3f90068016",fX="u14443",fY="8e628b34c8f54df4bd3134440237a08e",fZ="u14444";
return _creator();
})());