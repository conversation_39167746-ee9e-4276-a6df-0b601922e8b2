﻿<!DOCTYPE html>
<html>
  <head>
    <title>新增报告</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/新增报告/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/新增报告/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- 新增报告 (动态面板) -->
      <div id="u8555" class="ax_default" data-label="新增报告">
        <div id="u8555_state0" class="panel_state" data-label="快速报告" style="">
          <div id="u8555_state0_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u8556" class="ax_default box_1">
              <div id="u8556_div" class=""></div>
              <div id="u8556_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u8557" class="ax_default box_1">
              <div id="u8557_div" class=""></div>
              <div id="u8557_text" class="text ">
                <p><span>&nbsp;新增报告</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u8558" class="ax_default _图片_">
              <img id="u8558_img" class="img " src="images/样本采集/u822.png"/>
              <div id="u8558_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u8559" class="ax_default label">
              <div id="u8559_div" class=""></div>
              <div id="u8559_text" class="text ">
                <p><span style="color:#D9001B;">*</span><span>报告名称：</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u8560" class="ax_default label">
              <div id="u8560_div" class=""></div>
              <div id="u8560_text" class="text ">
                <p><span>&nbsp;报告描述</span><span>：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u8561" class="ax_default text_field1">
              <div id="u8561_div" class=""></div>
              <input id="u8561_input" type="text" value="请输入报告名称" class="u8561_input"/>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u8562" class="ax_default text_field1">
              <div id="u8562_div" class=""></div>
              <input id="u8562_input" type="text" value="请输入报告描述" class="u8562_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u8563" class="ax_default label">
              <div id="u8563_div" class=""></div>
              <div id="u8563_text" class="text ">
                <p><span style="color:#D9001B;">*</span><span style="color:#0B0B0A;">报告模板：</span></p>
              </div>
            </div>

            <!-- Unnamed (下拉列表) -->
            <div id="u8564" class="ax_default droplist">
              <div id="u8564_div" class=""></div>
              <select id="u8564_input" class="u8564_input">
                <option class="u8564_input_option" value="请选择报告模板">请选择报告模板</option>
              </select>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u8565" class="ax_default label">
              <div id="u8565_div" class=""></div>
              <div id="u8565_text" class="text ">
                <p><span>报告基本信息</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u8566" class="ax_default label">
              <div id="u8566_div" class=""></div>
              <div id="u8566_text" class="text ">
                <p><span>报告订阅</span></p>
              </div>
            </div>

            <!-- Unnamed (线段) -->
            <div id="u8567" class="ax_default line">
              <img id="u8567_img" class="img " src="images/智能报告管理/u8432.svg"/>
              <div id="u8567_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (线段) -->
            <div id="u8568" class="ax_default line">
              <img id="u8568_img" class="img " src="images/智能报告管理/u8433.svg"/>
              <div id="u8568_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- 发送方式 (组合) -->
            <div id="u8569" class="ax_default" data-label="发送方式" data-left="68" data-top="475" data-width="550" data-height="243">

              <!-- Unnamed (组合) -->
              <div id="u8570" class="ax_default" data-left="215" data-top="481" data-width="35" data-height="19">

                <!-- 开关开启 (形状) -->
                <div id="u8571" class="ax_default icon" data-label="开关开启">
                  <img id="u8571_img" class="img " src="images/数据字典/开关开启_u6852.svg"/>
                  <div id="u8571_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- 开关关闭 (形状) -->
                <div id="u8572" class="ax_default icon" data-label="开关关闭">
                  <img id="u8572_img" class="img " src="images/数据字典/开关关闭_u6853.svg"/>
                  <div id="u8572_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>
              </div>

              <!-- 邮件发送 (动态面板) -->
              <div id="u8573" class="ax_default" data-label="邮件发送">
                <div id="u8573_state0" class="panel_state" data-label="State1" style="">
                  <div id="u8573_state0_content" class="panel_state_content">

                    <!-- Unnamed (矩形) -->
                    <div id="u8574" class="ax_default label">
                      <div id="u8574_div" class=""></div>
                      <div id="u8574_text" class="text ">
                        <p><span style="color:#D9001B;">&nbsp;*</span><span style="color:#000000;">收件人邮箱</span><span style="color:#0B0B0A;">：</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (文本框) -->
                    <div id="u8575" class="ax_default text_field1">
                      <div id="u8575_div" class=""></div>
                      <input id="u8575_input" type="text" value="请输入通知人邮箱" class="u8575_input"/>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u8576" class="ax_default label">
                      <div id="u8576_div" class=""></div>
                      <div id="u8576_text" class="text ">
                        <p><span style="color:#000000;">邮件内容</span><span style="color:#0B0B0A;">：</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u8577" class="ax_default label">
                      <div id="u8577_div" class=""></div>
                      <div id="u8577_text" class="text ">
                        <p><span>邮件主题：</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (图片 ) -->
                    <div id="u8578" class="ax_default _图片_">
                      <img id="u8578_img" class="img " src="images/智能报告管理/u8443.png"/>
                      <div id="u8578_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>

                    <!-- Unnamed (文本框) -->
                    <div id="u8579" class="ax_default text_field1">
                      <div id="u8579_div" class=""></div>
                      <input id="u8579_input" type="text" value="请输入邮件内容" class="u8579_input"/>
                    </div>

                    <!-- Unnamed (文本框) -->
                    <div id="u8580" class="ax_default text_field1">
                      <div id="u8580_div" class=""></div>
                      <input id="u8580_input" type="text" value="请输入邮件主题" class="u8580_input"/>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u8581" class="ax_default label">
                      <div id="u8581_div" class=""></div>
                      <div id="u8581_text" class="text ">
                        <p><span style="color:#D9001B;">&nbsp;*</span><span style="color:#000000;">抄送人邮箱</span><span style="color:#0B0B0A;">：</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (文本框) -->
                    <div id="u8582" class="ax_default text_field1">
                      <div id="u8582_div" class=""></div>
                      <input id="u8582_input" type="text" value="请输入通知人邮箱" class="u8582_input"/>
                    </div>

                    <!-- Unnamed (图片 ) -->
                    <div id="u8583" class="ax_default _图片_">
                      <img id="u8583_img" class="img " src="images/智能报告管理/u8443.png"/>
                      <div id="u8583_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Unnamed (矩形) -->
              <div id="u8584" class="ax_default label">
                <div id="u8584_div" class=""></div>
                <div id="u8584_text" class="text ">
                  <p><span style="color:#D9001B;">*</span><span>是否开启邮件发送：</span></p>
                </div>
              </div>

              <!-- 上级领导 (组合) -->
              <div id="u8585" class="ax_default" data-label="上级领导" data-left="428" data-top="475" data-width="154" data-height="25">

                <!-- Unnamed (组合) -->
                <div id="u8586" class="ax_default" data-left="547" data-top="478" data-width="35" data-height="19">

                  <!-- 开关开启 (形状) -->
                  <div id="u8587" class="ax_default icon" data-label="开关开启">
                    <img id="u8587_img" class="img " src="images/数据字典/开关开启_u6852.svg"/>
                    <div id="u8587_text" class="text " style="display:none; visibility: hidden">
                      <p></p>
                    </div>
                  </div>

                  <!-- 开关关闭 (形状) -->
                  <div id="u8588" class="ax_default icon" data-label="开关关闭">
                    <img id="u8588_img" class="img " src="images/数据字典/开关关闭_u6853.svg"/>
                    <div id="u8588_text" class="text " style="display:none; visibility: hidden">
                      <p></p>
                    </div>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u8589" class="ax_default label">
                  <div id="u8589_div" class=""></div>
                  <div id="u8589_text" class="text ">
                    <p><span style="color:#D9001B;">*</span><span>抄送上级领导：</span></p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u8590" class="ax_default label">
              <div id="u8590_div" class=""></div>
              <div id="u8590_text" class="text ">
                <p><span style="color:#D9001B;">*</span><span style="color:#000000;">报告时间范围：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u8591" class="ax_default text_field1">
              <div id="u8591_div" class=""></div>
              <input id="u8591_input" type="text" value="请输入报告时间区间" class="u8591_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u8592" class="ax_default label">
              <div id="u8592_div" class=""></div>
              <div id="u8592_text" class="text ">
                <p><span style="color:#D9001B;">&nbsp;*</span><span style="color:#0B0B0A;">报告类型：</span></p>
              </div>
            </div>

            <!-- Unnamed (单选按钮) -->
            <div id="u8593" class="ax_default radio_button selected">
              <label id="u8593_input_label" for="u8593_input" style="position: absolute; left: 0px;">
                <img id="u8593_img" class="img " src="images/智能报告管理/u8458_selected.svg"/>
                <div id="u8593_text" class="text ">
                  <p><span>快速报告</span></p>
                </div>
              </label>
              <input id="u8593_input" type="radio" value="radio" name="u8593" checked/>
            </div>

            <!-- Unnamed (单选按钮) -->
            <div id="u8594" class="ax_default radio_button">
              <label id="u8594_input_label" for="u8594_input" style="position: absolute; left: 0px;">
                <img id="u8594_img" class="img " src="images/智能报告管理/u8459.svg"/>
                <div id="u8594_text" class="text ">
                  <p><span>周期报告</span></p>
                </div>
              </label>
              <input id="u8594_input" type="radio" value="radio" name="u8594"/>
            </div>

            <!-- Unnamed (组合) -->
            <div id="u8595" class="ax_default" data-left="128" data-top="197" data-width="437" data-height="25">

              <!-- Unnamed (矩形) -->
              <div id="u8596" class="ax_default label">
                <div id="u8596_div" class=""></div>
                <div id="u8596_text" class="text ">
                  <p><span style="color:#D9001B;">*</span><span style="color:#0B0B0A;">报告格式：</span></p>
                </div>
              </div>

              <!-- Unnamed (单选按钮) -->
              <div id="u8597" class="ax_default radio_button">
                <label id="u8597_input_label" for="u8597_input" style="position: absolute; left: 0px;">
                  <img id="u8597_img" class="img " src="images/智能报告管理/u8462.svg"/>
                  <div id="u8597_text" class="text ">
                    <p><span>word</span></p>
                  </div>
                </label>
                <input id="u8597_input" type="radio" value="radio" name="u8597"/>
              </div>

              <!-- Unnamed (单选按钮) -->
              <div id="u8598" class="ax_default radio_button">
                <label id="u8598_input_label" for="u8598_input" style="position: absolute; left: 0px;">
                  <img id="u8598_img" class="img " src="images/智能报告管理/u8463.svg"/>
                  <div id="u8598_text" class="text ">
                    <p><span>pdf</span></p>
                  </div>
                </label>
                <input id="u8598_input" type="radio" value="radio" name="u8598"/>
              </div>

              <!-- Unnamed (单选按钮) -->
              <div id="u8599" class="ax_default radio_button">
                <label id="u8599_input_label" for="u8599_input" style="position: absolute; left: 0px;">
                  <img id="u8599_img" class="img " src="images/智能报告管理/u8464.svg"/>
                  <div id="u8599_text" class="text ">
                    <p><span>ppt</span></p>
                  </div>
                </label>
                <input id="u8599_input" type="radio" value="radio" name="u8599"/>
              </div>

              <!-- Unnamed (单选按钮) -->
              <div id="u8600" class="ax_default radio_button">
                <label id="u8600_input_label" for="u8600_input" style="position: absolute; left: 0px;">
                  <img id="u8600_img" class="img " src="images/智能报告管理/u8465.svg"/>
                  <div id="u8600_text" class="text ">
                    <p><span>html</span></p>
                  </div>
                </label>
                <input id="u8600_input" type="radio" value="radio" name="u8600"/>
              </div>
            </div>

            <!-- 操作按钮 (组合) -->
            <div id="u8601" class="ax_default" data-label="操作按钮" data-left="504" data-top="738" data-width="269" data-height="27">

              <!-- Unnamed (矩形) -->
              <div id="u8602" class="ax_default button">
                <div id="u8602_div" class=""></div>
                <div id="u8602_text" class="text ">
                  <p><span>报告预览</span></p>
                </div>
              </div>

              <!-- Unnamed (矩形) -->
              <div id="u8603" class="ax_default button">
                <div id="u8603_div" class=""></div>
                <div id="u8603_text" class="text ">
                  <p><span>确定</span></p>
                </div>
              </div>

              <!-- Unnamed (矩形) -->
              <div id="u8604" class="ax_default button">
                <div id="u8604_div" class=""></div>
                <div id="u8604_text" class="text ">
                  <p><span>取消</span></p>
                </div>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u8605" class="ax_default label">
              <div id="u8605_div" class=""></div>
              <div id="u8605_text" class="text ">
                <p><span>&nbsp;订阅方式</span><span>：</span></p>
              </div>
            </div>

            <!-- 帐号管理员--模板 (组合) -->
            <div id="u8606" class="ax_default" data-label="帐号管理员--模板" data-left="129" data-top="377" data-width="463" data-height="80">

              <!-- 账户管理员-动态面板组合 (组合) -->
              <div id="u8607" class="ax_default" data-label="账户管理员-动态面板组合" data-left="129" data-top="377" data-width="463" data-height="80">

                <!-- 账户管理员 (动态面板) -->
                <div id="u8608" class="ax_default" data-label="账户管理员">
                  <div id="u8608_state0" class="panel_state" data-label="State1" style="">
                    <div id="u8608_state0_content" class="panel_state_content">
                    </div>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u8609" class="ax_default label">
                  <div id="u8609_div" class=""></div>
                  <div id="u8609_text" class="text ">
                    <p><span style="color:#D9001B;">&nbsp;</span><span style="color:#000000;">用户</span><span style="color:#0B0B0A;">：</span></p>
                  </div>
                </div>

                <!-- Unnamed (下拉列表) -->
                <div id="u8610" class="ax_default droplist">
                  <div id="u8610_div" class=""></div>
                  <select id="u8610_input" class="u8610_input">
                    <option class="u8610_input_option" value="请选择用户，支持多选">请选择用户，支持多选</option>
                  </select>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u8611" class="ax_default label">
                  <div id="u8611_div" class=""></div>
                  <div id="u8611_text" class="text ">
                    <p><span style="color:#D9001B;">&nbsp;</span><span style="color:#000000;">用户组</span><span style="color:#0B0B0A;">：</span></p>
                  </div>
                </div>

                <!-- Unnamed (下拉列表) -->
                <div id="u8612" class="ax_default droplist">
                  <div id="u8612_div" class=""></div>
                  <select id="u8612_input" class="u8612_input">
                    <option class="u8612_input_option" value="请选择用户，支持多选">请选择用户，支持多选</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Unnamed (组合) -->
            <div id="u8613" class="ax_default" selectiongroup="单选框组" data-left="221" data-top="348" data-width="62" data-height="25">

              <!-- Unnamed (矩形) -->
              <div id="u8614" class="ax_default label">
                <div id="u8614_div" class=""></div>
                <div id="u8614_text" class="text ">
                  <p><span>admin</span></p>
                </div>
              </div>

              <!-- Unnamed (圆形) -->
              <div id="u8615" class="ax_default ellipse">
                <img id="u8615_img" class="img " src="images/审批通知模板/u292.svg"/>
                <div id="u8615_text" class="text " style="display:none; visibility: hidden">
                  <p></p>
                </div>
              </div>
            </div>

            <!-- Unnamed (组合) -->
            <div id="u8616" class="ax_default" selectiongroup="单选框组" data-left="309" data-top="348" data-width="103" data-height="25">

              <!-- Unnamed (矩形) -->
              <div id="u8617" class="ax_default label">
                <div id="u8617_div" class=""></div>
                <div id="u8617_text" class="text ">
                  <p><span>自定义管理员</span></p>
                </div>
              </div>

              <!-- Unnamed (圆形) -->
              <div id="u8618" class="ax_default ellipse">
                <img id="u8618_img" class="img " src="images/审批通知模板/u292.svg"/>
                <div id="u8618_text" class="text " style="display:none; visibility: hidden">
                  <p></p>
                </div>
              </div>
            </div>

            <!-- 自定义管理员-动态面板组合 (组合) -->
            <div id="u8619" class="ax_default ax_default_hidden" data-label="自定义管理员-动态面板组合" style="display:none; visibility: hidden" data-left="129" data-top="377" data-width="463" data-height="80">

              <!-- 账户管理员 (动态面板) -->
              <div id="u8620" class="ax_default" data-label="账户管理员">
                <div id="u8620_state0" class="panel_state" data-label="State1" style="">
                  <div id="u8620_state0_content" class="panel_state_content">
                  </div>
                </div>
              </div>

              <!-- Unnamed (矩形) -->
              <div id="u8621" class="ax_default label">
                <div id="u8621_div" class=""></div>
                <div id="u8621_text" class="text ">
                  <p><span style="color:#D9001B;">&nbsp;</span><span style="color:#080808;">管理员</span><span style="color:#0B0B0A;">：</span></p>
                </div>
              </div>

              <!-- Unnamed (下拉列表) -->
              <div id="u8622" class="ax_default droplist">
                <div id="u8622_div" class=""></div>
                <select id="u8622_input" class="u8622_input">
                  <option class="u8622_input_option" value="请选择用户，支持多选">请选择用户，支持多选</option>
                </select>
              </div>
            </div>
          </div>
        </div>
        <div id="u8555_state1" class="panel_state" data-label="周期报告" style="visibility: hidden;">
          <div id="u8555_state1_content" class="panel_state_content">

            <!-- Unnamed (矩形) -->
            <div id="u8623" class="ax_default box_1">
              <div id="u8623_div" class=""></div>
              <div id="u8623_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u8624" class="ax_default box_1">
              <div id="u8624_div" class=""></div>
              <div id="u8624_text" class="text ">
                <p><span>&nbsp;新增报告</span></p>
              </div>
            </div>

            <!-- Unnamed (图片 ) -->
            <div id="u8625" class="ax_default _图片_">
              <img id="u8625_img" class="img " src="images/样本采集/u822.png"/>
              <div id="u8625_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u8626" class="ax_default label">
              <div id="u8626_div" class=""></div>
              <div id="u8626_text" class="text ">
                <p><span style="color:#D9001B;">*</span><span>报告名称：</span></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u8627" class="ax_default label">
              <div id="u8627_div" class=""></div>
              <div id="u8627_text" class="text ">
                <p><span>&nbsp;报告描述</span><span>：</span></p>
              </div>
            </div>

            <!-- 周期报告显示 (动态面板) -->
            <div id="u8628" class="ax_default" data-label="周期报告显示">
              <div id="u8628_state0" class="panel_state" data-label="State1" style="">
                <div id="u8628_state0_content" class="panel_state_content">

                  <!-- Unnamed (矩形) -->
                  <div id="u8629" class="ax_default label">
                    <div id="u8629_div" class=""></div>
                    <div id="u8629_text" class="text ">
                      <p><span style="color:#D9001B;">*</span><span>报告周期：</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (矩形) -->
                  <div id="u8630" class="ax_default label">
                    <div id="u8630_div" class=""></div>
                    <div id="u8630_text" class="text ">
                      <p><span style="color:#D9001B;">*</span><span style="color:#000000;">报告时</span><span style="color:#0B0B0A;">间：</span></p>
                    </div>
                  </div>

                  <!-- Unnamed (下拉列表) -->
                  <div id="u8631" class="ax_default droplist">
                    <div id="u8631_div" class=""></div>
                    <select id="u8631_input" class="u8631_input">
                      <option class="u8631_input_option" value="日报">日报</option>
                      <option class="u8631_input_option" value="周报">周报</option>
                      <option class="u8631_input_option" value="月报">月报</option>
                      <option class="u8631_input_option" value="季报">季报</option>
                      <option class="u8631_input_option" value="年报">年报</option>
                    </select>
                  </div>

                  <!-- Unnamed (下拉列表) -->
                  <div id="u8632" class="ax_default droplist">
                    <div id="u8632_div" class=""></div>
                    <select id="u8632_input" class="u8632_input">
                      <option class="u8632_input_option" value="请选择报告时间">请选择报告时间</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u8633" class="ax_default text_field1">
              <div id="u8633_div" class=""></div>
              <input id="u8633_input" type="text" value="请输入报告名称" class="u8633_input"/>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u8634" class="ax_default text_field1">
              <div id="u8634_div" class=""></div>
              <input id="u8634_input" type="text" value="请输入报告描述" class="u8634_input"/>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u8635" class="ax_default label">
              <div id="u8635_div" class=""></div>
              <div id="u8635_text" class="text ">
                <p><span>报告基本信息</span></p>
              </div>
            </div>

            <!-- Unnamed (线段) -->
            <div id="u8636" class="ax_default line">
              <img id="u8636_img" class="img " src="images/智能报告管理/u8432.svg"/>
              <div id="u8636_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u8637" class="ax_default label">
              <div id="u8637_div" class=""></div>
              <div id="u8637_text" class="text ">
                <p><span>&nbsp;报告类型：</span></p>
              </div>
            </div>

            <!-- Unnamed (单选按钮) -->
            <div id="u8638" class="ax_default radio_button">
              <label id="u8638_input_label" for="u8638_input" style="position: absolute; left: 0px;">
                <img id="u8638_img" class="img " src="images/智能报告管理/u8503.svg"/>
                <div id="u8638_text" class="text ">
                  <p><span>快速报告</span></p>
                </div>
              </label>
              <input id="u8638_input" type="radio" value="radio" name="u8638"/>
            </div>

            <!-- Unnamed (单选按钮) -->
            <div id="u8639" class="ax_default radio_button selected">
              <label id="u8639_input_label" for="u8639_input" style="position: absolute; left: 0px;">
                <img id="u8639_img" class="img " src="images/智能报告管理/u8504_selected.svg"/>
                <div id="u8639_text" class="text ">
                  <p><span>周期报告</span></p>
                </div>
              </label>
              <input id="u8639_input" type="radio" value="radio" name="u8639" checked/>
            </div>

            <!-- Unnamed (组合) -->
            <div id="u8640" class="ax_default" data-left="128" data-top="265" data-width="382" data-height="25">

              <!-- Unnamed (矩形) -->
              <div id="u8641" class="ax_default label">
                <div id="u8641_div" class=""></div>
                <div id="u8641_text" class="text ">
                  <p><span style="color:#D9001B;">*</span><span style="color:#0B0B0A;">报告格式：</span></p>
                </div>
              </div>

              <!-- Unnamed (单选按钮) -->
              <div id="u8642" class="ax_default radio_button">
                <label id="u8642_input_label" for="u8642_input" style="position: absolute; left: 0px;">
                  <img id="u8642_img" class="img " src="images/智能报告管理/u8507.svg"/>
                  <div id="u8642_text" class="text ">
                    <p><span>word</span></p>
                  </div>
                </label>
                <input id="u8642_input" type="radio" value="radio" name="u8642"/>
              </div>

              <!-- Unnamed (单选按钮) -->
              <div id="u8643" class="ax_default radio_button">
                <label id="u8643_input_label" for="u8643_input" style="position: absolute; left: 0px;">
                  <img id="u8643_img" class="img " src="images/智能报告管理/u8508.svg"/>
                  <div id="u8643_text" class="text ">
                    <p><span>pdf</span></p>
                  </div>
                </label>
                <input id="u8643_input" type="radio" value="radio" name="u8643"/>
              </div>

              <!-- Unnamed (单选按钮) -->
              <div id="u8644" class="ax_default radio_button">
                <label id="u8644_input_label" for="u8644_input" style="position: absolute; left: 0px;">
                  <img id="u8644_img" class="img " src="images/智能报告管理/u8509.svg"/>
                  <div id="u8644_text" class="text ">
                    <p><span>ppt</span></p>
                  </div>
                </label>
                <input id="u8644_input" type="radio" value="radio" name="u8644"/>
              </div>

              <!-- Unnamed (单选按钮) -->
              <div id="u8645" class="ax_default radio_button">
                <label id="u8645_input_label" for="u8645_input" style="position: absolute; left: 0px;">
                  <img id="u8645_img" class="img " src="images/智能报告管理/u8510.svg"/>
                  <div id="u8645_text" class="text ">
                    <p><span>html</span></p>
                  </div>
                </label>
                <input id="u8645_input" type="radio" value="radio" name="u8645"/>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u8646" class="ax_default label">
              <div id="u8646_div" class=""></div>
              <div id="u8646_text" class="text ">
                <p><span style="color:#D9001B;">*</span><span style="color:#0B0B0A;">报告模板：</span></p>
              </div>
            </div>

            <!-- Unnamed (下拉列表) -->
            <div id="u8647" class="ax_default droplist">
              <div id="u8647_div" class=""></div>
              <select id="u8647_input" class="u8647_input">
                <option class="u8647_input_option" value="请选择报告模板">请选择报告模板</option>
              </select>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u8648" class="ax_default label">
              <div id="u8648_div" class=""></div>
              <div id="u8648_text" class="text ">
                <p><span>报告订阅</span></p>
              </div>
            </div>

            <!-- Unnamed (线段) -->
            <div id="u8649" class="ax_default line">
              <img id="u8649_img" class="img " src="images/智能报告管理/u8433.svg"/>
              <div id="u8649_text" class="text " style="display:none; visibility: hidden">
                <p></p>
              </div>
            </div>

            <!-- 发送方式 (组合) -->
            <div id="u8650" class="ax_default" data-label="发送方式" data-left="68" data-top="535" data-width="550" data-height="240">

              <!-- Unnamed (组合) -->
              <div id="u8651" class="ax_default" data-left="215" data-top="538" data-width="35" data-height="19">

                <!-- 开关开启 (形状) -->
                <div id="u8652" class="ax_default icon" data-label="开关开启">
                  <img id="u8652_img" class="img " src="images/数据字典/开关开启_u6852.svg"/>
                  <div id="u8652_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>

                <!-- 开关关闭 (形状) -->
                <div id="u8653" class="ax_default icon" data-label="开关关闭">
                  <img id="u8653_img" class="img " src="images/数据字典/开关关闭_u6853.svg"/>
                  <div id="u8653_text" class="text " style="display:none; visibility: hidden">
                    <p></p>
                  </div>
                </div>
              </div>

              <!-- 邮件发送 (动态面板) -->
              <div id="u8654" class="ax_default" data-label="邮件发送">
                <div id="u8654_state0" class="panel_state" data-label="State1" style="">
                  <div id="u8654_state0_content" class="panel_state_content">

                    <!-- Unnamed (矩形) -->
                    <div id="u8655" class="ax_default label">
                      <div id="u8655_div" class=""></div>
                      <div id="u8655_text" class="text ">
                        <p><span style="color:#D9001B;">&nbsp;*</span><span style="color:#000000;">收件人邮箱</span><span style="color:#0B0B0A;">：</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (文本框) -->
                    <div id="u8656" class="ax_default text_field1">
                      <div id="u8656_div" class=""></div>
                      <input id="u8656_input" type="text" value="请输入通知人邮箱" class="u8656_input"/>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u8657" class="ax_default label">
                      <div id="u8657_div" class=""></div>
                      <div id="u8657_text" class="text ">
                        <p><span style="color:#000000;">邮件内容</span><span style="color:#0B0B0A;">：</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u8658" class="ax_default label">
                      <div id="u8658_div" class=""></div>
                      <div id="u8658_text" class="text ">
                        <p><span>邮件主题：</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (图片 ) -->
                    <div id="u8659" class="ax_default _图片_">
                      <img id="u8659_img" class="img " src="images/智能报告管理/u8443.png"/>
                      <div id="u8659_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>

                    <!-- Unnamed (文本框) -->
                    <div id="u8660" class="ax_default text_field1">
                      <div id="u8660_div" class=""></div>
                      <input id="u8660_input" type="text" value="请输入邮件内容" class="u8660_input"/>
                    </div>

                    <!-- Unnamed (文本框) -->
                    <div id="u8661" class="ax_default text_field1">
                      <div id="u8661_div" class=""></div>
                      <input id="u8661_input" type="text" value="请输入邮件主题" class="u8661_input"/>
                    </div>

                    <!-- Unnamed (矩形) -->
                    <div id="u8662" class="ax_default label">
                      <div id="u8662_div" class=""></div>
                      <div id="u8662_text" class="text ">
                        <p><span style="color:#D9001B;">&nbsp;*</span><span style="color:#000000;">抄送人邮箱</span><span style="color:#0B0B0A;">：</span></p>
                      </div>
                    </div>

                    <!-- Unnamed (文本框) -->
                    <div id="u8663" class="ax_default text_field1">
                      <div id="u8663_div" class=""></div>
                      <input id="u8663_input" type="text" value="请输入通知人邮箱" class="u8663_input"/>
                    </div>

                    <!-- Unnamed (图片 ) -->
                    <div id="u8664" class="ax_default _图片_">
                      <img id="u8664_img" class="img " src="images/智能报告管理/u8443.png"/>
                      <div id="u8664_text" class="text " style="display:none; visibility: hidden">
                        <p></p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Unnamed (矩形) -->
              <div id="u8665" class="ax_default label">
                <div id="u8665_div" class=""></div>
                <div id="u8665_text" class="text ">
                  <p><span style="color:#D9001B;">*</span><span>是否开启邮件发送：</span></p>
                </div>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u8666" class="ax_default label">
              <div id="u8666_div" class=""></div>
              <div id="u8666_text" class="text ">
                <p><span style="color:#D9001B;">*</span><span style="color:#000000;">报告时间范围：</span></p>
              </div>
            </div>

            <!-- Unnamed (文本框) -->
            <div id="u8667" class="ax_default text_field1">
              <div id="u8667_div" class=""></div>
              <input id="u8667_input" type="text" value="请输入报告时间区间" class="u8667_input"/>
            </div>

            <!-- 操作按钮 (组合) -->
            <div id="u8668" class="ax_default" data-label="操作按钮" data-left="504" data-top="795" data-width="269" data-height="27">

              <!-- Unnamed (矩形) -->
              <div id="u8669" class="ax_default button">
                <div id="u8669_div" class=""></div>
                <div id="u8669_text" class="text ">
                  <p><span>报告预览</span></p>
                </div>
              </div>

              <!-- Unnamed (矩形) -->
              <div id="u8670" class="ax_default button">
                <div id="u8670_div" class=""></div>
                <div id="u8670_text" class="text ">
                  <p><span>确定</span></p>
                </div>
              </div>

              <!-- Unnamed (矩形) -->
              <div id="u8671" class="ax_default button">
                <div id="u8671_div" class=""></div>
                <div id="u8671_text" class="text ">
                  <p><span>取消</span></p>
                </div>
              </div>
            </div>

            <!-- Unnamed (矩形) -->
            <div id="u8672" class="ax_default label">
              <div id="u8672_div" class=""></div>
              <div id="u8672_text" class="text ">
                <p><span>&nbsp;订阅方式</span><span>：</span></p>
              </div>
            </div>

            <!-- 帐号管理员--模板 (组合) -->
            <div id="u8673" class="ax_default" data-label="帐号管理员--模板" data-left="129" data-top="434" data-width="463" data-height="80">

              <!-- 账户管理员-动态面板组合 (组合) -->
              <div id="u8674" class="ax_default" data-label="账户管理员-动态面板组合" data-left="129" data-top="434" data-width="463" data-height="80">

                <!-- 账户管理员 (动态面板) -->
                <div id="u8675" class="ax_default" data-label="账户管理员">
                  <div id="u8675_state0" class="panel_state" data-label="State1" style="">
                    <div id="u8675_state0_content" class="panel_state_content">
                    </div>
                  </div>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u8676" class="ax_default label">
                  <div id="u8676_div" class=""></div>
                  <div id="u8676_text" class="text ">
                    <p><span style="color:#D9001B;">&nbsp;</span><span style="color:#000000;">用户</span><span style="color:#0B0B0A;">：</span></p>
                  </div>
                </div>

                <!-- Unnamed (下拉列表) -->
                <div id="u8677" class="ax_default droplist">
                  <div id="u8677_div" class=""></div>
                  <select id="u8677_input" class="u8677_input">
                    <option class="u8677_input_option" value="请选择用户，支持多选">请选择用户，支持多选</option>
                  </select>
                </div>

                <!-- Unnamed (矩形) -->
                <div id="u8678" class="ax_default label">
                  <div id="u8678_div" class=""></div>
                  <div id="u8678_text" class="text ">
                    <p><span style="color:#D9001B;">&nbsp;</span><span style="color:#000000;">用户组</span><span style="color:#0B0B0A;">：</span></p>
                  </div>
                </div>

                <!-- Unnamed (下拉列表) -->
                <div id="u8679" class="ax_default droplist">
                  <div id="u8679_div" class=""></div>
                  <select id="u8679_input" class="u8679_input">
                    <option class="u8679_input_option" value="请选择用户，支持多选">请选择用户，支持多选</option>
                  </select>
                </div>
              </div>
            </div>

            <!-- Unnamed (组合) -->
            <div id="u8680" class="ax_default" selectiongroup="单选框组" data-left="221" data-top="405" data-width="62" data-height="25">

              <!-- Unnamed (矩形) -->
              <div id="u8681" class="ax_default label">
                <div id="u8681_div" class=""></div>
                <div id="u8681_text" class="text ">
                  <p><span>admin</span></p>
                </div>
              </div>

              <!-- Unnamed (圆形) -->
              <div id="u8682" class="ax_default ellipse">
                <img id="u8682_img" class="img " src="images/审批通知模板/u292.svg"/>
                <div id="u8682_text" class="text " style="display:none; visibility: hidden">
                  <p></p>
                </div>
              </div>
            </div>

            <!-- Unnamed (组合) -->
            <div id="u8683" class="ax_default" selectiongroup="单选框组" data-left="309" data-top="405" data-width="103" data-height="25">

              <!-- Unnamed (矩形) -->
              <div id="u8684" class="ax_default label">
                <div id="u8684_div" class=""></div>
                <div id="u8684_text" class="text ">
                  <p><span>自定义管理员</span></p>
                </div>
              </div>

              <!-- Unnamed (圆形) -->
              <div id="u8685" class="ax_default ellipse">
                <img id="u8685_img" class="img " src="images/审批通知模板/u292.svg"/>
                <div id="u8685_text" class="text " style="display:none; visibility: hidden">
                  <p></p>
                </div>
              </div>
            </div>

            <!-- 自定义管理员-动态面板组合 (组合) -->
            <div id="u8686" class="ax_default ax_default_hidden" data-label="自定义管理员-动态面板组合" style="display:none; visibility: hidden" data-left="129" data-top="434" data-width="463" data-height="80">

              <!-- 账户管理员 (动态面板) -->
              <div id="u8687" class="ax_default" data-label="账户管理员">
                <div id="u8687_state0" class="panel_state" data-label="State1" style="">
                  <div id="u8687_state0_content" class="panel_state_content">
                  </div>
                </div>
              </div>

              <!-- Unnamed (矩形) -->
              <div id="u8688" class="ax_default label">
                <div id="u8688_div" class=""></div>
                <div id="u8688_text" class="text ">
                  <p><span style="color:#D9001B;">&nbsp;</span><span style="color:#080808;">管理员</span><span style="color:#0B0B0A;">：</span></p>
                </div>
              </div>

              <!-- Unnamed (下拉列表) -->
              <div id="u8689" class="ax_default droplist">
                <div id="u8689_div" class=""></div>
                <select id="u8689_input" class="u8689_input">
                  <option class="u8689_input_option" value="请选择用户，支持多选">请选择用户，支持多选</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
