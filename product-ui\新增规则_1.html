﻿<!DOCTYPE html>
<html>
  <head>
    <title>新增规则</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <link href="resources/css/axure_rp_page.css" type="text/css" rel="stylesheet"/>
    <link href="data/styles.css" type="text/css" rel="stylesheet"/>
    <link href="files/新增规则_1/styles.css" type="text/css" rel="stylesheet"/>
    <script src="resources/scripts/jquery-3.2.1.min.js"></script>
    <script src="resources/scripts/axure/axQuery.js"></script>
    <script src="resources/scripts/axure/globals.js"></script>
    <script src="resources/scripts/axutils.js"></script>
    <script src="resources/scripts/axure/annotation.js"></script>
    <script src="resources/scripts/axure/axQuery.std.js"></script>
    <script src="resources/scripts/axure/doc.js"></script>
    <script src="resources/scripts/messagecenter.js"></script>
    <script src="resources/scripts/axure/events.js"></script>
    <script src="resources/scripts/axure/recording.js"></script>
    <script src="resources/scripts/axure/action.js"></script>
    <script src="resources/scripts/axure/expr.js"></script>
    <script src="resources/scripts/axure/geometry.js"></script>
    <script src="resources/scripts/axure/flyout.js"></script>
    <script src="resources/scripts/axure/model.js"></script>
    <script src="resources/scripts/axure/repeater.js"></script>
    <script src="resources/scripts/axure/sto.js"></script>
    <script src="resources/scripts/axure/utils.temp.js"></script>
    <script src="resources/scripts/axure/variables.js"></script>
    <script src="resources/scripts/axure/drag.js"></script>
    <script src="resources/scripts/axure/move.js"></script>
    <script src="resources/scripts/axure/visibility.js"></script>
    <script src="resources/scripts/axure/style.js"></script>
    <script src="resources/scripts/axure/adaptive.js"></script>
    <script src="resources/scripts/axure/tree.js"></script>
    <script src="resources/scripts/axure/init.temp.js"></script>
    <script src="resources/scripts/axure/legacy.js"></script>
    <script src="resources/scripts/axure/viewer.js"></script>
    <script src="resources/scripts/axure/math.js"></script>
    <script src="resources/scripts/axure/jquery.nicescroll.min.js"></script>
    <script src="data/document.js"></script>
    <script src="files/新增规则_1/data.js"></script>
    <script type="text/javascript">
      $axure.utils.getTransparentGifPath = function() { return 'resources/images/transparent.gif'; };
      $axure.utils.getOtherPath = function() { return 'resources/Other.html'; };
      $axure.utils.getReloadPath = function() { return 'resources/reload.html'; };
    </script>
  </head>
  <body>
    <div id="base" class="">

      <!-- Unnamed (组合) -->
      <div id="u4682" class="ax_default" data-left="202" data-top="84" data-width="611" data-height="518">

        <!-- Unnamed (矩形) -->
        <div id="u4683" class="ax_default box_1">
          <div id="u4683_div" class=""></div>
          <div id="u4683_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u4684" class="ax_default box_1">
          <div id="u4684_div" class=""></div>
          <div id="u4684_text" class="text ">
            <p><span>&nbsp;&nbsp; 新增规则</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u4685" class="ax_default label">
          <div id="u4685_div" class=""></div>
          <div id="u4685_text" class="text ">
            <p><span style="color:#D9001B;">*</span><span>规则名称：</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u4686" class="ax_default box_1">
          <div id="u4686_div" class=""></div>
          <div id="u4686_text" class="text ">
            <p><span>确认</span></p>
          </div>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u4687" class="ax_default box_1">
          <div id="u4687_div" class=""></div>
          <div id="u4687_text" class="text ">
            <p><span>取消</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u4688" class="ax_default text_field1">
          <div id="u4688_div" class=""></div>
          <input id="u4688_input" type="text" value="" class="u4688_input"/>
        </div>

        <!-- Unnamed (矩形) -->
        <div id="u4689" class="ax_default box_1">
          <div id="u4689_div" class=""></div>
          <div id="u4689_text" class="text ">
            <p><span>重置</span></p>
          </div>
        </div>

        <!-- Unnamed (文本框) -->
        <div id="u4690" class="ax_default text_field1">
          <div id="u4690_div" class=""></div>
          <input id="u4690_input" type="text" value="&nbsp;" class="u4690_input"/>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u4691" class="ax_default" data-left="348" data-top="434" data-width="78" data-height="77">

          <!-- Unnamed (SVG) -->
          <div id="u4692" class="ax_default _图片_">
            <img id="u4692_img" class="img " src="images/图章规则/u4639.svg"/>
            <div id="u4692_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (SVG) -->
          <div id="u4693" class="ax_default _图片_">
            <img id="u4693_img" class="img " src="images/图章规则/u4640.svg"/>
            <div id="u4693_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u4694" class="ax_default" data-left="475" data-top="434" data-width="78" data-height="77">

          <!-- Unnamed (SVG) -->
          <div id="u4695" class="ax_default _图片_">
            <img id="u4695_img" class="img " src="images/图章规则/u4639.svg"/>
            <div id="u4695_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (SVG) -->
          <div id="u4696" class="ax_default _图片_">
            <img id="u4696_img" class="img " src="images/图章规则/u4640.svg"/>
            <div id="u4696_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (组合) -->
        <div id="u4697" class="ax_default" data-left="587" data-top="434" data-width="78" data-height="77">

          <!-- Unnamed (SVG) -->
          <div id="u4698" class="ax_default _图片_">
            <img id="u4698_img" class="img " src="images/图章规则/u4639.svg"/>
            <div id="u4698_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>

          <!-- Unnamed (SVG) -->
          <div id="u4699" class="ax_default _图片_">
            <img id="u4699_img" class="img " src="images/图章规则/u4640.svg"/>
            <div id="u4699_text" class="text " style="display:none; visibility: hidden">
              <p></p>
            </div>
          </div>
        </div>

        <!-- Unnamed (图片 ) -->
        <div id="u4700" class="ax_default _图片_">
          <img id="u4700_img" class="img " src="images/图章规则/u4647.png"/>
          <div id="u4700_text" class="text " style="display:none; visibility: hidden">
            <p></p>
          </div>
        </div>
      </div>
    </div>
    <script src="resources/scripts/axure/ios.js"></script>
  </body>
</html>
