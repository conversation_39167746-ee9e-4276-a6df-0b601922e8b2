﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:0px;
  width:1970px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u12565_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1769px;
  height:878px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-top:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  color:#1890FF;
}
#u12565 {
  border-width:0px;
  position:absolute;
  left:201px;
  top:62px;
  width:1769px;
  height:878px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  color:#1890FF;
}
#u12565 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12565_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12566_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:881px;
  background:inherit;
  background-color:rgba(5, 55, 125, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
}
#u12566 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:59px;
  width:200px;
  height:881px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
}
#u12566 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12566_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12567_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1969px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:2px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-left:0px;
  border-top:0px;
  border-bottom:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
}
#u12567 {
  border-width:0px;
  position:absolute;
  left:1px;
  top:0px;
  width:1969px;
  height:60px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
}
#u12567 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12567_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12568_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u12568 {
  border-width:0px;
  position:absolute;
  left:65px;
  top:21px;
  width:120px;
  height:25px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:20px;
}
#u12568 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12568_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12569_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:34px;
}
#u12569 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:16px;
  width:33px;
  height:34px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
}
#u12569 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12569_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12570 {
  position:absolute;
  left:0px;
  top:61px;
}
#u12570_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:100px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12570_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12571 {
  position:absolute;
  left:0px;
  top:0px;
}
#u12571_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:100px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12571_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12572 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12573 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12574_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u12574 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u12574 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12574_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12575_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u12575 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:23px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u12575 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12575_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12576_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u12576 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:23px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u12576 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12576_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12577 {
  position:absolute;
  left:0px;
  top:50px;
  visibility:hidden;
}
#u12577_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12577_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12578_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12578 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12578 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12578_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12579_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12579 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12579 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12579_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12580_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12580 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12580 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12580_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12581 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12582_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u12582 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:50px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u12582 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12582_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12583_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u12583 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:73px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u12583 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12583_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12584_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u12584 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:73px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u12584 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12584_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12585 {
  position:absolute;
  left:0px;
  top:100px;
  visibility:hidden;
}
#u12585_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12585_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12586_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12586 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12586 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12586_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12570_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:150px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u12570_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12587 {
  position:absolute;
  left:0px;
  top:0px;
}
#u12587_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:150px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12587_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12588 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12589 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12590_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u12590 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u12590 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12590_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12591_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u12591 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:23px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u12591 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12591_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12592_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u12592 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:23px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u12592 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12592_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12593 {
  position:absolute;
  left:0px;
  top:50px;
  visibility:hidden;
}
#u12593_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12593_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12594_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12594 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12594 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12594_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12595 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12596_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u12596 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:50px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u12596 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12596_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12597_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u12597 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:73px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u12597 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12597_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12598_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u12598 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:73px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u12598 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12598_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12599 {
  position:absolute;
  left:0px;
  top:100px;
  visibility:hidden;
}
#u12599_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:80px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12599_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12600_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12600 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12600 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12600_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12601_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12601 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12601 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12601_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12602 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12603_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u12603 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:100px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u12603 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12603_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12604_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u12604 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:123px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u12604 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12604_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12605_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u12605 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:123px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u12605 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12605_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12606 {
  position:absolute;
  left:0px;
  top:150px;
  visibility:hidden;
}
#u12606_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12606_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12607_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12607 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12607 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12607_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12608_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12608 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12608 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12608_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12609_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12609 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12609 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12609_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12570_state2 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:100px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u12570_state2_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12610 {
  position:absolute;
  left:0px;
  top:0px;
}
#u12610_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:100px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12610_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12611 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12612 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12613_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u12613 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u12613 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12613_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12614_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u12614 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:23px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u12614 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12614_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12615_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u12615 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:23px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u12615 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12615_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12616 {
  position:absolute;
  left:0px;
  top:50px;
  visibility:hidden;
}
#u12616_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:319px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12616_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12617_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12617 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12617 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12617_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12618_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12618 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:79px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12618 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12618_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12619_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12619 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:119px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12619 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12619_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12620_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12620 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12620 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12620_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12621_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12621 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:159px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12621 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12621_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12622_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12622 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:199px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12622 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12622_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12623_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12623 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:239px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12623 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12623_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12624_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12624 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:279px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12624 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12624_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12625 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12626_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u12626 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:50px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u12626 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12626_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12627_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u12627 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:73px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u12627 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12627_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12628_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u12628 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:73px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u12628 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12628_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12629 {
  position:absolute;
  left:0px;
  top:100px;
  visibility:hidden;
}
#u12629_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:159px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12629_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12630_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12630 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12630 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12630_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12631_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12631 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12631 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12631_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12632_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12632 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12632 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12632_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12633_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12633 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:119px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12633 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12633_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12570_state3 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:250px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u12570_state3_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12634 {
  position:absolute;
  left:0px;
  top:0px;
}
#u12634_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:250px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12634_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12635 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12636 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12637_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u12637 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u12637 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12637_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12638_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u12638 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:23px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u12638 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12638_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12639_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u12639 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:23px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u12639 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12639_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12640 {
  position:absolute;
  left:0px;
  top:50px;
  visibility:hidden;
}
#u12640_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:239px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12640_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12641_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12641 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12641 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12641_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12642_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12642 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:79px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12642 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12642_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12643_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12643 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:119px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12643 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12643_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12644_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12644 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:159px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12644 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12644_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12645_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12645 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12645 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12645_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12646_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12646 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:199px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12646 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12646_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12647 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12648_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u12648 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:50px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u12648 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12648_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12649_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u12649 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:73px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u12649 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12649_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12650_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u12650 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:73px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u12650 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12650_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12651 {
  position:absolute;
  left:0px;
  top:100px;
  visibility:hidden;
}
#u12651_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:120px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12651_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12652_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12652 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12652 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12652_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12653_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12653 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12653 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12653_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12654_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12654 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12654 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12654_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12655 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12656_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u12656 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:100px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u12656 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12656_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12657_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u12657 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:123px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u12657 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12657_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12658_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u12658 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:123px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u12658 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12658_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12659 {
  position:absolute;
  left:0px;
  top:150px;
  visibility:hidden;
}
#u12659_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12659_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12660_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12660 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12660 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12660_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12661 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12662_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u12662 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:150px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u12662 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12662_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12663_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u12663 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:173px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u12663 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12663_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12664_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u12664 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:173px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u12664 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12664_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12665 {
  position:absolute;
  left:0px;
  top:200px;
  visibility:hidden;
}
#u12665_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12665_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12666_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12666 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12666 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12666_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12667 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12668_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u12668 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:200px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u12668 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12668_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12669_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u12669 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:223px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u12669 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12669_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12670_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u12670 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:223px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u12670 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12670_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12671 {
  position:absolute;
  left:0px;
  top:250px;
  visibility:hidden;
}
#u12671_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12671_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12672_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12672 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12672 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12672_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12570_state4 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:150px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u12570_state4_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12673 {
  position:absolute;
  left:0px;
  top:0px;
}
#u12673_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:150px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12673_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12674 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12675 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12676_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u12676 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u12676 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12676_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12677_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u12677 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:23px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u12677 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12677_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12678_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u12678 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:23px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u12678 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12678_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12679 {
  position:absolute;
  left:0px;
  top:50px;
  visibility:hidden;
}
#u12679_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:199px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12679_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12680_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12680 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12680 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12680_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12681_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12681 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:79px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12681 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12681_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12682_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12682 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:119px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12682 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12682_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12683_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12683 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12683 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12683_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12684_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12684 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:159px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12684 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12684_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12685 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12686_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u12686 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:50px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u12686 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12686_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12687_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u12687 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:73px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u12687 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12687_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12688_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u12688 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:73px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u12688 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12688_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12689 {
  position:absolute;
  left:0px;
  top:100px;
  visibility:hidden;
}
#u12689_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:160px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12689_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12690_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12690 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12690 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12690_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12691_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12691 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12691 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12691_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12692_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12692 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:80px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12692 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12692_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12693_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12693 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:120px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12693 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12693_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12694 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12695_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:50px;
}
#u12695 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:100px;
  width:200px;
  height:50px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
  line-height:22px;
}
#u12695 .text {
  position:absolute;
  align-self:center;
  padding:15px 2px 15px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12695_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12696_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:11px;
}
#u12696 {
  border-width:0px;
  position:absolute;
  left:19px;
  top:123px;
  width:11px;
  height:11px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
}
#u12696 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12696_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12697_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:12px;
}
#u12697 {
  border-width:0px;
  position:absolute;
  left:177px;
  top:123px;
  width:11px;
  height:12px;
  display:flex;
  -webkit-transform:rotate(180deg);
  -moz-transform:rotate(180deg);
  -ms-transform:rotate(180deg);
  transform:rotate(180deg);
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:16px;
  color:#FFFFFF;
}
#u12697 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12697_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12698 {
  position:absolute;
  left:0px;
  top:150px;
  visibility:hidden;
}
#u12698_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:200px;
  height:80px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12698_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12699_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12699 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12699 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12699_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12700_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:40px;
  background:inherit;
  background-color:rgba(10, 25, 80, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12700 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:40px;
  width:200px;
  height:40px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  font-size:14px;
  color:rgba(255, 255, 255, 0.647058823529412);
  text-align:left;
  line-height:22px;
}
#u12700 .text {
  position:absolute;
  align-self:center;
  padding:9px 2px 9px 50px;
  box-sizing:border-box;
  width:100%;
}
#u12700_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12701_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1770px;
  height:2px;
}
#u12701 {
  border-width:0px;
  position:absolute;
  left:200px;
  top:60px;
  width:1769px;
  height:1px;
  display:flex;
}
#u12701 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12701_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12702_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:546px;
  height:2px;
}
#u12702 {
  border-width:0px;
  position:absolute;
  left:212px;
  top:50px;
  width:545px;
  height:1px;
  display:flex;
}
#u12702 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12702_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12703_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 231, 237, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u12703 {
  border-width:0px;
  position:absolute;
  left:212px;
  top:16px;
  width:98px;
  height:34px;
  display:flex;
  font-size:16px;
  color:#303133;
}
#u12703 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12703_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 231, 237, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u12703.mouseOver {
}
#u12703_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(64, 158, 255, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u12703.selected {
}
#u12703_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12704_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 231, 237, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u12704 {
  border-width:0px;
  position:absolute;
  left:326px;
  top:16px;
  width:83px;
  height:34px;
  display:flex;
  font-size:16px;
  color:#303133;
}
#u12704 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12704_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 231, 237, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u12704.mouseOver {
}
#u12704_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:83px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(64, 158, 255, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u12704.selected {
}
#u12704_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12705_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 231, 237, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u12705 {
  border-width:0px;
  position:absolute;
  left:430px;
  top:16px;
  width:87px;
  height:34px;
  display:flex;
  font-size:16px;
  color:#303133;
}
#u12705 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12705_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 231, 237, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u12705.mouseOver {
}
#u12705_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:87px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(64, 158, 255, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u12705.selected {
}
#u12705_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12706_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 231, 237, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u12706 {
  border-width:0px;
  position:absolute;
  left:533px;
  top:16px;
  width:102px;
  height:34px;
  display:flex;
  font-size:16px;
  color:#303133;
}
#u12706 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12706_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 231, 237, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u12706.mouseOver {
}
#u12706_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(64, 158, 255, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u12706.selected {
}
#u12706_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12707_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 231, 237, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u12707 {
  border-width:0px;
  position:absolute;
  left:654px;
  top:16px;
  width:102px;
  height:34px;
  display:flex;
  font-size:16px;
  color:#303133;
}
#u12707 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12707_div.mouseOver {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(228, 231, 237, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u12707.mouseOver {
}
#u12707_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:34px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(64, 158, 255, 1);
  border-left:0px;
  border-top:0px;
  border-right:0px;
  border-radius:0px;
  border-top-left-radius:0px;
  border-top-right-radius:0px;
  border-bottom-right-radius:0px;
  border-bottom-left-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#303133;
}
#u12707.selected {
}
#u12707_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12708_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:32px;
}
#u12708 {
  border-width:0px;
  position:absolute;
  left:1850px;
  top:14px;
  width:32px;
  height:32px;
  display:flex;
}
#u12708 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12708_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12709_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:32px;
  height:32px;
}
#u12709 {
  border-width:0px;
  position:absolute;
  left:1923px;
  top:14px;
  width:32px;
  height:32px;
  display:flex;
}
#u12709 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12709_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12710 {
  border-width:0px;
  position:absolute;
  left:1471px;
  top:62px;
  width:498px;
  height:240px;
  visibility:hidden;
}
#u12710_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:498px;
  height:240px;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12710_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12711_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:170px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12711 {
  border-width:0px;
  position:absolute;
  left:4px;
  top:0px;
  width:300px;
  height:170px;
  display:flex;
}
#u12711 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12711_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12712_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12712 {
  border-width:0px;
  position:absolute;
  left:148px;
  top:3px;
  width:47px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12712 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12712_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12713_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12713 {
  border-width:0px;
  position:absolute;
  left:41px;
  top:3px;
  width:102px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12713 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12713_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12714_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u12714 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:0px;
  width:26px;
  height:25px;
  display:flex;
}
#u12714 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12714_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12715 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12716_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12716 {
  border-width:0px;
  position:absolute;
  left:145px;
  top:111px;
  width:47px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12716 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12716_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12717_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12717 {
  border-width:0px;
  position:absolute;
  left:38px;
  top:111px;
  width:102px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12717 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12717_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12718_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:18px;
}
#u12718 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:115px;
  width:21px;
  height:18px;
  display:flex;
}
#u12718 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12718_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12719_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:93px;
  height:25px;
  background:inherit;
  background-color:rgba(52, 116, 240, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u12719 {
  border-width:0px;
  position:absolute;
  left:398px;
  top:204px;
  width:93px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u12719 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12719_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12720_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:143px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12720 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:28px;
  width:143px;
  height:25px;
  display:flex;
}
#u12720 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12720_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12721_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12721 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:53px;
  width:139px;
  height:25px;
  display:flex;
}
#u12721 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12721_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12722_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12722 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:78px;
  width:139px;
  height:25px;
  display:flex;
}
#u12722 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12722_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12723_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12723 {
  border-width:0px;
  position:absolute;
  left:43px;
  top:141px;
  width:139px;
  height:25px;
  display:flex;
}
#u12723 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12723_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12724_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12724 {
  border-width:0px;
  position:absolute;
  left:43px;
  top:166px;
  width:139px;
  height:25px;
  display:flex;
}
#u12724 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12724_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12725_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12725 {
  border-width:0px;
  position:absolute;
  left:43px;
  top:191px;
  width:139px;
  height:25px;
  display:flex;
}
#u12725 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12725_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12726_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12726 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:28px;
  width:9px;
  height:25px;
  display:flex;
}
#u12726 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12726_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12710_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:498px;
  height:240px;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u12710_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12727_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:300px;
  height:170px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12727 {
  border-width:0px;
  position:absolute;
  left:4px;
  top:0px;
  width:300px;
  height:170px;
  display:flex;
}
#u12727 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12727_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12728_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12728 {
  border-width:0px;
  position:absolute;
  left:154px;
  top:8px;
  width:47px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12728 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12728_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12729_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12729 {
  border-width:0px;
  position:absolute;
  left:47px;
  top:8px;
  width:102px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12729 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12729_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12730_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:26px;
  height:25px;
}
#u12730 {
  border-width:0px;
  position:absolute;
  left:21px;
  top:5px;
  width:26px;
  height:25px;
  display:flex;
}
#u12730 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12730_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12731_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:47px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12731 {
  border-width:0px;
  position:absolute;
  left:147px;
  top:204px;
  width:47px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12731 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12731_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12732_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:102px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12732 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:204px;
  width:102px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12732 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12732_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12733_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:25px;
}
#u12733 {
  border-width:0px;
  position:absolute;
  left:21px;
  top:204px;
  width:21px;
  height:25px;
  display:flex;
}
#u12733 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12733_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12734_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:147px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12734 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:33px;
  width:147px;
  height:25px;
  display:flex;
}
#u12734 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12734_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12735_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12735 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:138px;
  width:139px;
  height:25px;
  display:flex;
}
#u12735 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12735_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12736_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12736 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:163px;
  width:139px;
  height:25px;
  display:flex;
}
#u12736 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12736_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12737_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12737 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:232px;
  width:139px;
  height:25px;
  display:flex;
}
#u12737 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12737_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12738_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12738 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:312px;
  width:139px;
  height:25px;
  display:flex;
}
#u12738 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12738_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12739_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:139px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12739 {
  border-width:0px;
  position:absolute;
  left:49px;
  top:337px;
  width:139px;
  height:25px;
  display:flex;
}
#u12739 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12739_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12740_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12740 {
  border-width:0px;
  position:absolute;
  left:54px;
  top:33px;
  width:15px;
  height:25px;
  display:flex;
}
#u12740 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12740_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12741_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:276px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u12741 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:60px;
  width:276px;
  height:25px;
  display:flex;
  color:#000000;
}
#u12741 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12741_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12742_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:312px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u12742 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:85px;
  width:312px;
  height:25px;
  display:flex;
  color:#000000;
}
#u12742 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12742_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12743_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u12743 {
  border-width:0px;
  position:absolute;
  left:82px;
  top:113px;
  width:29px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u12743 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12743_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12744_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:98px;
  height:25px;
  background:inherit;
  background-color:rgba(52, 116, 240, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u12744 {
  border-width:0px;
  position:absolute;
  left:374px;
  top:383px;
  width:98px;
  height:25px;
  display:flex;
  color:#FFFFFF;
}
#u12744 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12744_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12745_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u12745 {
  border-width:0px;
  position:absolute;
  left:357px;
  top:60px;
  width:22px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u12745 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12745_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12746_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u12746 {
  border-width:0px;
  position:absolute;
  left:395px;
  top:60px;
  width:33px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u12746 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12746_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12747_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:22px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u12747 {
  border-width:0px;
  position:absolute;
  left:357px;
  top:85px;
  width:22px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u12747 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12747_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12748_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:33px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u12748 {
  border-width:0px;
  position:absolute;
  left:395px;
  top:85px;
  width:33px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u12748 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12748_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12749_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:276px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u12749 {
  border-width:0px;
  position:absolute;
  left:62px;
  top:257px;
  width:276px;
  height:25px;
  display:flex;
  color:#000000;
}
#u12749 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12749_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12750_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u12750 {
  border-width:0px;
  position:absolute;
  left:357px;
  top:257px;
  width:1px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u12750 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12750_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u12751_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:29px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u12751 {
  border-width:0px;
  position:absolute;
  left:79px;
  top:287px;
  width:29px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u12751 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12751_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12752_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:11px;
  color:#0000FF;
}
#u12752 {
  border-width:0px;
  position:absolute;
  left:357px;
  top:257px;
  width:1px;
  height:25px;
  display:flex;
  font-size:11px;
  color:#0000FF;
}
#u12752 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12752_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u12753_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:21px;
  background:inherit;
  background-color:rgba(255, 59, 48, 1);
  border:none;
  border-radius:10px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
  color:#FFFFFF;
}
#u12753 {
  border-width:0px;
  position:absolute;
  left:1873px;
  top:9px;
  width:27px;
  height:21px;
  display:flex;
  font-size:12px;
  color:#FFFFFF;
}
#u12753 .text {
  position:absolute;
  align-self:center;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12753_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12754 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12755_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:400px;
  height:230px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0.00784313725490196);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 218, 226, 1);
  border-radius:4px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12755 {
  border-width:0px;
  position:absolute;
  left:1568px;
  top:62px;
  width:400px;
  height:230px;
  display:flex;
}
#u12755 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12755_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12756_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:329px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  color:#606266;
}
#u12756 {
  border-width:0px;
  position:absolute;
  left:1620px;
  top:112px;
  width:329px;
  height:25px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  color:#606266;
}
#u12756 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12756_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12757_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:40px;
  height:39px;
}
#u12757 {
  border-width:0px;
  position:absolute;
  left:1751px;
  top:73px;
  width:40px;
  height:39px;
  display:flex;
}
#u12757 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12757_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12758_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:30px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u12758 {
  border-width:0px;
  position:absolute;
  left:1634px;
  top:160px;
  width:30px;
  height:25px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u12758 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12758_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12759_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u12759 {
  border-width:0px;
  position:absolute;
  left:1775px;
  top:160px;
  width:25px;
  height:25px;
  display:flex;
  font-size:12px;
}
#u12759 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12759_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12760_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:114px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u12760 {
  border-width:0px;
  position:absolute;
  left:1809px;
  top:160px;
  width:114px;
  height:25px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u12760 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12760_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12761_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u12761 {
  border-width:0px;
  position:absolute;
  left:1602px;
  top:160px;
  width:25px;
  height:25px;
  display:flex;
  font-size:12px;
}
#u12761 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12761_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12762_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u12762 {
  border-width:0px;
  position:absolute;
  left:1602px;
  top:200px;
  width:25px;
  height:25px;
  display:flex;
  font-size:12px;
}
#u12762 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12762_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12763_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u12763 {
  border-width:0px;
  position:absolute;
  left:1775px;
  top:200px;
  width:25px;
  height:25px;
  display:flex;
  font-size:12px;
}
#u12763 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12763_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12764_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u12764 {
  border-width:0px;
  position:absolute;
  left:1602px;
  top:238px;
  width:25px;
  height:25px;
  display:flex;
  font-size:12px;
}
#u12764 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12764_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12765_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u12765 {
  border-width:0px;
  position:absolute;
  left:1775px;
  top:238px;
  width:25px;
  height:25px;
  display:flex;
  font-size:12px;
}
#u12765 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12765_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12766_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u12766 {
  border-width:0px;
  position:absolute;
  left:1873px;
  top:243px;
  width:20px;
  height:20px;
  display:flex;
  font-size:12px;
}
#u12766 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12766_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12767_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u12767 {
  border-width:0px;
  position:absolute;
  left:1809px;
  top:240px;
  width:48px;
  height:25px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u12767 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12767_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12768_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u12768 {
  border-width:0px;
  position:absolute;
  left:1809px;
  top:200px;
  width:66px;
  height:25px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u12768 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12768_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12769_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u12769 {
  border-width:0px;
  position:absolute;
  left:1635px;
  top:200px;
  width:36px;
  height:25px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u12769 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12769_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12770_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u12770 {
  border-width:0px;
  position:absolute;
  left:1634px;
  top:238px;
  width:48px;
  height:25px;
  display:flex;
  font-family:'黑体';
  font-weight:400;
  font-style:normal;
  font-size:12px;
  color:#606266;
}
#u12770 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12770_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12771_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:401px;
  height:2px;
}
#u12771 {
  border-width:0px;
  position:absolute;
  left:1569px;
  top:142px;
  width:400px;
  height:1px;
  display:flex;
  opacity:0.64;
  color:rgba(153, 144, 144, 0.313725490196078);
}
#u12771 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12771_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12772_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1291px;
  height:60px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12772 {
  border-width:0px;
  position:absolute;
  left:233px;
  top:105px;
  width:1291px;
  height:60px;
  display:flex;
}
#u12772 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12772_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12773_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12773 {
  border-width:0px;
  position:absolute;
  left:255px;
  top:114px;
  width:1px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12773 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12773_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u12774_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u12774 {
  border-width:0px;
  position:absolute;
  left:269px;
  top:120px;
  width:96px;
  height:25px;
  display:flex;
  font-size:16px;
}
#u12774 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12774_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12775_input {
  position:absolute;
  left:0px;
  top:0px;
  width:181px;
  height:28px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u12775_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:181px;
  height:28px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u12775_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:181px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u12775 {
  border-width:0px;
  position:absolute;
  left:358px;
  top:120px;
  width:181px;
  height:28px;
  display:flex;
  color:#AAAAAA;
}
#u12775 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12775_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:181px;
  height:28px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u12775.disabled {
}
#u12776_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1090px;
  height:700px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12776 {
  border-width:0px;
  position:absolute;
  left:233px;
  top:229px;
  width:1090px;
  height:700px;
  display:flex;
}
#u12776 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12776_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12777_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:89px;
  height:27px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#FFFFFF;
}
#u12777 {
  border-width:0px;
  position:absolute;
  left:246px;
  top:180px;
  width:89px;
  height:27px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u12777 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12777_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12778 {
  border-width:0px;
  position:absolute;
  left:240px;
  top:240px;
  width:1083px;
  height:321px;
}
#u12778_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1083px;
  height:321px;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12778_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12779 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12780 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1026px;
  height:262px;
}
#u12781_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:52px;
}
#u12781 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u12781 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12781_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12782_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:223px;
  height:52px;
}
#u12782 {
  border-width:0px;
  position:absolute;
  left:74px;
  top:0px;
  width:223px;
  height:52px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  line-height:24px;
}
#u12782 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12782_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12783_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:163px;
  height:52px;
}
#u12783 {
  border-width:0px;
  position:absolute;
  left:297px;
  top:0px;
  width:163px;
  height:52px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  line-height:24px;
}
#u12783 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12783_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12784_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:52px;
}
#u12784 {
  border-width:0px;
  position:absolute;
  left:460px;
  top:0px;
  width:175px;
  height:52px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  line-height:24px;
}
#u12784 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12784_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12785_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:391px;
  height:52px;
}
#u12785 {
  border-width:0px;
  position:absolute;
  left:635px;
  top:0px;
  width:391px;
  height:52px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  line-height:24px;
}
#u12785 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12785_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12786_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:52px;
}
#u12786 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:52px;
  width:74px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u12786 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12786_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12787_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:223px;
  height:52px;
}
#u12787 {
  border-width:0px;
  position:absolute;
  left:74px;
  top:52px;
  width:223px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12787 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12787_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12788_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:163px;
  height:52px;
}
#u12788 {
  border-width:0px;
  position:absolute;
  left:297px;
  top:52px;
  width:163px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12788 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12788_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12789_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:52px;
}
#u12789 {
  border-width:0px;
  position:absolute;
  left:460px;
  top:52px;
  width:175px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12789 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12789_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12790_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:391px;
  height:52px;
}
#u12790 {
  border-width:0px;
  position:absolute;
  left:635px;
  top:52px;
  width:391px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u12790 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12790_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12791_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:52px;
}
#u12791 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:104px;
  width:74px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u12791 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12791_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12792_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:223px;
  height:52px;
}
#u12792 {
  border-width:0px;
  position:absolute;
  left:74px;
  top:104px;
  width:223px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12792 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12792_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12793_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:163px;
  height:52px;
}
#u12793 {
  border-width:0px;
  position:absolute;
  left:297px;
  top:104px;
  width:163px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12793 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12793_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12794_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:52px;
}
#u12794 {
  border-width:0px;
  position:absolute;
  left:460px;
  top:104px;
  width:175px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12794 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12794_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12795_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:391px;
  height:52px;
}
#u12795 {
  border-width:0px;
  position:absolute;
  left:635px;
  top:104px;
  width:391px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u12795 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12795_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12796_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:52px;
}
#u12796 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:156px;
  width:74px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u12796 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12796_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12797_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:223px;
  height:52px;
}
#u12797 {
  border-width:0px;
  position:absolute;
  left:74px;
  top:156px;
  width:223px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12797 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12797_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12798_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:163px;
  height:52px;
}
#u12798 {
  border-width:0px;
  position:absolute;
  left:297px;
  top:156px;
  width:163px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12798 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12798_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12799_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:52px;
}
#u12799 {
  border-width:0px;
  position:absolute;
  left:460px;
  top:156px;
  width:175px;
  height:52px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12799 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12799_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12800_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:391px;
  height:52px;
}
#u12800 {
  border-width:0px;
  position:absolute;
  left:635px;
  top:156px;
  width:391px;
  height:52px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u12800 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12800_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12801_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:54px;
}
#u12801 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:208px;
  width:74px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u12801 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12801_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12802_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:223px;
  height:54px;
}
#u12802 {
  border-width:0px;
  position:absolute;
  left:74px;
  top:208px;
  width:223px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12802 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12802_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12803_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:163px;
  height:54px;
}
#u12803 {
  border-width:0px;
  position:absolute;
  left:297px;
  top:208px;
  width:163px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12803 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12803_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12804_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:175px;
  height:54px;
}
#u12804 {
  border-width:0px;
  position:absolute;
  left:460px;
  top:208px;
  width:175px;
  height:54px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12804 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12804_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12805_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:391px;
  height:54px;
}
#u12805 {
  border-width:0px;
  position:absolute;
  left:635px;
  top:208px;
  width:391px;
  height:54px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u12805 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12805_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12806 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12807 label {
  left:0px;
  width:100%;
}
#u12807_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u12807 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:16px;
  width:30px;
  height:16px;
  display:flex;
}
#u12807 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u12807_img.selected {
}
#u12807.selected {
}
#u12807_img.disabled {
}
#u12807.disabled {
}
#u12807_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12807_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u12808 label {
  left:0px;
  width:100%;
}
#u12808_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u12808 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:67px;
  width:30px;
  height:16px;
  display:flex;
}
#u12808 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u12808_img.selected {
}
#u12808.selected {
}
#u12808_img.disabled {
}
#u12808.disabled {
}
#u12808_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12808_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u12809 label {
  left:0px;
  width:100%;
}
#u12809_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u12809 {
  border-width:0px;
  position:absolute;
  left:32px;
  top:119px;
  width:30px;
  height:16px;
  display:flex;
}
#u12809 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u12809_img.selected {
}
#u12809.selected {
}
#u12809_img.disabled {
}
#u12809.disabled {
}
#u12809_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12809_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u12810 label {
  left:0px;
  width:100%;
}
#u12810_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u12810 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:173px;
  width:30px;
  height:16px;
  display:flex;
}
#u12810 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u12810_img.selected {
}
#u12810.selected {
}
#u12810_img.disabled {
}
#u12810.disabled {
}
#u12810_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12810_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u12811 label {
  left:0px;
  width:100%;
}
#u12811_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u12811 {
  border-width:0px;
  position:absolute;
  left:29px;
  top:227px;
  width:30px;
  height:16px;
  display:flex;
}
#u12811 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u12811_img.selected {
}
#u12811.selected {
}
#u12811_img.disabled {
}
#u12811.disabled {
}
#u12811_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12811_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u12812_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u12812 {
  border-width:0px;
  position:absolute;
  left:670px;
  top:72px;
  width:56px;
  height:25px;
  display:flex;
  color:#1890FF;
}
#u12812 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12812_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12813_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u12813 {
  border-width:0px;
  position:absolute;
  left:766px;
  top:72px;
  width:28px;
  height:25px;
  display:flex;
  color:#1890FF;
}
#u12813 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12813_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12814_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u12814 {
  border-width:0px;
  position:absolute;
  left:829px;
  top:72px;
  width:28px;
  height:25px;
  display:flex;
  color:#1890FF;
}
#u12814 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12814_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12815_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:8px;
}
#u12815 {
  border-width:0px;
  position:absolute;
  left:316px;
  top:72px;
  width:8px;
  height:8px;
  display:flex;
}
#u12815 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12815_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12816_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:8px;
}
#u12816 {
  border-width:0px;
  position:absolute;
  left:316px;
  top:126px;
  width:8px;
  height:8px;
  display:flex;
}
#u12816 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12816_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12817_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:8px;
}
#u12817 {
  border-width:0px;
  position:absolute;
  left:317px;
  top:177px;
  width:8px;
  height:8px;
  display:flex;
}
#u12817 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12817_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12818_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:8px;
  height:8px;
}
#u12818 {
  border-width:0px;
  position:absolute;
  left:317px;
  top:232px;
  width:8px;
  height:8px;
  display:flex;
}
#u12818 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12818_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12819_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u12819 {
  border-width:0px;
  position:absolute;
  left:1382px;
  top:63px;
  width:56px;
  height:25px;
  display:flex;
  color:#1890FF;
}
#u12819 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12819_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12820 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12821_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1248px;
  height:53px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12821 {
  border-width:0px;
  position:absolute;
  left:233px;
  top:848px;
  width:1248px;
  height:53px;
  display:flex;
}
#u12821 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12821_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12822_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u12822 {
  border-width:0px;
  position:absolute;
  left:255px;
  top:862px;
  width:80px;
  height:25px;
  display:flex;
  font-size:16px;
}
#u12822 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12822_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12823_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:135px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u12823 {
  border-width:0px;
  position:absolute;
  left:1256px;
  top:861px;
  width:135px;
  height:25px;
  display:flex;
  font-size:16px;
}
#u12823 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12823_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12824_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:35px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:left;
}
#u12824 {
  border-width:0px;
  position:absolute;
  left:339px;
  top:857px;
  width:96px;
  height:35px;
  display:flex;
  text-align:left;
}
#u12824 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12824_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12825_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:15px;
  height:15px;
}
#u12825 {
  border-width:0px;
  position:absolute;
  left:415px;
  top:867px;
  width:15px;
  height:15px;
  display:flex;
}
#u12825 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12825_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12826_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u12826 {
  border-width:0px;
  position:absolute;
  left:1385px;
  top:863px;
  width:25px;
  height:25px;
  display:flex;
}
#u12826 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12826_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12827_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:28px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(24, 144, 255, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#1890FF;
}
#u12827 {
  border-width:0px;
  position:absolute;
  left:1416px;
  top:861px;
  width:28px;
  height:27px;
  display:flex;
  color:#1890FF;
}
#u12827 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12827_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12828_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:25px;
  height:25px;
}
#u12828 {
  border-width:0px;
  position:absolute;
  left:1456px;
  top:864px;
  width:25px;
  height:25px;
  display:flex;
}
#u12828 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12828_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12829_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:54px;
  height:27px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#AAAAAA;
}
#u12829 {
  border-width:0px;
  position:absolute;
  left:415px;
  top:180px;
  width:54px;
  height:27px;
  display:flex;
  font-size:16px;
  color:#AAAAAA;
}
#u12829 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12829_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12830_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:48px;
  height:27px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#FFFFFF;
}
#u12830 {
  border-width:0px;
  position:absolute;
  left:352px;
  top:180px;
  width:48px;
  height:27px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u12830 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12830_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12832_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
}
#u12832 {
  border-width:0px;
  position:absolute;
  left:609px;
  top:120px;
  width:80px;
  height:25px;
  display:flex;
  font-size:16px;
}
#u12832 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12832_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12833_input {
  position:absolute;
  left:0px;
  top:0px;
  width:196px;
  height:28px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u12833_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:196px;
  height:28px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u12833_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:196px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12833 {
  border-width:0px;
  position:absolute;
  left:689px;
  top:120px;
  width:196px;
  height:28px;
  display:flex;
}
#u12833 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12833_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:196px;
  height:28px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12833.disabled {
}
.u12833_input_option {
}
#u12834_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#AAAAAA;
}
#u12834 {
  border-width:0px;
  position:absolute;
  left:1059px;
  top:120px;
  width:53px;
  height:31px;
  display:flex;
  font-size:14px;
  color:#AAAAAA;
}
#u12834 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12834_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12835_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:31px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#FFFFFF;
}
#u12835 {
  border-width:0px;
  position:absolute;
  left:995px;
  top:120px;
  width:55px;
  height:31px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u12835 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12835_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12836 {
  border-width:0px;
  position:absolute;
  left:230px;
  top:145px;
  width:1005px;
  height:678px;
}
#u12836_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1005px;
  height:678px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12836_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12837_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1021px;
  height:682px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  text-align:left;
}
#u12837 {
  border-width:0px;
  position:absolute;
  left:-16px;
  top:0px;
  width:1021px;
  height:682px;
  display:flex;
  text-align:left;
}
#u12837 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12837_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12838_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1005px;
  height:40px;
  background:inherit;
  background-color:rgba(0, 153, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
  text-align:left;
}
#u12838 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1005px;
  height:40px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
  text-align:left;
}
#u12838 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12838_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12839_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u12839 {
  border-width:0px;
  position:absolute;
  left:155px;
  top:105px;
  width:42px;
  height:25px;
  display:flex;
  color:#000000;
}
#u12839 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12839_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12840_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12840 {
  border-width:0px;
  position:absolute;
  left:110px;
  top:50px;
  width:90px;
  height:25px;
  display:flex;
}
#u12840 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12840_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12841 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12842_input {
  position:absolute;
  left:0px;
  top:0px;
  width:514px;
  height:54px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u12842_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:514px;
  height:54px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u12842_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:514px;
  height:54px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u12842 {
  border-width:0px;
  position:absolute;
  left:212px;
  top:90px;
  width:514px;
  height:54px;
  display:flex;
  color:#AAAAAA;
}
#u12842 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12842_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:514px;
  height:54px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u12842.disabled {
}
#u12843 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12844_input {
  position:absolute;
  left:0px;
  top:0px;
  width:514px;
  height:29px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u12844_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:514px;
  height:29px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u12844_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:514px;
  height:29px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u12844 {
  border-width:0px;
  position:absolute;
  left:212px;
  top:48px;
  width:514px;
  height:29px;
  display:flex;
  color:#AAAAAA;
}
#u12844 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12844_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:514px;
  height:29px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u12844.disabled {
}
#u12845_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:22px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u12845 {
  border-width:0px;
  position:absolute;
  left:133px;
  top:160px;
  width:70px;
  height:22px;
  display:flex;
  color:#000000;
}
#u12845 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12845_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12846_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:29px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u12846 {
  border-width:0px;
  position:absolute;
  left:229px;
  top:412px;
  width:63px;
  height:29px;
  display:flex;
  color:#FFFFFF;
}
#u12846 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12846_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12847_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:19px;
}
#u12847 {
  border-width:0px;
  position:absolute;
  left:208px;
  top:163px;
  width:17px;
  height:19px;
  display:flex;
}
#u12847 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12847_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12848_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12848 {
  border-width:0px;
  position:absolute;
  left:230px;
  top:160px;
  width:70px;
  height:25px;
  display:flex;
}
#u12848 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12848_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12849 {
  border-width:0px;
  position:absolute;
  left:118px;
  top:210px;
  width:754px;
  height:90px;
}
#u12850_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:153px;
  height:30px;
}
#u12850 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:153px;
  height:30px;
  display:flex;
}
#u12850 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12850_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12851_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:185px;
  height:30px;
}
#u12851 {
  border-width:0px;
  position:absolute;
  left:153px;
  top:0px;
  width:185px;
  height:30px;
  display:flex;
}
#u12851 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12851_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12852_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:30px;
}
#u12852 {
  border-width:0px;
  position:absolute;
  left:338px;
  top:0px;
  width:169px;
  height:30px;
  display:flex;
}
#u12852 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12852_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12853_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:247px;
  height:30px;
}
#u12853 {
  border-width:0px;
  position:absolute;
  left:507px;
  top:0px;
  width:247px;
  height:30px;
  display:flex;
}
#u12853 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12853_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12854_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:153px;
  height:30px;
}
#u12854 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:153px;
  height:30px;
  display:flex;
}
#u12854 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12854_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12855_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:185px;
  height:30px;
}
#u12855 {
  border-width:0px;
  position:absolute;
  left:153px;
  top:30px;
  width:185px;
  height:30px;
  display:flex;
}
#u12855 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12855_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12856_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:30px;
}
#u12856 {
  border-width:0px;
  position:absolute;
  left:338px;
  top:30px;
  width:169px;
  height:30px;
  display:flex;
}
#u12856 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12856_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12857_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:247px;
  height:30px;
}
#u12857 {
  border-width:0px;
  position:absolute;
  left:507px;
  top:30px;
  width:247px;
  height:30px;
  display:flex;
}
#u12857 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12857_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12858_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:153px;
  height:30px;
}
#u12858 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:153px;
  height:30px;
  display:flex;
}
#u12858 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12858_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12859_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:185px;
  height:30px;
}
#u12859 {
  border-width:0px;
  position:absolute;
  left:153px;
  top:60px;
  width:185px;
  height:30px;
  display:flex;
}
#u12859 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12859_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12860_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:169px;
  height:30px;
}
#u12860 {
  border-width:0px;
  position:absolute;
  left:338px;
  top:60px;
  width:169px;
  height:30px;
  display:flex;
}
#u12860 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12860_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12861_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:247px;
  height:30px;
}
#u12861 {
  border-width:0px;
  position:absolute;
  left:507px;
  top:60px;
  width:247px;
  height:30px;
  display:flex;
}
#u12861 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12861_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12862_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:29px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u12862 {
  border-width:0px;
  position:absolute;
  left:356px;
  top:412px;
  width:63px;
  height:29px;
  display:flex;
  color:#555555;
}
#u12862 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12862_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12863_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:29px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u12863 {
  border-width:0px;
  position:absolute;
  left:485px;
  top:412px;
  width:63px;
  height:29px;
  display:flex;
  color:#555555;
}
#u12863 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12863_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12864 label {
  left:0px;
  width:100%;
}
#u12864_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u12864 {
  border-width:0px;
  position:absolute;
  left:128px;
  top:214px;
  width:100px;
  height:25px;
  display:flex;
}
#u12864 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u12864_img.selected {
}
#u12864.selected {
}
#u12864_img.disabled {
}
#u12864.disabled {
}
#u12864_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u12864_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u12865_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:754px;
  height:61px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12865 {
  border-width:0px;
  position:absolute;
  left:118px;
  top:239px;
  width:754px;
  height:61px;
  display:flex;
}
#u12865 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12865_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12866_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:23px;
  height:23px;
}
#u12866 {
  border-width:0px;
  position:absolute;
  left:469px;
  top:256px;
  width:23px;
  height:23px;
  display:flex;
}
#u12866 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12866_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12867_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:56px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u12867 {
  border-width:0px;
  position:absolute;
  left:502px;
  top:256px;
  width:56px;
  height:25px;
  display:flex;
  color:#555555;
}
#u12867 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12867_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12868 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12869_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:23px;
}
#u12869 {
  border-width:0px;
  position:absolute;
  left:887px;
  top:270px;
  width:65px;
  height:23px;
  display:flex;
  color:#7F7F7F;
}
#u12869 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12869_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12870_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:14px;
  height:14px;
}
#u12870 {
  border-width:0px;
  position:absolute;
  left:893px;
  top:274px;
  width:14px;
  height:14px;
  display:flex;
}
#u12870 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12870_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12871 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12872_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:23px;
}
#u12872 {
  border-width:0px;
  position:absolute;
  left:887px;
  top:237px;
  width:65px;
  height:23px;
  display:flex;
}
#u12872 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12872_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12873_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:17px;
  height:18px;
}
#u12873 {
  border-width:0px;
  position:absolute;
  left:890px;
  top:239px;
  width:17px;
  height:18px;
  display:flex;
}
#u12873 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12873_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12874 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:185px;
  width:978px;
  height:497px;
}
#u12874_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:978px;
  height:497px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12874_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12875_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:870px;
  height:494px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12875 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:870px;
  height:494px;
  display:flex;
}
#u12875 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12875_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12876_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:977px;
  height:40px;
  background:inherit;
  background-color:rgba(0, 153, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:15px;
  color:#FFFFFF;
  text-align:left;
}
#u12876 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:977px;
  height:40px;
  display:flex;
  font-size:15px;
  color:#FFFFFF;
  text-align:left;
}
#u12876 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12876_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12877_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:28px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#FFFFFF;
}
#u12877 {
  border-width:0px;
  position:absolute;
  left:592px;
  top:323px;
  width:65px;
  height:28px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u12877 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12877_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12878_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#7F7F7F;
}
#u12878 {
  border-width:0px;
  position:absolute;
  left:688px;
  top:323px;
  width:65px;
  height:28px;
  display:flex;
  font-size:14px;
  color:#7F7F7F;
}
#u12878 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12878_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12879_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:977px;
  height:604px;
  background:inherit;
  background-color:rgba(242, 242, 242, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12879 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:39px;
  width:977px;
  height:604px;
  display:flex;
}
#u12879 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12879_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12880_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:225px;
  height:603px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12880 {
  border-width:0px;
  position:absolute;
  left:9px;
  top:40px;
  width:225px;
  height:603px;
  display:flex;
}
#u12880 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12880_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12881 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12882 {
  border-width:0px;
  position:absolute;
  left:4px;
  top:90px;
  width:48px;
  height:80px;
}
#u12882_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12883 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:27px;
  height:20px;
}
#u12884_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u12884 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:6px;
  width:9px;
  height:9px;
  display:flex;
  line-height:normal;
}
#u12884 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12884_img.selected {
}
#u12884.selected {
}
#u12884_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12885_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:5px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12885 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:5px;
  height:20px;
  display:flex;
}
#u12885 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12885_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12883_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12886 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:20px;
  width:28px;
  height:20px;
}
#u12887_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:9px;
  height:9px;
}
#u12887 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:6px;
  width:9px;
  height:9px;
  display:flex;
}
#u12887 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12887_img.selected {
}
#u12887.selected {
}
#u12887_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12888_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12888 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u12888 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12888_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u12886_children {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  visibility:hidden;
}
#u12889 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:20px;
  width:28px;
  height:20px;
}
#u12890_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12890 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u12890 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12890_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u12891 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:40px;
  width:28px;
  height:20px;
}
#u12892_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12892 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u12892 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12892_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u12893 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:40px;
  width:28px;
  height:20px;
}
#u12894_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12894 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u12894 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12894_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u12895 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:60px;
  width:28px;
  height:20px;
}
#u12896_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:6px;
  height:20px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12896 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:0px;
  width:6px;
  height:20px;
  display:flex;
}
#u12896 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12896_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
  visibility:hidden;
}
#u12897_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12897 {
  border-width:0px;
  position:absolute;
  left:11px;
  top:40px;
  width:200px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12897 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12897_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12898_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
}
#u12898 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:86px;
  width:52px;
  height:25px;
  display:flex;
  font-size:13px;
}
#u12898 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12898_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12899_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
}
#u12899 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:108px;
  width:52px;
  height:25px;
  display:flex;
  font-size:13px;
}
#u12899 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12899_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12900_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
}
#u12900 {
  border-width:0px;
  position:absolute;
  left:57px;
  top:127px;
  width:65px;
  height:25px;
  display:flex;
  font-size:13px;
}
#u12900 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12900_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12901_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:65px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
}
#u12901 {
  border-width:0px;
  position:absolute;
  left:57px;
  top:152px;
  width:65px;
  height:25px;
  display:flex;
  font-size:13px;
}
#u12901 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12901_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12902_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
}
#u12902 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:177px;
  width:52px;
  height:25px;
  display:flex;
  font-size:13px;
}
#u12902 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12902_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12903_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:13px;
}
#u12903 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:202px;
  width:52px;
  height:25px;
  display:flex;
  font-size:13px;
}
#u12903 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12903_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12904_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:733px;
  height:53px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(242, 242, 242, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12904 {
  border-width:0px;
  position:absolute;
  left:244px;
  top:40px;
  width:733px;
  height:53px;
  display:flex;
}
#u12904 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12904_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12905 {
  border-width:0px;
  position:absolute;
  left:248px;
  top:106px;
  width:719px;
  height:253px;
}
#u12905_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:719px;
  height:253px;
  overflow:auto;
  -webkit-overflow-scrolling:touch;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12905_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12906 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:9px;
  width:649px;
  height:196px;
}
#u12907_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:52px;
}
#u12907 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:52px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  line-height:24px;
}
#u12907 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12907_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12908_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:52px;
}
#u12908 {
  border-width:0px;
  position:absolute;
  left:53px;
  top:0px;
  width:149px;
  height:52px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  line-height:24px;
}
#u12908 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12908_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12909_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:52px;
}
#u12909 {
  border-width:0px;
  position:absolute;
  left:202px;
  top:0px;
  width:149px;
  height:52px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  line-height:24px;
}
#u12909 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12909_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12910_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:52px;
}
#u12910 {
  border-width:0px;
  position:absolute;
  left:351px;
  top:0px;
  width:149px;
  height:52px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  line-height:24px;
}
#u12910 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12910_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12911_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:52px;
}
#u12911 {
  border-width:0px;
  position:absolute;
  left:500px;
  top:0px;
  width:149px;
  height:52px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:14px;
  line-height:24px;
}
#u12911 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12911_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12912_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:48px;
}
#u12912 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:52px;
  width:53px;
  height:48px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12912 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12912_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12913_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:48px;
}
#u12913 {
  border-width:0px;
  position:absolute;
  left:53px;
  top:52px;
  width:149px;
  height:48px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12913 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12913_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12914_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:48px;
}
#u12914 {
  border-width:0px;
  position:absolute;
  left:202px;
  top:52px;
  width:149px;
  height:48px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12914 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12914_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12915_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:48px;
}
#u12915 {
  border-width:0px;
  position:absolute;
  left:351px;
  top:52px;
  width:149px;
  height:48px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u12915 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12915_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12916_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:48px;
}
#u12916 {
  border-width:0px;
  position:absolute;
  left:500px;
  top:52px;
  width:149px;
  height:48px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12916 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12916_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12917_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:48px;
}
#u12917 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:100px;
  width:53px;
  height:48px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12917 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12917_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12918_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:48px;
}
#u12918 {
  border-width:0px;
  position:absolute;
  left:53px;
  top:100px;
  width:149px;
  height:48px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12918 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12918_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12919_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:48px;
}
#u12919 {
  border-width:0px;
  position:absolute;
  left:202px;
  top:100px;
  width:149px;
  height:48px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12919 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12919_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12920_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:48px;
}
#u12920 {
  border-width:0px;
  position:absolute;
  left:351px;
  top:100px;
  width:149px;
  height:48px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u12920 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12920_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12921_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:48px;
}
#u12921 {
  border-width:0px;
  position:absolute;
  left:500px;
  top:100px;
  width:149px;
  height:48px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12921 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12921_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12922_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:48px;
}
#u12922 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:148px;
  width:53px;
  height:48px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12922 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12922_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12923_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:48px;
}
#u12923 {
  border-width:0px;
  position:absolute;
  left:53px;
  top:148px;
  width:149px;
  height:48px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12923 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12923_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12924_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:48px;
}
#u12924 {
  border-width:0px;
  position:absolute;
  left:202px;
  top:148px;
  width:149px;
  height:48px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12924 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12924_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12925_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:48px;
}
#u12925 {
  border-width:0px;
  position:absolute;
  left:351px;
  top:148px;
  width:149px;
  height:48px;
  display:flex;
  font-size:14px;
  line-height:24px;
}
#u12925 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12925_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12926_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:149px;
  height:48px;
}
#u12926 {
  border-width:0px;
  position:absolute;
  left:500px;
  top:148px;
  width:149px;
  height:48px;
  display:flex;
  font-size:14px;
  text-align:left;
  line-height:24px;
}
#u12926 .text {
  position:absolute;
  align-self:center;
  padding:2px 4px 2px 3px;
  box-sizing:border-box;
  width:100%;
}
#u12926_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12927 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u12928 label {
  left:0px;
  width:100%;
}
#u12928_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u12928 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:28px;
  width:30px;
  height:16px;
  display:flex;
}
#u12928 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u12928_img.selected {
}
#u12928.selected {
}
#u12928_img.disabled {
}
#u12928.disabled {
}
#u12928_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12928_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u12929 label {
  left:0px;
  width:100%;
}
#u12929_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u12929 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:80px;
  width:30px;
  height:16px;
  display:flex;
}
#u12929 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u12929_img.selected {
}
#u12929.selected {
}
#u12929_img.disabled {
}
#u12929.disabled {
}
#u12929_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12929_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u12930 label {
  left:0px;
  width:100%;
}
#u12930_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u12930 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:133px;
  width:30px;
  height:16px;
  display:flex;
}
#u12930 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u12930_img.selected {
}
#u12930.selected {
}
#u12930_img.disabled {
}
#u12930.disabled {
}
#u12930_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12930_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u12931 label {
  left:0px;
  width:100%;
}
#u12931_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:12px;
  height:12px;
}
#u12931 {
  border-width:0px;
  position:absolute;
  left:30px;
  top:174px;
  width:30px;
  height:16px;
  display:flex;
}
#u12931 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u12931_img.selected {
}
#u12931.selected {
}
#u12931_img.disabled {
}
#u12931.disabled {
}
#u12931_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:14px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12931_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u12932_input {
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u12932_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u12932_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12932 {
  border-width:0px;
  position:absolute;
  left:310px;
  top:53px;
  width:161px;
  height:25px;
  display:flex;
}
#u12932 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12932_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12932.disabled {
}
#u12933_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12933 {
  border-width:0px;
  position:absolute;
  left:266px;
  top:52px;
  width:42px;
  height:25px;
  display:flex;
}
#u12933 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12933_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12934_input {
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u12934_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#000000;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u12934_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12934 {
  border-width:0px;
  position:absolute;
  left:599px;
  top:51px;
  width:161px;
  height:25px;
  display:flex;
}
#u12934 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12934_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:161px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12934.disabled {
}
#u12935_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:42px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12935 {
  border-width:0px;
  position:absolute;
  left:548px;
  top:51px;
  width:42px;
  height:25px;
  display:flex;
}
#u12935 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12935_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12936_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:53px;
  height:31px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u12936 {
  border-width:0px;
  position:absolute;
  left:871px;
  top:51px;
  width:53px;
  height:31px;
  display:flex;
  color:#AAAAAA;
}
#u12936 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12936_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12937_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:55px;
  height:31px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u12937 {
  border-width:0px;
  position:absolute;
  left:807px;
  top:51px;
  width:55px;
  height:31px;
  display:flex;
  color:#FFFFFF;
}
#u12937 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12937_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12938_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
}
#u12938 {
  border-width:0px;
  position:absolute;
  left:945px;
  top:15px;
  width:16px;
  height:16px;
  display:flex;
}
#u12938 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12938_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12939_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:29px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u12939 {
  border-width:0px;
  position:absolute;
  left:805px;
  top:384px;
  width:63px;
  height:29px;
  display:flex;
  color:#FFFFFF;
}
#u12939 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12939_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12940_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:63px;
  height:29px;
  background:inherit;
  background-color:rgba(215, 215, 215, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u12940 {
  border-width:0px;
  position:absolute;
  left:915px;
  top:384px;
  width:63px;
  height:29px;
  display:flex;
  color:#555555;
}
#u12940 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12940_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12941_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
}
#u12941 {
  border-width:0px;
  position:absolute;
  left:957px;
  top:18px;
  width:16px;
  height:16px;
  display:flex;
}
#u12941 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12941_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12942 {
  border-width:0px;
  position:absolute;
  left:393px;
  top:165px;
  width:786px;
  height:830px;
}
#u12942_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:786px;
  height:830px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12942_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12943_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:784px;
  height:795px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12943 {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:3px;
  width:784px;
  height:795px;
  display:flex;
}
#u12943 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12943_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12944_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:783px;
  height:35px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
}
#u12944 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:4px;
  width:783px;
  height:35px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
}
#u12944 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12944_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12945_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:122px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12945 {
  border-width:0px;
  position:absolute;
  left:54px;
  top:87px;
  width:122px;
  height:25px;
  display:flex;
}
#u12945 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12945_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12946_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
}
#u12946 {
  border-width:0px;
  position:absolute;
  left:747px;
  top:15px;
  width:16px;
  height:16px;
  display:flex;
}
#u12946 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12946_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12947_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#7F7F7F;
}
#u12947 {
  border-width:0px;
  position:absolute;
  left:652px;
  top:629px;
  width:60px;
  height:28px;
  display:flex;
  color:#7F7F7F;
}
#u12947 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12947_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12948_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u12948 {
  border-width:0px;
  position:absolute;
  left:17px;
  top:48px;
  width:80px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u12948 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12948_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12949_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:777px;
  height:2px;
}
#u12949 {
  border-width:0px;
  position:absolute;
  left:4px;
  top:78px;
  width:776px;
  height:1px;
  display:flex;
}
#u12949 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12949_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12950_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u12950 {
  border-width:0px;
  position:absolute;
  left:14px;
  top:282px;
  width:64px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u12950 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12950_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12951_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:777px;
  height:2px;
}
#u12951 {
  border-width:0px;
  position:absolute;
  left:1px;
  top:312px;
  width:776px;
  height:1px;
  display:flex;
}
#u12951 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12951_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12952 {
  border-width:0px;
  position:absolute;
  left:54px;
  top:335px;
  width:647px;
  height:90px;
}
#u12953_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
}
#u12953 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12953 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12953_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12954_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:196px;
  height:30px;
}
#u12954 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:0px;
  width:196px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12954 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12954_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12955_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:307px;
  height:30px;
}
#u12955 {
  border-width:0px;
  position:absolute;
  left:340px;
  top:0px;
  width:307px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12955 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12955_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12956_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
}
#u12956 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:144px;
  height:30px;
  display:flex;
}
#u12956 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12956_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12957_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:196px;
  height:30px;
}
#u12957 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:30px;
  width:196px;
  height:30px;
  display:flex;
}
#u12957 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12957_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12958_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:307px;
  height:30px;
}
#u12958 {
  border-width:0px;
  position:absolute;
  left:340px;
  top:30px;
  width:307px;
  height:30px;
  display:flex;
}
#u12958 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12958_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12959_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
}
#u12959 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:144px;
  height:30px;
  display:flex;
}
#u12959 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12959_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12960_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:196px;
  height:30px;
}
#u12960 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:60px;
  width:196px;
  height:30px;
  display:flex;
}
#u12960 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12960_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12961_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:307px;
  height:30px;
}
#u12961 {
  border-width:0px;
  position:absolute;
  left:340px;
  top:60px;
  width:307px;
  height:30px;
  display:flex;
}
#u12961 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12961_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12962_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:64px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u12962 {
  border-width:0px;
  position:absolute;
  left:15px;
  top:448px;
  width:64px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u12962 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12962_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12963_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:777px;
  height:2px;
}
#u12963 {
  border-width:0px;
  position:absolute;
  left:2px;
  top:478px;
  width:776px;
  height:1px;
  display:flex;
}
#u12963 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12963_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12964 {
  border-width:0px;
  position:absolute;
  left:54px;
  top:492px;
  width:647px;
  height:88px;
}
#u12965_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
}
#u12965 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12965 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12965_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12966_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:196px;
  height:30px;
}
#u12966 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:0px;
  width:196px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12966 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12966_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12967_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:307px;
  height:30px;
}
#u12967 {
  border-width:0px;
  position:absolute;
  left:340px;
  top:0px;
  width:307px;
  height:30px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
}
#u12967 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12967_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12968_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:30px;
}
#u12968 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:144px;
  height:30px;
  display:flex;
}
#u12968 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12968_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12969_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:196px;
  height:30px;
}
#u12969 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:30px;
  width:196px;
  height:30px;
  display:flex;
}
#u12969 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12969_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12970_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:307px;
  height:30px;
}
#u12970 {
  border-width:0px;
  position:absolute;
  left:340px;
  top:30px;
  width:307px;
  height:30px;
  display:flex;
}
#u12970 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12970_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12971_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:144px;
  height:28px;
}
#u12971 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:144px;
  height:28px;
  display:flex;
}
#u12971 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12971_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12972_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:196px;
  height:28px;
}
#u12972 {
  border-width:0px;
  position:absolute;
  left:144px;
  top:60px;
  width:196px;
  height:28px;
  display:flex;
}
#u12972 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12972_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12973_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:307px;
  height:28px;
}
#u12973 {
  border-width:0px;
  position:absolute;
  left:340px;
  top:60px;
  width:307px;
  height:28px;
  display:flex;
}
#u12973 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12973_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12974_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12974 {
  border-width:0px;
  position:absolute;
  left:412px;
  top:87px;
  width:200px;
  height:25px;
  display:flex;
}
#u12974 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12974_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12975 {
  border-width:0px;
  position:absolute;
  left:54px;
  top:170px;
  width:647px;
  height:90px;
}
#u12976_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:30px;
}
#u12976 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:30px;
  display:flex;
}
#u12976 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12976_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12977_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:166px;
  height:30px;
}
#u12977 {
  border-width:0px;
  position:absolute;
  left:137px;
  top:0px;
  width:166px;
  height:30px;
  display:flex;
}
#u12977 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12977_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12978_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:152px;
  height:30px;
}
#u12978 {
  border-width:0px;
  position:absolute;
  left:303px;
  top:0px;
  width:152px;
  height:30px;
  display:flex;
}
#u12978 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12978_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12979_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:192px;
  height:30px;
}
#u12979 {
  border-width:0px;
  position:absolute;
  left:455px;
  top:0px;
  width:192px;
  height:30px;
  display:flex;
}
#u12979 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12979_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12980_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:30px;
}
#u12980 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:30px;
  width:137px;
  height:30px;
  display:flex;
}
#u12980 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12980_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12981_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:166px;
  height:30px;
}
#u12981 {
  border-width:0px;
  position:absolute;
  left:137px;
  top:30px;
  width:166px;
  height:30px;
  display:flex;
}
#u12981 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12981_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12982_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:152px;
  height:30px;
}
#u12982 {
  border-width:0px;
  position:absolute;
  left:303px;
  top:30px;
  width:152px;
  height:30px;
  display:flex;
}
#u12982 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12982_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12983_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:192px;
  height:30px;
}
#u12983 {
  border-width:0px;
  position:absolute;
  left:455px;
  top:30px;
  width:192px;
  height:30px;
  display:flex;
}
#u12983 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12983_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12984_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:137px;
  height:30px;
}
#u12984 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:60px;
  width:137px;
  height:30px;
  display:flex;
}
#u12984 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12984_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12985_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:166px;
  height:30px;
}
#u12985 {
  border-width:0px;
  position:absolute;
  left:137px;
  top:60px;
  width:166px;
  height:30px;
  display:flex;
}
#u12985 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12985_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12986_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:152px;
  height:30px;
}
#u12986 {
  border-width:0px;
  position:absolute;
  left:303px;
  top:60px;
  width:152px;
  height:30px;
  display:flex;
}
#u12986 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12986_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12987_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:192px;
  height:30px;
}
#u12987 {
  border-width:0px;
  position:absolute;
  left:455px;
  top:60px;
  width:192px;
  height:30px;
  display:flex;
}
#u12987 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12987_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12988_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:128px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12988 {
  border-width:0px;
  position:absolute;
  left:54px;
  top:122px;
  width:128px;
  height:25px;
  display:flex;
}
#u12988 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12988_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12989_input {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u12989_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u12989_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#AAAAAA;
}
#u12989 {
  border-width:0px;
  position:absolute;
  left:512px;
  top:132px;
  width:200px;
  height:25px;
  display:flex;
  font-size:10px;
  color:#AAAAAA;
}
#u12989 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12989_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:200px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#AAAAAA;
}
#u12989.disabled {
}
#u12990_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:18px;
  height:19px;
}
#u12990 {
  border-width:0px;
  position:absolute;
  left:515px;
  top:137px;
  width:18px;
  height:19px;
  display:flex;
}
#u12990 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12990_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12942_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:786px;
  height:830px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u12942_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u12991_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:703px;
  height:815px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12991 {
  border-width:0px;
  position:absolute;
  left:-1px;
  top:3px;
  width:703px;
  height:815px;
  display:flex;
}
#u12991 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12991_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12992_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:705px;
  height:35px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
}
#u12992 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:4px;
  width:705px;
  height:35px;
  display:flex;
  font-size:16px;
  color:#FFFFFF;
  text-align:left;
}
#u12992 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12992_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12993_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:90px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12993 {
  border-width:0px;
  position:absolute;
  left:84px;
  top:58px;
  width:90px;
  height:25px;
  display:flex;
}
#u12993 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12993_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u12994_input {
  position:absolute;
  left:0px;
  top:0px;
  width:393px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u12994_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:393px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u12994_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:393px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u12994 {
  border-width:0px;
  position:absolute;
  left:174px;
  top:58px;
  width:393px;
  height:25px;
  display:flex;
  color:#AAAAAA;
}
#u12994 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12994_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:393px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u12994.disabled {
}
#u12995_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:16px;
  height:16px;
}
#u12995 {
  border-width:0px;
  position:absolute;
  left:679px;
  top:14px;
  width:16px;
  height:16px;
  display:flex;
}
#u12995 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12995_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u12996_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:28px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12996 {
  border-width:0px;
  position:absolute;
  left:167px;
  top:760px;
  width:60px;
  height:28px;
  display:flex;
}
#u12996 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12996_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12997_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#7F7F7F;
}
#u12997 {
  border-width:0px;
  position:absolute;
  left:277px;
  top:760px;
  width:60px;
  height:28px;
  display:flex;
  color:#7F7F7F;
}
#u12997 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12997_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12998_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:60px;
  height:28px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#7F7F7F;
}
#u12998 {
  border-width:0px;
  position:absolute;
  left:403px;
  top:760px;
  width:60px;
  height:28px;
  display:flex;
  color:#7F7F7F;
}
#u12998 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u12998_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u12999_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u12999 {
  border-width:0px;
  position:absolute;
  left:70px;
  top:94px;
  width:104px;
  height:25px;
  display:flex;
}
#u12999 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u12999_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13000 label {
  left:0px;
  width:100%;
}
#u13000_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u13000 {
  border-width:0px;
  position:absolute;
  left:189px;
  top:94px;
  width:100px;
  height:25px;
  display:flex;
}
#u13000 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u13000_img.selected {
}
#u13000.selected {
}
#u13000_img.disabled {
}
#u13000.disabled {
}
#u13000_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u13000_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u13001 label {
  left:0px;
  width:100%;
}
#u13001_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u13001 {
  border-width:0px;
  position:absolute;
  left:326px;
  top:94px;
  width:139px;
  height:25px;
  display:flex;
}
#u13001 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u13001_img.selected {
}
#u13001.selected {
}
#u13001_img.disabled {
}
#u13001.disabled {
}
#u13001_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:123px;
  word-wrap:break-word;
  text-transform:none;
}
#u13001_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u13002_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:96px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u13002 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:131px;
  width:96px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
}
#u13002 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13002_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13003 {
  border-width:0px;
  position:absolute;
  left:105px;
  top:301px;
  width:584px;
  height:219px;
}
#u13003_state0 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:584px;
  height:219px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13003_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u13004_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13004 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:94px;
  height:25px;
  display:flex;
}
#u13004 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13004_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13005_input {
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#555555;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u13005_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#555555;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u13005_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u13005 {
  border-width:0px;
  position:absolute;
  left:119px;
  top:0px;
  width:385px;
  height:25px;
  display:flex;
  color:#555555;
}
#u13005 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13005_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#555555;
}
#u13005.disabled {
}
.u13005_input_option {
  color:#555555;
}
#u13006 {
  position:absolute;
  left:0px;
  top:36px;
}
#u13006_state0 {
  position:relative;
  left:0px;
  top:0px;
  width:499px;
  height:183px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13006_state0_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u13007_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:52px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13007 {
  border-width:0px;
  position:absolute;
  left:44px;
  top:86px;
  width:52px;
  height:25px;
  display:flex;
}
#u13007 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13007_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13008_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13008 {
  border-width:0px;
  position:absolute;
  left:58px;
  top:123px;
  width:38px;
  height:25px;
  display:flex;
}
#u13008 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13008_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13009_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13009 {
  border-width:0px;
  position:absolute;
  left:45px;
  top:5px;
  width:51px;
  height:25px;
  display:flex;
}
#u13009 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13009_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13010_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u13010 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:86px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13010 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13010_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13011_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u13011 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:123px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13011 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13011_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13012_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u13012 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:5px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13012 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13012_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13013_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13013 {
  border-width:0px;
  position:absolute;
  left:58px;
  top:42px;
  width:38px;
  height:25px;
  display:flex;
}
#u13013 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13013_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13014_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u13014 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:42px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13014 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13014_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13015 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u13016_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
}
#u13016 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:159px;
  width:385px;
  height:24px;
  display:flex;
  font-size:10px;
}
#u13016 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13016_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(1, 119, 255, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
}
#u13016.selected {
}
#u13016_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(1, 119, 255, 1);
  border-radius:2px;
  -moz-box-shadow:0px 0px 3px rgba(1, 119, 255, 1);
  -webkit-box-shadow:0px 0px 3px rgba(1, 119, 255, 1);
  box-shadow:0px 0px 3px rgba(1, 119, 255, 1);
  font-size:10px;
}
#u13016.disabled {
}
#u13016_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u13017_input {
  position:absolute;
  left:0px;
  top:0px;
  width:183px;
  height:16px;
  padding:3px 2px 3px 2px;
  font-family:'Microsoft YaHei Regular', 'Microsoft YaHei';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u13017_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:183px;
  height:16px;
  padding:3px 2px 3px 2px;
  font-family:'Microsoft YaHei Regular', 'Microsoft YaHei';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u13017_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:183px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#D7D7D7;
}
#u13017 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:162px;
  width:183px;
  height:16px;
  display:flex;
  font-size:10px;
  color:#D7D7D7;
}
#u13017 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13017_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:183px;
  height:16px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#D7D7D7;
}
#u13017.disabled {
}
#u13018_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:15px;
}
#u13018 {
  border-width:0px;
  position:absolute;
  left:321px;
  top:162px;
  width:11px;
  height:15px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  font-size:10px;
}
#u13018 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13018_img.selected {
}
#u13018.selected {
}
#u13018_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u13019 {
  border-width:0px;
  position:absolute;
  left:310px;
  top:165px;
  width:24px;
  height:11px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u13020_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u13020 {
  border-width:0px;
  position:absolute;
  left:20px;
  top:157px;
  width:82px;
  height:25px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u13020 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13020_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13006_state1 {
  position:relative;
  left:0px;
  top:0px;
  width:499px;
  height:254px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u13006_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u13021_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13021 {
  border-width:0px;
  position:absolute;
  left:42px;
  top:121px;
  width:50px;
  height:25px;
  display:flex;
}
#u13021 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13021_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13022_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13022 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:158px;
  width:36px;
  height:25px;
  display:flex;
}
#u13022 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13022_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13023_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13023 {
  border-width:0px;
  position:absolute;
  left:43px;
  top:40px;
  width:51px;
  height:25px;
  display:flex;
}
#u13023 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13023_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13024_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u13024 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:120px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13024 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13024_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13025_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u13025 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:157px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13025 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13025_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13026_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u13026 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:39px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13026 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13026_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13027_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13027 {
  border-width:0px;
  position:absolute;
  left:56px;
  top:77px;
  width:38px;
  height:25px;
  display:flex;
}
#u13027 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13027_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13028_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u13028 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:76px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13028 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13028_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13029_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13029 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:1px;
  width:66px;
  height:25px;
  display:flex;
}
#u13029 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13029_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13030_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u13030 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:0px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13030 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13030_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13031 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u13032_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
}
#u13032 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:229px;
  width:385px;
  height:24px;
  display:flex;
  font-size:10px;
}
#u13032 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13032_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(1, 119, 255, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
}
#u13032.selected {
}
#u13032_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(1, 119, 255, 1);
  border-radius:2px;
  -moz-box-shadow:0px 0px 3px rgba(1, 119, 255, 1);
  -webkit-box-shadow:0px 0px 3px rgba(1, 119, 255, 1);
  box-shadow:0px 0px 3px rgba(1, 119, 255, 1);
  font-size:10px;
}
#u13032.disabled {
}
#u13032_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u13033_input {
  position:absolute;
  left:0px;
  top:0px;
  width:183px;
  height:16px;
  padding:3px 2px 3px 2px;
  font-family:'Microsoft YaHei Regular', 'Microsoft YaHei';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u13033_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:183px;
  height:16px;
  padding:3px 2px 3px 2px;
  font-family:'Microsoft YaHei Regular', 'Microsoft YaHei';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u13033_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:183px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#D7D7D7;
}
#u13033 {
  border-width:0px;
  position:absolute;
  left:120px;
  top:232px;
  width:183px;
  height:16px;
  display:flex;
  font-size:10px;
  color:#D7D7D7;
}
#u13033 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13033_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:183px;
  height:16px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#D7D7D7;
}
#u13033.disabled {
}
#u13034_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:15px;
}
#u13034 {
  border-width:0px;
  position:absolute;
  left:321px;
  top:232px;
  width:11px;
  height:15px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  font-size:10px;
}
#u13034 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13034_img.selected {
}
#u13034.selected {
}
#u13034_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u13035 {
  border-width:0px;
  position:absolute;
  left:310px;
  top:235px;
  width:24px;
  height:11px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u13036_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u13036 {
  border-width:0px;
  position:absolute;
  left:18px;
  top:229px;
  width:82px;
  height:25px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u13036 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13036_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13037_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13037 {
  border-width:0px;
  position:absolute;
  left:12px;
  top:193px;
  width:80px;
  height:25px;
  display:flex;
}
#u13037 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13037_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13038_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u13038 {
  border-width:0px;
  position:absolute;
  left:114px;
  top:192px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13038 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13038_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13006_state2 {
  position:relative;
  left:0px;
  top:0px;
  width:650px;
  height:292px;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u13006_state2_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u13039_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:50px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13039 {
  border-width:0px;
  position:absolute;
  left:52px;
  top:159px;
  width:50px;
  height:25px;
  display:flex;
}
#u13039 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13039_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13040_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:36px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13040 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:196px;
  width:36px;
  height:25px;
  display:flex;
}
#u13040 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13040_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13041_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:51px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13041 {
  border-width:0px;
  position:absolute;
  left:53px;
  top:78px;
  width:51px;
  height:25px;
  display:flex;
}
#u13041 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13041_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13042_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u13042 {
  border-width:0px;
  position:absolute;
  left:112px;
  top:159px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13042 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13042_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13043_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u13043 {
  border-width:0px;
  position:absolute;
  left:112px;
  top:196px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13043 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13043_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13044_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u13044 {
  border-width:0px;
  position:absolute;
  left:112px;
  top:78px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13044 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13044_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13045_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:38px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13045 {
  border-width:0px;
  position:absolute;
  left:66px;
  top:115px;
  width:38px;
  height:25px;
  display:flex;
}
#u13045 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13045_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13046_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u13046 {
  border-width:0px;
  position:absolute;
  left:112px;
  top:115px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13046 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13046_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13047_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:66px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13047 {
  border-width:0px;
  position:absolute;
  left:38px;
  top:39px;
  width:66px;
  height:25px;
  display:flex;
}
#u13047 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13047_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13048_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u13048 {
  border-width:0px;
  position:absolute;
  left:112px;
  top:39px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13048 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13048_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13049 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u13050_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:373px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
}
#u13050 {
  border-width:0px;
  position:absolute;
  left:124px;
  top:267px;
  width:373px;
  height:24px;
  display:flex;
  font-size:10px;
}
#u13050 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13050_div.selected {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:373px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(1, 119, 255, 1);
  border-radius:2px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
}
#u13050.selected {
}
#u13050_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:373px;
  height:24px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(1, 119, 255, 1);
  border-radius:2px;
  -moz-box-shadow:0px 0px 3px rgba(1, 119, 255, 1);
  -webkit-box-shadow:0px 0px 3px rgba(1, 119, 255, 1);
  box-shadow:0px 0px 3px rgba(1, 119, 255, 1);
  font-size:10px;
}
#u13050.disabled {
}
#u13050_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u13051_input {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:16px;
  padding:3px 2px 3px 2px;
  font-family:'Microsoft YaHei Regular', 'Microsoft YaHei';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u13051_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:16px;
  padding:3px 2px 3px 2px;
  font-family:'Microsoft YaHei Regular', 'Microsoft YaHei';
  font-weight:400;
  font-style:normal;
  font-size:10px;
  letter-spacing:normal;
  color:#D7D7D7;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u13051_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:16px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#D7D7D7;
}
#u13051 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:270px;
  width:177px;
  height:16px;
  display:flex;
  font-size:10px;
  color:#D7D7D7;
}
#u13051 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13051_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:177px;
  height:16px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:10px;
  color:#D7D7D7;
}
#u13051.disabled {
}
#u13052_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:11px;
  height:15px;
}
#u13052 {
  border-width:0px;
  position:absolute;
  left:324px;
  top:270px;
  width:11px;
  height:15px;
  display:flex;
  -webkit-transform:rotate(90deg);
  -moz-transform:rotate(90deg);
  -ms-transform:rotate(90deg);
  transform:rotate(90deg);
  font-size:10px;
}
#u13052 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13052_img.selected {
}
#u13052.selected {
}
#u13052_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u13053 {
  border-width:0px;
  position:absolute;
  left:314px;
  top:273px;
  width:23px;
  height:11px;
  overflow:hidden;
  background-image:url('../../resources/images/transparent.gif');
}
#u13054_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:82px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u13054 {
  border-width:0px;
  position:absolute;
  left:28px;
  top:267px;
  width:82px;
  height:25px;
  display:flex;
  font-family:'微软雅黑';
  font-weight:400;
  font-style:normal;
  text-align:right;
}
#u13054 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13054_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13055_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13055 {
  border-width:0px;
  position:absolute;
  left:22px;
  top:231px;
  width:80px;
  height:25px;
  display:flex;
}
#u13055 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13055_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13056_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u13056 {
  border-width:0px;
  position:absolute;
  left:112px;
  top:231px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13056 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13056_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13057_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:106px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13057 {
  border-width:0px;
  position:absolute;
  left:6px;
  top:3px;
  width:106px;
  height:25px;
  display:flex;
}
#u13057 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13057_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13058_input {
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#7F7F7F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u13058_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#7F7F7F;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u13058_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#7F7F7F;
}
#u13058 {
  border-width:0px;
  position:absolute;
  left:112px;
  top:3px;
  width:385px;
  height:25px;
  display:flex;
  color:#7F7F7F;
}
#u13058 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13058_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#7F7F7F;
}
#u13058.disabled {
}
.u13058_input_option {
  color:#7F7F7F;
}
#u13059_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u13059 {
  border-width:0px;
  position:absolute;
  left:502px;
  top:232px;
  width:20px;
  height:20px;
  display:flex;
}
#u13059 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13059_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u13060_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:120px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:12px;
}
#u13060 {
  border-width:0px;
  position:absolute;
  left:530px;
  top:230px;
  width:120px;
  height:25px;
  display:flex;
  font-size:12px;
}
#u13060 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13060_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13061_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:80px;
  height:25px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:5px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-size:14px;
  color:#FFFFFF;
}
#u13061 {
  border-width:0px;
  position:absolute;
  left:504px;
  top:158px;
  width:80px;
  height:25px;
  display:flex;
  font-size:14px;
  color:#FFFFFF;
}
#u13061 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13061_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13003_state1 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:584px;
  height:219px;
  -ms-overflow-x:hidden;
  overflow-x:hidden;
  -ms-overflow-y:hidden;
  overflow-y:hidden;
  background-image:none;
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  visibility:hidden;
}
#u13003_state1_content {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:1px;
  height:1px;
}
#u13062 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u13063_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:570px;
  height:144px;
}
#u13063 {
  border-width:0px;
  position:absolute;
  left:7px;
  top:0px;
  width:570px;
  height:144px;
  display:flex;
}
#u13063 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13063_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u13064_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:67px;
  height:54px;
}
#u13064 {
  border-width:0px;
  position:absolute;
  left:250px;
  top:18px;
  width:67px;
  height:54px;
  display:flex;
}
#u13064 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13064_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u13065_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:196px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13065 {
  border-width:0px;
  position:absolute;
  left:202px;
  top:82px;
  width:196px;
  height:25px;
  display:flex;
}
#u13065 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13065_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13066_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:104px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13066 {
  border-width:0px;
  position:absolute;
  left:100px;
  top:186px;
  width:104px;
  height:25px;
  display:flex;
}
#u13066 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13066_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13067_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u13067 {
  border-width:0px;
  position:absolute;
  left:216px;
  top:185px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13067 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13067_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13068_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u13068 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:219px;
  width:70px;
  height:25px;
  display:flex;
  color:#000000;
}
#u13068 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13068_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13069_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u13069 {
  border-width:0px;
  position:absolute;
  left:216px;
  top:219px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13069 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13069_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13070_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u13070 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:263px;
  width:70px;
  height:25px;
  display:flex;
  color:#000000;
}
#u13070 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13070_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13071 label {
  left:0px;
  width:100%;
}
#u13071_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u13071 {
  border-width:0px;
  position:absolute;
  left:216px;
  top:262px;
  width:100px;
  height:25px;
  display:flex;
}
#u13071 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u13071_img.selected {
}
#u13071.selected {
}
#u13071_img.disabled {
}
#u13071.disabled {
}
#u13071_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:84px;
  word-wrap:break-word;
  text-transform:none;
}
#u13071_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u13072 label {
  left:0px;
  width:100%;
}
#u13072_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:7px;
  width:12px;
  height:12px;
}
#u13072 {
  border-width:0px;
  position:absolute;
  left:351px;
  top:263px;
  width:139px;
  height:25px;
  display:flex;
}
#u13072 .text {
  position:absolute;
  align-self:center;
  padding:0px 2px 0px 2px;
  box-sizing:border-box;
}
#u13072_img.selected {
}
#u13072.selected {
}
#u13072_img.disabled {
}
#u13072.disabled {
}
#u13072_text {
  border-width:0px;
  position:absolute;
  left:14px;
  top:0px;
  width:123px;
  word-wrap:break-word;
  text-transform:none;
}
#u13072_input {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
  opacity:0;
}
#u13073_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:112px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#000000;
}
#u13073 {
  border-width:0px;
  position:absolute;
  left:26px;
  top:538px;
  width:112px;
  height:25px;
  display:flex;
  font-family:'Arial Negreta', 'Arial Normal', 'Arial';
  font-weight:700;
  font-style:normal;
  font-size:16px;
  color:#000000;
}
#u13073 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13073_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13074_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:674px;
  height:2px;
}
#u13074 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:157px;
  width:673px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.179374796200804deg);
  -moz-transform:rotate(-0.179374796200804deg);
  -ms-transform:rotate(-0.179374796200804deg);
  transform:rotate(-0.179374796200804deg);
}
#u13074 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13074_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u13075_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:674px;
  height:2px;
}
#u13075 {
  border-width:0px;
  position:absolute;
  left:16px;
  top:572px;
  width:673px;
  height:1px;
  display:flex;
  -webkit-transform:rotate(-0.179374796200804deg);
  -moz-transform:rotate(-0.179374796200804deg);
  -ms-transform:rotate(-0.179374796200804deg);
  transform:rotate(-0.179374796200804deg);
}
#u13075 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13075_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u13076_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:78px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13076 {
  border-width:0px;
  position:absolute;
  left:126px;
  top:587px;
  width:78px;
  height:25px;
  display:flex;
}
#u13076 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13076_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13077_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13077 {
  border-width:0px;
  position:absolute;
  left:130px;
  top:629px;
  width:74px;
  height:25px;
  display:flex;
}
#u13077 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13077_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13078_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:312px;
  height:25px;
}
#u13078 {
  border-width:0px;
  position:absolute;
  left:318px;
  top:629px;
  width:312px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13078 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13078_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13079_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13079 {
  border-width:0px;
  position:absolute;
  left:136px;
  top:670px;
  width:74px;
  height:25px;
  display:flex;
}
#u13079 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13079_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13080_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u13080 {
  border-width:0px;
  position:absolute;
  left:234px;
  top:670px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13080 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13080_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u13081_input {
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u13081_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u13081_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u13081 {
  border-width:0px;
  position:absolute;
  left:234px;
  top:582px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
}
#u13081 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13081_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u13081.disabled {
}
.u13081_input_option {
  color:#AAAAAA;
}
#u13082_input {
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u13082_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u13082_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u13082 {
  border-width:0px;
  position:absolute;
  left:234px;
  top:629px;
  width:84px;
  height:25px;
  display:flex;
  color:#AAAAAA;
}
#u13082 .text {
  position:absolute;
  align-self:flex-start;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13082_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:84px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u13082.disabled {
}
.u13082_input_option {
  color:#AAAAAA;
}
#u13083_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:21px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u13083 {
  border-width:0px;
  position:absolute;
  left:640px;
  top:629px;
  width:21px;
  height:25px;
  display:flex;
  color:#000000;
}
#u13083 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13083_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13084_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:74px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u13084 {
  border-width:0px;
  position:absolute;
  left:134px;
  top:703px;
  width:74px;
  height:25px;
  display:flex;
}
#u13084 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u13084_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u13085_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:385px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
  text-align:left;
}
#u13085 {
  border-width:0px;
  position:absolute;
  left:234px;
  top:703px;
  width:385px;
  height:25px;
  display:flex;
  color:#AAAAAA;
  text-align:left;
}
#u13085 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u13085_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
