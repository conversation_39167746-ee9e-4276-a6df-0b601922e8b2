# 项目问题统计分析工具

## 功能概述

这个Python脚本用于分析Excel文件中的项目问题数据，按项目编号统计各类问题数量，并生成统计报告。

## 主要功能

1. **数据读取与分析**：读取Excel文件中的项目问题数据
2. **统计分析**：按项目编号统计各项指标
3. **报告生成**：生成详细统计报告和汇总表格
4. **数据导出**：支持将结果导出到Excel文件

## 统计指标

### 基础统计
- **问题总数**：每个项目的问题总数量
- **无效**：问题判定中包含"无效"字样的问题数量
- **有效BUG**：问题判定为"有效BUG"的数量
- **非产品BUG**：问题判定为"非产品BUG"的数量
- **其他**：除上述类型外的其他问题数量

### 问题严重性统计
- 统计每个项目中不同严重性级别的问题数量（如：一般、高、低、无等）

### 版本信息
- 每个项目的版本号（如有重复，取第一个值）

## 数据要求

Excel文件必须包含以下列：
- `版本号`
- `问题严重性（产品经理）`
- `问题判定（产品经理）`
- `项目编号`
- `项目阶段`

## 使用方法

### 1. 准备数据
确保Excel文件位于 `rdm/daily/6月问题.xlsx` 路径下，或修改脚本中的文件路径。

### 2. 运行脚本
```bash
python rdm/daily/productMonthReport.py
```

### 3. 选择输出格式
脚本运行后会提示选择输出格式：
- `1` - 详细统计报告：显示每个项目的详细统计信息
- `2` - 汇总表格：显示所有项目的汇总表格
- `3` - 两种都显示：同时显示详细报告和汇总表格
- `4` - 导出到Excel文件：仅导出结果到Excel文件
- `5` - 显示汇总表格并导出Excel：显示汇总表格并导出（默认选项）

## 输出示例

### 汇总表格示例
```
项目编号                                               版本号                  问题总数       无效         有效BUG      非产品BUG     其他
------------------------------------------------------------------------------------------------------------------------------------------------------
3672-F-3438-RMBX-2024-02-8-中国人保财险集团邮件防泄漏扩容采购项目      V542F04             4          3          0          1          0
3288-F-4217-PYGJ-2023-03-73-浦银国际DLP项目              V542                 3          1          1          1          0
```

### 详细统计报告示例
```
【项目编号】: 3672-F-3438-RMBX-2024-02-8-中国人保财险集团邮件防泄漏扩容采购项目
【版本号】:  V542F04
========================================================================================================================
统计项目                      数量
----------------------------------------
问题总数                      4
无效                        3
非产品BUG                    1

问题严重性                     数量
----------------------------------------
无                         3
一般                        1
```

## 输出文件

- **Excel导出文件**：`项目问题统计结果.xlsx`
  - 包含所有项目的统计数据
  - 可用于进一步分析或报告

## 依赖库

- `pandas`：用于数据处理和Excel文件操作
- `openpyxl`：用于Excel文件读写

安装依赖：
```bash
pip install pandas openpyxl
```

## 注意事项

1. 确保Excel文件格式正确，包含所有必需的列
2. 项目编号用作唯一标识符进行分组统计
3. 版本号如有重复，脚本会自动取第一个值
4. "无效"问题的判定基于"问题判定（产品经理）"列是否包含"无效"字样
5. 脚本会自动处理空值和异常数据

## 扩展功能

如需添加其他统计指标或修改输出格式，可以修改以下函数：
- `process_project_data()`：修改统计逻辑
- `print_project_statistics()`：修改详细报告格式
- `create_summary_table()`：修改汇总表格格式
- `export_to_excel()`：修改Excel导出内容

## 作者信息

- 作者：AI Assistant
- 创建日期：2025-07-02
- 版本：1.0
