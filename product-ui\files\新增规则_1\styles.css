﻿body {
  margin:0px;
  background-image:none;
  position:relative;
  left:-202px;
  width:611px;
  margin-left:auto;
  margin-right:auto;
  text-align:left;
}
.form_sketch {
  border-color:transparent;
  background-color:transparent;
}
#base {
  position:absolute;
  z-index:0;
}
#u4682 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4683_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:611px;
  height:484px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(121, 121, 121, 1);
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4683 {
  border-width:0px;
  position:absolute;
  left:202px;
  top:118px;
  width:611px;
  height:484px;
  display:flex;
}
#u4683 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4683_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4684_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:611px;
  height:34px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
  text-align:left;
}
#u4684 {
  border-width:0px;
  position:absolute;
  left:202px;
  top:84px;
  width:611px;
  height:34px;
  display:flex;
  color:#FFFFFF;
  text-align:left;
}
#u4684 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4684_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4685_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:76px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 0);
  border:none;
  border-radius:0px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
}
#u4685 {
  border-width:0px;
  position:absolute;
  left:249px;
  top:146px;
  width:76px;
  height:25px;
  display:flex;
}
#u4685 .text {
  position:absolute;
  align-self:flex-start;
  padding:0px 0px 0px 0px;
  box-sizing:border-box;
  width:100%;
}
#u4685_text {
  border-width:0px;
  white-space:nowrap;
  text-transform:none;
}
#u4686_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:30px;
  background:inherit;
  background-color:rgba(24, 144, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#FFFFFF;
}
#u4686 {
  border-width:0px;
  position:absolute;
  left:729px;
  top:552px;
  width:71px;
  height:30px;
  display:flex;
  color:#FFFFFF;
}
#u4686 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4686_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4687_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u4687 {
  border-width:0px;
  position:absolute;
  left:567px;
  top:552px;
  width:71px;
  height:30px;
  display:flex;
  color:#000000;
}
#u4687 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4687_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4688_input {
  position:absolute;
  left:0px;
  top:0px;
  width:355px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4688_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:355px;
  height:25px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4688_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:355px;
  height:25px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u4688 {
  border-width:0px;
  position:absolute;
  left:339px;
  top:148px;
  width:355px;
  height:25px;
  display:flex;
  color:#AAAAAA;
}
#u4688 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4688_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:355px;
  height:25px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u4688.disabled {
}
#u4689_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:71px;
  height:30px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(215, 215, 215, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#000000;
}
#u4689 {
  border-width:0px;
  position:absolute;
  left:647px;
  top:552px;
  width:71px;
  height:30px;
  display:flex;
  color:#000000;
}
#u4689 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4689_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
}
#u4690_input {
  position:absolute;
  left:0px;
  top:0px;
  width:355px;
  height:108px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4690_input.disabled {
  position:absolute;
  left:0px;
  top:0px;
  width:355px;
  height:108px;
  padding:3px 2px 3px 2px;
  font-family:'Arial Normal', 'Arial';
  font-weight:400;
  font-style:normal;
  font-size:13px;
  letter-spacing:normal;
  color:#AAAAAA;
  vertical-align:none;
  text-align:left;
  text-transform:none;
  background-color:transparent;
  border-color:transparent;
}
#u4690_div {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:355px;
  height:108px;
  background:inherit;
  background-color:rgba(255, 255, 255, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u4690 {
  border-width:0px;
  position:absolute;
  left:339px;
  top:422px;
  width:355px;
  height:108px;
  display:flex;
  color:#AAAAAA;
}
#u4690 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4690_div.disabled {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:355px;
  height:108px;
  background:inherit;
  background-color:rgba(240, 240, 240, 1);
  box-sizing:border-box;
  border-width:1px;
  border-style:solid;
  border-color:rgba(170, 170, 170, 1);
  border-radius:3px;
  -moz-box-shadow:none;
  -webkit-box-shadow:none;
  box-shadow:none;
  color:#AAAAAA;
}
#u4690.disabled {
}
#u4691 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4692_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:70px;
}
#u4692 {
  border-width:0px;
  position:absolute;
  left:348px;
  top:441px;
  width:70px;
  height:70px;
  display:flex;
}
#u4692 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4692_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4693_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u4693 {
  border-width:0px;
  position:absolute;
  left:406px;
  top:434px;
  width:20px;
  height:20px;
  display:flex;
}
#u4693 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4693_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4694 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4695_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:70px;
}
#u4695 {
  border-width:0px;
  position:absolute;
  left:475px;
  top:441px;
  width:70px;
  height:70px;
  display:flex;
}
#u4695 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4695_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4696_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u4696 {
  border-width:0px;
  position:absolute;
  left:533px;
  top:434px;
  width:20px;
  height:20px;
  display:flex;
}
#u4696 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4696_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4697 {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:0px;
  height:0px;
}
#u4698_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:70px;
  height:70px;
}
#u4698 {
  border-width:0px;
  position:absolute;
  left:587px;
  top:441px;
  width:70px;
  height:70px;
  display:flex;
}
#u4698 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4698_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4699_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:20px;
  height:20px;
}
#u4699 {
  border-width:0px;
  position:absolute;
  left:645px;
  top:434px;
  width:20px;
  height:20px;
  display:flex;
}
#u4699 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4699_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
#u4700_img {
  border-width:0px;
  position:absolute;
  left:0px;
  top:0px;
  width:432px;
  height:198px;
}
#u4700 {
  border-width:0px;
  position:absolute;
  left:330px;
  top:190px;
  width:432px;
  height:198px;
  display:flex;
}
#u4700 .text {
  position:absolute;
  align-self:center;
  padding:2px 2px 2px 2px;
  box-sizing:border-box;
  width:100%;
}
#u4700_text {
  border-width:0px;
  word-wrap:break-word;
  text-transform:none;
  visibility:hidden;
}
