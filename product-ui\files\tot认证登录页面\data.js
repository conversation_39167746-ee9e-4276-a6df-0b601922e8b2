﻿$axure.loadCurrentPage(
(function() {
    var _ = function() { var r={},a=arguments; for(var i=0; i<a.length; i+=2) r[a[i]]=a[i+1]; return r; }
    var _creator = function() { return _(b,c,d,e,f,_(g,h,i,_(j,k,l,k)),m,[],n,_(h,o),p,[q,r,s,t,u],v,_(w,x,y,z,g,A,B,_(),C,[],D,_(E,F,G,H,I,_(J,K,L,M),N,null,O,P,P,Q,R,S,null,T,U,V,W,X,Y,Z,U,ba,bb,_(J,K,L,bc),bd,U,be,bf,_(bg,bh,bi,bj,bk,bj,bl,bj,L,_(bm,bn,bo,bn,bp,bn,bq,br)),i,_(j,k,l,k)),bs,_(),bt,_(),bu,_(bv,[_(bw,bx,by,h,bz,bA,y,bB,bC,bB,bD,bE,D,_(E,bF,i,_(j,bG,l,bH),N,null),bs,_(),bI,_(),bJ,_(bK,bL)),_(bw,bM,by,h,bz,bN,y,bO,bC,bO,bD,bE,D,_(),bs,_(),bI,_(),bP,[_(bw,bQ,by,h,bz,bA,y,bB,bC,bB,bD,bE,D,_(E,bF,i,_(j,bR,l,bS),bT,_(bU,bV,bW,bX),N,null),bs,_(),bI,_(),bJ,_(bK,bY)),_(bw,bZ,by,h,bz,ca,y,cb,bC,cb,bD,bE,D,_(X,cc,cd,ce,cf,_(J,K,L,cg,ch,ci),i,_(j,cj,l,bS),E,ck,bT,_(bU,cl,bW,bX)),bs,_(),bI,_(),cm,bh),_(bw,cn,by,h,bz,bA,y,bB,bC,bB,bD,bE,D,_(E,bF,i,_(j,co,l,cp),bT,_(bU,bV,bW,cq),N,null),bs,_(),bI,_(),bJ,_(bK,cr))],cs,bh)])),ct,_(),cu,_(cv,_(cw,cx),cy,_(cw,cz),cA,_(cw,cB),cC,_(cw,cD),cE,_(cw,cF)));}; 
var b="url",c="tot认证登录页面.html",d="generationDate",e=new Date(1747988951184.83),f="defaultAdaptiveView",g="name",h="",i="size",j="width",k=0,l="height",m="adaptiveViews",n="sketchKeys",o="s0",p="variables",q="OnLoadVariable",r="currentTime",s="huanmanxielou",t="Checkbox_2",u="Checkbox_3",v="page",w="packageId",x="********************************",y="type",z="Axure:Page",A="TOT认证登录页面",B="notes",C="annotations",D="style",E="baseStyle",F="627587b6038d43cca051c114ac41ad32",G="pageAlignment",H="center",I="fill",J="fillType",K="solid",L="color",M=0xFFFFFFFF,N="image",O="imageAlignment",P="near",Q="imageRepeat",R="auto",S="favicon",T="sketchFactor",U="0",V="colorStyle",W="appliedColor",X="fontName",Y="Applied Font",Z="borderWidth",ba="borderVisibility",bb="borderFill",bc=0xFF797979,bd="cornerRadius",be="cornerVisibility",bf="outerShadow",bg="on",bh=false,bi="offsetX",bj=5,bk="offsetY",bl="blurRadius",bm="r",bn=0,bo="g",bp="b",bq="a",br=0.349019607843137,bs="adaptiveStyles",bt="interactionMap",bu="diagram",bv="objects",bw="id",bx="54bc60d74d714b4b962729c7a3839639",by="label",bz="friendlyType",bA="图片 ",bB="imageBox",bC="styleType",bD="visible",bE=true,bF="75a91ee5b9d042cfa01b8d565fe289c0",bG=1896,bH=911,bI="imageOverrides",bJ="images",bK="normal~",bL="images/tot认证登录页面/u13643.png",bM="268883f580f5453690921876b8b57c62",bN="组合",bO="layer",bP="objs",bQ="6781d435eac14aac8ce5dd0a55d1301d",bR=133,bS=122,bT="location",bU="x",bV=675,bW="y",bX=269,bY="images/tot认证登录页面/u13645.png",bZ="37d3b77ee1f6496889d2cabafad330a0",ca="矩形",cb="vectorShape",cc="'黑体'",cd="fontWeight",ce="400",cf="foreGroundFill",cg=0xFF0B0B0B,ch="opacity",ci=1,cj=393,ck="2285372321d148ec80932747449c36c9",cl=819,cm="generateCompound",cn="68f48cefdceb4fddae421da39686f57d",co=480,cp=159,cq=409,cr="images/tot认证登录页面/u13647.png",cs="propagate",ct="masters",cu="objectPaths",cv="54bc60d74d714b4b962729c7a3839639",cw="scriptId",cx="u13643",cy="268883f580f5453690921876b8b57c62",cz="u13644",cA="6781d435eac14aac8ce5dd0a55d1301d",cB="u13645",cC="37d3b77ee1f6496889d2cabafad330a0",cD="u13646",cE="68f48cefdceb4fddae421da39686f57d",cF="u13647";
return _creator();
})());